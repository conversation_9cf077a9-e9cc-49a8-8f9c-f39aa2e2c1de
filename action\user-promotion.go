package action

import (
	"log"
	"time"

	"gitlab.com/buymed.th/marketplace/promotion/utils"

	"github.com/google/uuid"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/buymed.th/marketplace/promotion/client"
	"gitlab.com/buymed.th/marketplace/promotion/model"
	"gitlab.com/buymed.th/marketplace/promotion/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// UserPromotionList user code list
func UserPromotionList(query model.UserPromotion, offset, limit int64, getTotal bool) *common.APIResponse {
	filter := bson.M{}
	if query.Status != nil && *query.Status != "" {
		filter["status"] = *query.Status
	}

	if query.CustomerID > 0 {
		filter["customer_id"] = query.CustomerID
	}

	result := model.UserPromotionDB.Query(
		filter,
		offset,
		limit,
		&primitive.M{"_id": -1})
	if getTotal {
		countResult := model.PromotionDB.Count(filter)
		result.Total = countResult.Total
	}
	return result
}

// CollectVoucher user collect voucher
/*func CollectVoucher(CustomerID int64, VoucherCode string) *common.APIResponse {
	// Verify voucher code
	vcCodeResult := model.PromotionDB.QueryOne(model.Promotion{
		PromotionCode: VoucherCode,
		Status:        &enum.PromotionStatus.ACTIVE,
	})

	if vcCodeResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    vcCodeResult.Status,
			Message:   "Mã giảm giá hết hạn hoặc không tồn tại.",
			ErrorCode: "VOUCHER_CODE_INVALID",
		}
	}

	// Kiểm tra voucher
	userVoucher := model.UserPromotionDB.Count(model.UserPromotion{
		CustomerID:  CustomerID,
		VoucherCode: VoucherCode,
	})

	if userVoucher.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    userVoucher.Status,
			Message:   "Ui có lỗi rồi. Bạn vui lòng thử lại nhé!.",
			ErrorCode: "CHECK_VOUCHER_CODE_ERROR",
		}
	}

	// Kiểm tra số lượt sưu tập mã
	promoDetail := vcCodeResult.Data.(model.Promotion)
	if promoDetail.TotalCode <= promoDetail.ToTalCollect {
		return &common.APIResponse{
			Status:    userVoucher.Status,
			Message:   "Mã khuyến mãi đã hết số lượt sưu tập.",
			ErrorCode: "CHECK_VOUCHER_CODE_ERROR",
		}
	}

	// Kiểm tra số lượt tối đa user được sử dụng mã
	if vcCodeResult.Total >= promoDetail.ApplyPerUser {
		return &common.APIResponse{
			Status:    common.APIStatus.Existed,
			Message:   "Mã khuyến mãi đã tồn tại trong ví voucher của bạn.",
			ErrorCode: "USER_VOUCHER_EXISTED",
		}
	}

	// Update Total Collect
	now := time.Now()
	updateInfo := bson.M{
		"last_updated_time": &now,
	}
	if promoDetail.TotalCode == (promoDetail.ToTalCollect + 1) {
		updateInfo["status"] = &enum.PromotionStatus.FULL
	}

	updateCollectNumber := model.PromotionDB.UpdateOne(
		model.Promotion{
			PromotionID: promoDetail.PromotionID,
		},
		bson.M{
			"$set": updateInfo,
			"$inc": bson.M{"total_collect": 1}},
	)

	if updateCollectNumber.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   "Sưu tập mã khuyến mãi không thành công. Xin vui lòng thử lại.",
			ErrorCode: "COLLECT_VOUCHER_ERROR",
		}
	}

	// Add voucher user
	result := model.UserPromotionDB.Create(&model.UserPromotion{
		PromotionID:   promoDetail.PromotionID,
		VoucherCode:   VoucherCode,
		CustomerID:    CustomerID,
		CollectedTime: &now,
		Status:        &enum.CodeStatus.COLLECTED,
		CreatedTime:   &now,
		CreatedBy:     CustomerID,
		VersionNo:     uuid.New().String(),
	})

	if result.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   "Sưu tập mã khuyến mãi không thành công. Xin vui lòng thử lại.",
			ErrorCode: "COLLECT_VOUCHER_ERROR",
		}
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Mã giảm giá đã được lưu thành công vào ví của bạn.",
	}
}*/

// UseVoucher user use a voucher
func UseVoucher(accountID, orderID int64, voucherCodes []string) *common.APIResponse {
	customerInfo, err := client.Services.Customer.GetCustomerByAccountID(accountID)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Forbidden,
			Message:   "Tài khoản không xác định",
			ErrorCode: "CUSTOMER_NOT_FOUND",
		}
	}
	customerID := customerInfo.CustomerID

	qVoucher := model.VoucherDB.Query(model.Voucher{
		ComplexQuery: []*bson.M{
			{
				"code": bson.M{"$in": voucherCodes},
			},
		},
	}, 0, 0, nil)
	if qVoucher.Status != common.APIStatus.Ok {
		return qVoucher
	}
	qUsed := model.UserPromotionDB.Query(model.UserPromotion{
		ComplexQuery: []*bson.M{
			{
				"voucher_code": bson.M{"$in": voucherCodes},
			},
		},
		CustomerID: customerID,
	}, 0, 0, nil)
	mapUsed := make(map[string]*model.UserPromotion)
	if qUsed.Status == common.APIStatus.Ok {
		for _, used := range qUsed.Data.([]*model.UserPromotion) {
			mapUsed[used.VoucherCode] = used
		}
	}
	vouchers := qVoucher.Data.([]*model.Voucher)
	for _, voucher := range vouchers {
		now := time.Now()
		newVersionNo := uuid.New().String()
		userPromotion := mapUsed[voucher.Code]
		if userPromotion != nil {
			curOrderIds := make([]int64, 0)
			newOrderIds := make([]int64, 0)
			if userPromotion.OrderIDs != nil {
				curOrderIds = *userPromotion.OrderIDs
			}
			currentVersionNo := userPromotion.VersionNo
			newOrderIds = append(curOrderIds, orderID)
			userPromotion.OrderIDs = &newOrderIds
			if userPromotion.Amount == nil {
				userPromotion.Amount = utils.ParseInt64ToPointer(1)
			} else {
				userPromotion.Amount = utils.ParseInt64ToPointer(*userPromotion.Amount + 1)
			}
			userPromotion.UsedTime = &now
			userPromotion.Status = &enum.CodeStatus.USED
			userPromotion.LastUpdatedTime = &now
			userPromotion.UpdatedBy = customerID
			userPromotion.VersionNo = newVersionNo

			result := model.UserPromotionDB.UpdateOne(&model.UserPromotion{
				ID:        userPromotion.ID,
				VersionNo: currentVersionNo,
			}, userPromotion)

			if result.Status != common.APIStatus.Ok {
				return result
			}

		} else {
			result := model.UserPromotionDB.Create(&model.UserPromotion{
				PromotionID: voucher.PromotionID,
				VoucherCode: voucher.Code,
				CustomerID:  customerID,
				OrderIDs:    &[]int64{orderID},
				Status:      &enum.CodeStatus.USED,
				CreatedTime: &now,
				UsedTime:    &now,
				CreatedBy:   customerID,
				VersionNo:   newVersionNo,
				Amount:      utils.ParseInt64ToPointer(1),
			})

			if result.Status != common.APIStatus.Ok {
				return result
			}
		}
		// update voucher status
		go func(voucher *model.Voucher) {
			updater := model.Voucher{
				UsageTotal: func() *int64 {
					if voucher.UsageTotal != nil {
						return utils.ParseInt64ToPointer(*voucher.UsageTotal + 1)
					}
					return utils.ParseInt64ToPointer(1)
				}(),
			}
			if voucher.MaxUsage != nil && *voucher.MaxUsage > 0 && *updater.UsageTotal == *voucher.MaxUsage {
				updater.Status = &enum.VoucherStatus.HIDE
			}
			model.VoucherDB.UpdateOne(model.Voucher{Code: voucher.Code}, updater)
			WarmUpVoucherByCode(voucher.Code, nil)
			WarmUpUserPromotion(voucher.Code, customerID, nil)
			createVoucherHistory(&model.VoucherHistory{
				CustomerID: customerID,
				OrderID:    orderID,
				Usage:      1,
				Type:       enum.VoucherHistoryType.USE,
				Voucher:    voucher,
				Promotion:  nil,
			}, 0, 0, voucher.Code)
		}(voucher)
	}

	return qVoucher
}

// GetListUserVoucher is func to get list user voucher
func GetListUserVoucher(query *model.UserPromotion, offset, limit int64, getTotal bool) *common.APIResponse {
	result := model.UserPromotionDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if getTotal {
		countResult := model.UserPromotionDB.Count(query)
		result.Total = countResult.Total
	}
	return result
}

// CreateManyUserVoucher
func CreateManyUserVoucher(acc *model.Account, input *model.CreateManyUserVoucherRequest) *common.APIResponse {
	if input.UserPromotions == nil || len(input.UserPromotions) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Danh sách voucher không được trống",
			ErrorCode: "PAYLOAD_INVALID",
		}
	}
	for _, userPromotion := range input.UserPromotions {
		// CreateUserVoucher
		userVoucher := &model.UserPromotion{
			VoucherCode: userPromotion.VoucherCode,
			CustomerIDs: userPromotion.CustomerIDs,
			Status:      &enum.CodeStatus.ACTIVE,
		}
		log.Println("ABC")

		resp := CreateUserVoucher(acc, userVoucher)
		if resp.Status != common.APIStatus.Ok {
			log.Println("resp", resp)
			return resp
		}
	}
	return &common.APIResponse{
		Status: common.APIStatus.Ok,
	}
}

func UpdateManyUserVoucher(acc *model.Account, input *model.UpdateManyUserVoucherRequest) *common.APIResponse {
	if input.Vouchers == nil || len(input.Vouchers) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Danh sách voucher không được trống",
			ErrorCode: "PAYLOAD_INVALID",
		}
	}
	for _, voucher := range input.Vouchers {
		getVoucherResult := model.VoucherDB.QueryOne(
			bson.M{
				"code": voucher.Code,
			},
		)

		// Validate Code
		if getVoucherResult.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Mã khuyến mãi không tồn tại.",
				ErrorCode: "CODE_INVALID",
			}
		}

		existVoucher := getVoucherResult.Data.([]*model.Voucher)[0]
		existVoucher.VersionNo = uuid.New().String()
		updater := bson.M{
			"version_no": existVoucher.VersionNo,
		}

		if voucher.DisplayName != "" {
			existVoucher.DisplayName = voucher.DisplayName
			updater["display_name"] = existVoucher.DisplayName
		}

		// end time
		{
			if input.EndTime != nil {
				existVoucher.EndTime = input.EndTime
				updater["end_time"] = existVoucher.EndTime
			}
			if voucher.EndTime != nil {
				existVoucher.EndTime = voucher.EndTime
				updater["end_time"] = existVoucher.EndTime
			}
		}

		// status
		{
			if input.Status != nil {
				existVoucher.Status = input.Status
				updater["status"] = existVoucher.Status
			}
			if voucher.Status != nil {
				existVoucher.Status = voucher.Status
				updater["status"] = existVoucher.Status
			}
		}

		{
			if voucher.AndConditions != nil {
				existVoucher.AndConditions[enum.ConditionType.ORDER_VALUE].OrConditions[0].OrderConditionField.MinTotalPrice = voucher.AndConditions[enum.ConditionType.ORDER_VALUE].OrConditions[0].OrderConditionField.MinTotalPrice
				updater["and_conditions"] = existVoucher.AndConditions
			}
		}

		afterOption := options.After
		updateResult := model.VoucherDB.UpdateOneWithOption(
			bson.M{
				"voucher_id": existVoucher.VoucherID,
			},
			bson.M{
				"$set": updater,
			},
			&options.FindOneAndUpdateOptions{
				ReturnDocument: &afterOption,
			},
		)

		if updateResult.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:    common.APIStatus.Error,
				Message:   "Cập nhật mã khuyến mãi không thành công!",
				ErrorCode: "SERVER_ERROR",
			}
		}

		newVoucher := updateResult.Data.([]*model.Voucher)[0]

		go func(voucher *model.Voucher) {
			WarmUpVoucherByCode(voucher.Code, voucher)
			WarmUpUserPromotion(voucher.Code, 0, nil)
		}(newVoucher)
	}
	return &common.APIResponse{
		Status: common.APIStatus.Ok,
	}
}

// CreateUserVoucher is func to add customer to voucher
func CreateUserVoucher(acc *model.Account, userVoucher *model.UserPromotion) *common.APIResponse {
	qVoucher := model.VoucherDB.QueryOne(model.Voucher{
		Code: userVoucher.VoucherCode,
	})
	if qVoucher.Status != common.APIStatus.Ok {
		return qVoucher
	}
	defer func() {
		go WarmUpVoucherByCode(userVoucher.VoucherCode, nil)
		go WarmUpUserPromotion(userVoucher.VoucherCode, 0, nil)
	}()
	voucher := qVoucher.Data.([]*model.Voucher)[0]

	if userVoucher.Status == nil {
		userVoucher.Status = &enum.CodeStatus.ACTIVE
	}
	if len(userVoucher.CustomerIDs) > 0 {
		query := model.UserPromotion{
			VoucherCode: voucher.Code,
			ComplexQuery: []*bson.M{
				{
					"customer_id": bson.M{"$in": userVoucher.CustomerIDs},
				},
			},
		}

		existedCustomerIds := make([]int64, 0)
		qUserVoucher := model.UserPromotionDB.Query(query, 0, 0, nil)
		if qUserVoucher.Status == common.APIStatus.Ok {
			userVoucherList := qUserVoucher.Data.([]*model.UserPromotion)
			invalidUsers := make([]*model.UserPromotion, 0)
			ids := make([]int64, 0)
			usedList := make([]int64, 0)

			for _, userVoucherItem := range userVoucherList {
				existedCustomerIds = append(existedCustomerIds, userVoucherItem.CustomerID)
				if userVoucherItem.Status != nil && userVoucher.Status != nil {
					if *userVoucherItem.Status == *userVoucher.Status || (*userVoucherItem.Status == enum.CodeStatus.USED && *userVoucher.Status == enum.CodeStatus.ACTIVE) {
						invalidUsers = append(invalidUsers, userVoucherItem)
						continue
					}

					if *userVoucher.Status == enum.CodeStatus.ACTIVE &&
						(*userVoucherItem.Status == enum.CodeStatus.DELETED || *userVoucherItem.Status == enum.CodeStatus.INACTIVE) && userVoucherItem.Amount != nil && *userVoucherItem.Amount > 0 {
						usedList = append(usedList, userVoucherItem.CustomerID)
						continue
					}
				}
				ids = append(ids, userVoucherItem.CustomerID)
			}

			if len(invalidUsers) > 0 {
				return &common.APIResponse{
					Status:    common.APIStatus.Existed,
					Data:      invalidUsers,
					Message:   "Customer added voucher",
					ErrorCode: "EXISTED",
				}
			}

			if len(usedList) > 0 {
				_ = model.UserPromotionDB.UpdateMany(&model.UserPromotion{
					VoucherCode: voucher.Code,
					ComplexQuery: []*bson.M{
						{
							"customer_id": bson.M{"$in": usedList},
						},
					},
				}, &model.UserPromotion{
					Status: &enum.CodeStatus.USED,
				})
			}

			if len(ids) > 0 {
				_ = model.UserPromotionDB.UpdateMany(&model.UserPromotion{
					VoucherCode: voucher.Code,
					ComplexQuery: []*bson.M{
						{
							"customer_id": bson.M{"$in": ids},
						},
					},
				}, &model.UserPromotion{
					Status: userVoucher.Status,
				})
			}

			if len(existedCustomerIds) == len(userVoucher.CustomerIDs) {
				return &common.APIResponse{
					Status:  common.APIStatus.Ok,
					Message: "Ok",
				}
			}
		}

		userVouchers := make([]*model.UserPromotion, 0)
		for _, customerID := range userVoucher.CustomerIDs {
			isExisted := false
			for _, existedID := range existedCustomerIds {
				if existedID == customerID {
					isExisted = true
					break
				}
			}

			if !isExisted {
				userVouchers = append(userVouchers, &model.UserPromotion{
					CreatedBy:   acc.AccountID,
					CustomerID:  customerID,
					PromotionID: voucher.PromotionID,
					VoucherCode: voucher.Code,
					Status:      userVoucher.Status,
				})
			}
		}
		resp := model.UserPromotionDB.CreateMany(userVouchers)
		return resp
	} else if userVoucher.CustomerID != 0 {
		query := model.UserPromotion{
			VoucherCode: voucher.Code,
			CustomerID:  userVoucher.CustomerID,
		}
		qUserVoucher := model.UserPromotionDB.Query(query, 0, 0, nil)
		if qUserVoucher.Status == common.APIStatus.Ok {
			existedUser := qUserVoucher.Data.([]*model.UserPromotion)[0]
			if existedUser.Status != nil && userVoucher.Status != nil {
				if *existedUser.Status == *userVoucher.Status || (*existedUser.Status == enum.CodeStatus.USED && *userVoucher.Status == enum.CodeStatus.ACTIVE) {
					return &common.APIResponse{
						Status:    common.APIStatus.Existed,
						Data:      qUserVoucher.Data,
						Message:   "Customer added voucher",
						ErrorCode: "EXISTED",
					}
				}

				if *userVoucher.Status == enum.CodeStatus.ACTIVE &&
					(*existedUser.Status == enum.CodeStatus.DELETED || *existedUser.Status == enum.CodeStatus.INACTIVE) && existedUser.Amount != nil && *existedUser.Amount > 0 {
					return model.UserPromotionDB.UpdateOne(query, &model.UserPromotion{
						Status: &enum.CodeStatus.USED,
					})
				}

				return model.UserPromotionDB.UpdateOne(query, &model.UserPromotion{
					Status: userVoucher.Status,
				})
			}
		}
		resp := model.UserPromotionDB.Create(userVoucher)
		if resp.Status == common.APIStatus.Ok {

		}
		return resp
	} else {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing customer value",
			ErrorCode: "MISSING_CUSTOMER_VALUE",
		}
	}
}

// UpdateUserVoucher is func to update status user voucher
func UpdateUserVoucher(acc *model.Account, userVoucher *model.UserPromotion) *common.APIResponse {
	qVoucher := model.VoucherDB.QueryOne(model.Voucher{
		Code: userVoucher.VoucherCode,
	})
	if qVoucher.Status != common.APIStatus.Ok {
		return qVoucher
	}
	defer func() {
		go WarmUpVoucherByCode(userVoucher.VoucherCode, nil)
		go WarmUpUserPromotion(userVoucher.VoucherCode, 0, nil)
	}()
	voucher := qVoucher.Data.([]*model.Voucher)[0]

	if userVoucher.IsUpdateAll && userVoucher.PrevStatus != nil {
		query := model.UserPromotion{
			VoucherCode: voucher.Code,
			Status:      userVoucher.PrevStatus,
		}

		if *userVoucher.PrevStatus == enum.CodeStatus.ACTIVE {
			query.ComplexQuery = []*bson.M{
				{
					"status": bson.M{"$in": []string{string(enum.CodeStatus.ACTIVE), string(enum.CodeStatus.USED)}},
				},
			}
			query.Status = nil
		}

		customerIds := make([]int64, 0)
		userVoucherResp := model.UserPromotionDB.Query(query, 0, 0, nil)
		if userVoucherResp.Status != common.APIStatus.Ok {
			return userVoucherResp
		}
		for _, userVoucherItem := range userVoucherResp.Data.([]*model.UserPromotion) {
			customerIds = append(customerIds, userVoucherItem.CustomerID)
		}

		updater := model.UserPromotion{
			Status: userVoucher.Status,
		}
		resp := model.UserPromotionDB.UpdateMany(query, updater)

		responseData := &model.UserPromotion{
			CustomerIDs: customerIds,
		}
		resp.Data = []*model.UserPromotion{
			responseData,
		}
		return resp
	}
	if len(userVoucher.CustomerIDs) > 0 {
		query := model.UserPromotion{
			VoucherCode: voucher.Code,
			ComplexQuery: []*bson.M{
				{
					"customer_id": bson.M{"$in": userVoucher.CustomerIDs},
				},
			},
		}
		updater := model.UserPromotion{
			Status: userVoucher.Status,
		}
		return model.UserPromotionDB.UpdateMany(query, updater)
	} else {
		return model.UserPromotionDB.UpdateOne(model.UserPromotion{VoucherCode: voucher.Code, CustomerID: userVoucher.CustomerID}, userVoucher)
	}
}
