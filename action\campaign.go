package action

import (
	"encoding/json"
	"fmt"
	"math"
	"sort"
	"strings"
	"time"

	"gitlab.com/buymed.th/marketplace/promotion/conf"
	"gitlab.com/buymed.th/marketplace/promotion/utils"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/buymed.th/marketplace/promotion/client"
	"gitlab.com/buymed.th/marketplace/promotion/helper"
	"gitlab.com/buymed.th/marketplace/promotion/model"
	"gitlab.com/buymed.th/marketplace/promotion/model/cache"
	"gitlab.com/buymed.th/marketplace/promotion/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func GetListCampaignProduct(query interface{}, offset, limit int64, getTotal bool, sort *primitive.M) *common.APIResponse {
	result := model.CampaignProductDB.Query(query, offset, limit, sort)
	if result.Status != common.APIStatus.Ok {
		return result
	}
	if getTotal {
		result.Total = model.CampaignProductDB.Count(query).Total
	}
	return result
}

func GetListActiveCampaign(input *model.Campaign, sellerCode string, account *model.Account) *common.APIResponse {
	customerInfo, err := client.Services.Customer.GetCustomerByAccountID(account.AccountID)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Forbidden,
			Message:   "Tài khoản không xác định",
			ErrorCode: "CUSTOMER_NOT_FOUND",
		}
	}

	var customerRegionCodes []string
	regionResp := client.LocationClient.GetRegionList([]string{customerInfo.ProvinceCode})
	if regionResp.Status == common.APIStatus.Ok {
		for _, region := range regionResp.Data {
			customerRegionCodes = append(customerRegionCodes, region.Code)
		}
	}

	// check time
	t := time.Now()

	query := &model.Campaign{
		ComplexQuery: []*bson.M{
			{
				"view_type": bson.M{
					"$ne": "INTERNAL",
				},
			},
			{
				"end_time": bson.M{
					"$gte": t,
				},
				"is_active": true,
				"total_product": bson.M{
					"$gte": 0,
				},
			},
			{
				"$or": []bson.M{
					{"start_time": bson.M{
						"$lt": t,
					}},
					{"registration_end_time": bson.M{
						"$lt": t,
					}},
				},
			},
			{
				"$or": []*bson.M{
					{
						"regions": bson.M{
							"$in": customerRegionCodes,
						},
					},
					{
						"regions": bson.M{"$size": 0},
					},
					{
						"regions": bson.M{"$exists": false},
					},
				},
			},
			{
				"$or": []*bson.M{
					{
						"customer_scopes": customerInfo.Scope,
					},
					{
						"customer_scopes": bson.M{"$size": 0},
					},
					{
						"customer_scopes": bson.M{"$exists": false},
					},
				},
			},
		},
	}

	if input != nil {
		if input.CampaignCode != "" {
			query.CampaignCode = input.CampaignCode
		}

		if input.Slug != "" {
			query.Slug = input.Slug
		}

		if len(input.CampaignType) > 0 {
			query.CampaignType = input.CampaignType
		}
	}

	if len(sellerCode) > 0 && input.CampaignCode == "" {
		lstCampaignCode := make([]string, 0)
		countCampaignResult := model.CampaignProductDB.Distinct(&model.CampaignProduct{
			SellerCode: sellerCode,
		}, "campaign_code")
		if countCampaignResult.Status == common.APIStatus.Ok {
			for _, item := range countCampaignResult.Data.([]interface{}) {
				lstCampaignCode = append(lstCampaignCode, item.(string))
			}
		}
		if len(lstCampaignCode) > 0 {
			query.ComplexQuery = append(query.ComplexQuery, &bson.M{
				"campaign_code": bson.M{
					"$in": lstCampaignCode,
				},
			})
		}
	}

	//fmt.Printf("count %v\n", model.CampaignCacheDB.Count(model.Campaign{}).Total)
	result := model.CampaignCacheDB.Query(query, 0, 0, &primitive.M{"_id": -1})
	if result.Status != common.APIStatus.Ok {
		//fmt.Println("DEBUG 144")
		return result
	}

	campaigns := result.Data.([]*model.Campaign)
	// fmt.Println("len=", len(campaigns))
	campaignResult := make([]*model.Campaign, 0)
	ZERO := int64(0)
	for _, campaign := range campaigns {
		campaign.TotalProduct = &ZERO
		campaign.TotalRevenue = &ZERO
		campaign.TotalSeller = &ZERO
		campaign.SellerCodes = nil
		campaign.Regions = nil
		campaign.FlashSaleTimes = nil
		match := false

		// filter customer scope
		if campaign.CustomerScopes != nil && len(*campaign.CustomerScopes) > 0 {
			for _, scope := range *campaign.CustomerScopes {
				if customerInfo.Scope == scope {
					match = true
					break
				}
			}
			if !match {
				continue
			}
		}
		t := time.Now()
		h := t.Hour()
		endDay := *GetLastDay(t)
		saleCurrentDay := make([]*model.CampaignFlashSaleTimeItem, 0)
		// map check duplicate
		saleMapCurrentDay := make(map[string]*model.CampaignFlashSaleTimeItem)

		// fmt.Println("hour=", h)
		for idx, saleTime := range campaign.FlashSaleTimesView {
			// fmt.Printf("compare: %d vs %d, %d vs %d,%d vs %d\n", saleTime.EndTime.Day(), endDay.Day(), saleTime.EndTime.Month(), endDay.Month(), saleTime.EndTime.Hour(), t.Hour())
			if saleTime.EndTime.Day() == endDay.Day() &&
				saleTime.EndTime.Month() == endDay.Month() &&
				saleTime.EndTime.Year() == endDay.Year() &&
				saleTime.EndTime.Hour() >= h { // 10h >= 23h
				if _, ok := saleMapCurrentDay[saleTime.Code]; !ok {
					saleMapCurrentDay[saleTime.Code] = saleTime
					saleCurrentDay = append(saleCurrentDay, saleTime)
				}
				continue
			}

			if idx == len(campaign.FlashSaleTimesView)-1 && len(saleCurrentDay) == 0 {
				// todo add more
			}
			// lay them 1 khung gio
			// if len(saleCurrentDay) < len(model.CampaignFlashSaleTime) {
			// 	if saleTime.EndTime.Day() > endDay.Day() &&
			// 		saleTime.EndTime.Month() == endDay.Month() {
			// 		saleCurrentDay = append(saleCurrentDay, saleTime)
			// 	}
			// }
		}

		campaign.FlashSaleTimesView = nil
		if campaign.CampaignType == enum.CampaignType.FLASH_SALE && len(saleCurrentDay) > 0 {
			campaign.FlashSaleTimesView = saleCurrentDay
			campaignResult = append(campaignResult, campaign)
		} else if campaign.CampaignType == enum.CampaignType.NORMAL {
			campaignResult = append(campaignResult, campaign)
		}

		// if campaign.Regions != nil && len(*campaign.Regions) > 0 {
		// 	for _, reg := range *campaign.Regions {
		// 		if customerInfo.ProvinceCode == reg {
		// 			match = true
		// 		}
		// 	}
		// }
	}

	if len(campaignResult) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Campaign not found",
		}
	}

	result.Data = campaignResult
	result.Total = int64(len(campaignResult))
	return result
}

func GetListCampaign(query interface{}, offset, limit int64, getTotal bool, isJoined bool, sellerCode string, sort *primitive.M) *common.APIResponse {
	result := model.CampaignDB.Query(query, offset, limit, sort)
	if result.Status != common.APIStatus.Ok {
		return result
	}

	campaigns := result.Data.([]*model.Campaign)
	campaignCode := make([]string, 0)
	campaignID := make([]int64, 0)
	// campaignProductMap := make(map[string]*model.CampaignProduct)
	campaignProductMap := make(map[int64]*model.Ticket)
	for _, campaign := range campaigns {
		campaignCode = append(campaignCode, campaign.CampaignCode)
		campaignID = append(campaignID, campaign.CampaignID)
	}
	if isJoined && len(sellerCode) > 0 {
		campaignTicketResult := model.TicketDB.Query(bson.M{
			"campaign_id": bson.M{
				"$in": campaignID,
			},
			"seller_code": sellerCode,
		}, 0, 0, nil)
		if campaignTicketResult.Status == common.APIStatus.Ok {
			campaignTickets := campaignTicketResult.Data.([]*model.Ticket)
			for _, campaignTicket := range campaignTickets {
				campaignProductMap[campaignTicket.CampaignID] = campaignTicket
			}
		}
		TRUE := true
		FALSE := false
		for idx, campaign := range campaigns {
			if campaignProductMap[campaign.CampaignID] != nil {
				campaigns[idx].IsJoined = &TRUE
			} else {
				campaigns[idx].IsJoined = &FALSE
			}
		}

		// campaignProductResult := model.CampaignProductDB.Query(bson.M{
		// 	"campaign_code": bson.M{
		// 		"$in": campaignCode,
		// 	},
		// 	"seller_code": sellerCode,
		// }, 0, 0, nil)
		// if campaignProductResult.Status == common.APIStatus.Ok {
		// 	campaignProducts := campaignProductResult.Data.([]*model.CampaignProduct)
		// 	for _, campaignProduct := range campaignProducts {
		// 		campaignProductMap[campaignProduct.CampaignCode] = campaignProduct
		// 	}
		// }
		// TRUE := true
		// FALSE := false
		// for idx, campaign := range campaigns {
		// 	if campaignProductMap[campaign.CampaignCode] != nil {
		// 		campaigns[idx].IsJoined = &TRUE
		// 	} else {
		// 		campaigns[idx].IsJoined = &FALSE
		// 	}
		// }
		result.Data = campaigns
	}
	getCampaignFlashSaleTimeItems(campaigns)
	if getTotal {
		result.Total = model.CampaignDB.Count(query).Total
	}
	return result
}

func CreateCampaign(accountID int64, in *model.CampaignCreateRequest) *common.APIResponse {
	ZERO := int64(0)
	FALSE := false
	campaignID, campaignCode := model.GenCampaignID()

	// split sale time if campaign is FLASH SALE
	campaignSaleTimeLst := make([]*model.CampaignSaleTime, 0)
	saleTimeViewLst := make([]*model.CampaignFlashSaleTimeItem, 0)

	if in.CampaignType == enum.CampaignType.FLASH_SALE {
		campaignSaleTime := make(map[int64][]*model.CampaignSaleTime)

		for idx, item := range in.FlashSaleTimes {
			if item.EndTime == nil {
				in.FlashSaleTimes[idx].EndTime = &item.StartTime
			}
			in.FlashSaleTimes[idx].EndTime = GetLastDay(*in.FlashSaleTimes[idx].EndTime)
		}

		for i := 0; i < len(in.FlashSaleTimes); i++ {
			for j := i + 1; j < len(in.FlashSaleTimes); j++ {
				// start time
				if in.FlashSaleTimes[j].StartTime.Before(*in.FlashSaleTimes[i].EndTime) && (in.FlashSaleTimes[j].StartTime.After(in.FlashSaleTimes[i].StartTime) || in.FlashSaleTimes[j].StartTime.Equal(in.FlashSaleTimes[i].StartTime)) {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   fmt.Sprintf("Range time %s invalid", in.FlashSaleTimes[j].Name),
						ErrorCode: "START_TIME_INVALID",
					}
				}
				// end time
				if in.FlashSaleTimes[j].EndTime.After(in.FlashSaleTimes[i].StartTime) && (in.FlashSaleTimes[j].EndTime.Before(*in.FlashSaleTimes[i].EndTime) || in.FlashSaleTimes[j].EndTime.Equal(*in.FlashSaleTimes[i].EndTime)) {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   fmt.Sprintf("Range time %s invalid", in.FlashSaleTimes[j].Name),
						ErrorCode: "END_TIME_INVALID",
					}
				}

				if in.FlashSaleTimes[j].StartTime.After(in.FlashSaleTimes[i].StartTime) && in.FlashSaleTimes[j].EndTime.Before(*in.FlashSaleTimes[i].EndTime) {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   fmt.Sprintf("Range time %s invalid", in.FlashSaleTimes[j].Name),
						ErrorCode: "FLASH_SALE_TIME_IN_OTHER_RANGE_TIME",
					}
				}

				if in.FlashSaleTimes[j].StartTime.Before(in.FlashSaleTimes[i].StartTime) && in.FlashSaleTimes[j].EndTime.After(*in.FlashSaleTimes[i].EndTime) {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   fmt.Sprintf("Range time %s invalid", in.FlashSaleTimes[j].Name),
						ErrorCode: "FLASH_SALE_TIME_CONTAIN_OTHER_RANGE_TIME",
					}
				}
			}
		}

		for idx, item := range in.FlashSaleTimes {
			// format time
			in.FlashSaleTimes[idx].Code = fmt.Sprintf("%s_%d", campaignCode, idx+1)
			in.FlashSaleTimes[idx].StartTime = GetBeginDay(in.FlashSaleTimes[idx].StartTime)
			if item.EndTime == nil {
				in.FlashSaleTimes[idx].EndTime = &item.StartTime
			}
			in.FlashSaleTimes[idx].EndTime = GetLastDay(*in.FlashSaleTimes[idx].EndTime)

			for idx2, t := range item.Detail {
				if model.CampaignFlashSaleTime[t.Code] == nil {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   fmt.Sprintf("Range time %s invalid", t.Code),
						ErrorCode: "FLASH_SALE_TIME_NOT_FOUND",
					}
				}
				in.FlashSaleTimes[idx].Detail[idx2].Name = model.CampaignFlashSaleTime[t.Code].Name
				in.FlashSaleTimes[idx].Detail[idx2].Ref = fmt.Sprintf("%s_%d", in.FlashSaleTimes[idx].Code, idx2+1)
				in.FlashSaleTimes[idx].Detail[idx2].Hour = model.CampaignFlashSaleTime[t.Code].Hour
				numDays := int(math.Ceil(in.FlashSaleTimes[idx].EndTime.Sub(in.FlashSaleTimes[idx].StartTime).Hours() / 24))

				for i := 0; i < numDays; i++ {
					subSaleTime := *in.FlashSaleTimes[idx].Detail[idx2]
					start := in.FlashSaleTimes[idx].StartTime.Add(time.Duration(24*i) * time.Hour).Add(subSaleTime.Hour[0])
					end := in.FlashSaleTimes[idx].StartTime.Add(time.Duration(24*i) * time.Hour).Add(subSaleTime.Hour[1])
					subSaleTime.StartTime = &start
					subSaleTime.EndTime = &end
					subSaleTime.Day = i + 1
					subSaleTime.CampaignID = campaignID
					subSaleTime.RefTable = model.CampaignDB.ColName
					saleTimeViewLst = append(saleTimeViewLst, &subSaleTime)
				}

				if t.ProductIDs == nil || len(*t.ProductIDs) == 0 {
					if _, ok := campaignSaleTime[0]; !ok {
						campaignSaleTime[0] = make([]*model.CampaignSaleTime, 0)
					}

					saleTimeLst := make([]*model.CampaignFlashSaleTimeItem, 0)
					for i := 0; i < numDays; i++ {
						subSaleTime := *in.FlashSaleTimes[idx].Detail[idx2]
						start := in.FlashSaleTimes[idx].StartTime.Add(time.Duration(24*i) * time.Hour).Add(subSaleTime.Hour[0])
						end := in.FlashSaleTimes[idx].StartTime.Add(time.Duration(24*i) * time.Hour).Add(subSaleTime.Hour[1])
						subSaleTime.StartTime = &start
						subSaleTime.EndTime = &end
						subSaleTime.Day = i + 1
						saleTimeLst = append(saleTimeLst, &subSaleTime)
					}

					_, hashCode := model.GenCampaignID()
					campaignSaleTime[0] = append(campaignSaleTime[0], &model.CampaignSaleTime{
						CampaignID:   campaignID,
						CampaignCode: campaignCode,
						// Code:         fmt.Sprintf("%s_%d_ALL", in.FlashSaleTimes[idx].Code, idx2+1),
						Code:      hashCode,
						ProductID: &ZERO,
						NumDay:    numDays,
						Kind:      "ALL",
						SaleTime:  saleTimeLst,
						StartTime: &in.FlashSaleTimes[idx].StartTime,
						EndTime:   in.FlashSaleTimes[idx].EndTime,
					})
				} else {
					for idx3, product := range *t.ProductIDs {
						p := product
						if _, ok := campaignSaleTime[p]; !ok {
							campaignSaleTime[p] = make([]*model.CampaignSaleTime, 0)
						}
						exist := false
						for _, check := range campaignSaleTime[p] {
							if check.ProductID != nil && *check.ProductID == p && check.Code == fmt.Sprintf("%s_%d", in.FlashSaleTimes[idx].Code, idx2+idx3+1) {
								exist = true
								break
							}
						}

						if exist {
							continue
						}
						saleTimeLst := make([]*model.CampaignFlashSaleTimeItem, 0)
						for i := 0; i < numDays; i++ {
							subSaleTime := *in.FlashSaleTimes[idx].Detail[idx2]
							start := in.FlashSaleTimes[idx].StartTime.Add(time.Duration(24*i) * time.Hour).Add(subSaleTime.Hour[0])
							end := in.FlashSaleTimes[idx].StartTime.Add(time.Duration(24*i) * time.Hour).Add(subSaleTime.Hour[1])
							subSaleTime.StartTime = &start
							subSaleTime.EndTime = &end
							subSaleTime.Day = i + 1
							saleTimeLst = append(saleTimeLst, &subSaleTime)
						}

						_, hashCode := model.GenCampaignID()
						campaignSaleTime[p] = append(campaignSaleTime[p], &model.CampaignSaleTime{
							CampaignID:   campaignID,
							CampaignCode: campaignCode,
							// Code:         fmt.Sprintf("%s_%d_ALONE", in.FlashSaleTimes[idx].Code, idx2+idx3+1),
							Code:      hashCode,
							ProductID: &p,
							NumDay:    numDays,
							Kind:      "ALONE",
							SaleTime:  saleTimeLst,
							StartTime: &in.FlashSaleTimes[idx].StartTime,
							EndTime:   in.FlashSaleTimes[idx].EndTime,
						})
					}
				}
			}
		}

		for _, item := range campaignSaleTime {
			campaignSaleTimeLst = append(campaignSaleTimeLst, item...)
		}
	}

	sort.Slice(saleTimeViewLst, func(i, j int) bool {
		return saleTimeViewLst[i].StartTime.Before(*saleTimeViewLst[j].StartTime)
	})

	cam := &model.Campaign{
		CampaignCode:          campaignCode,
		CampaignID:            campaignID,
		CampaignName:          in.CampaignName,
		CampaignType:          in.CampaignType,
		Banner:                in.Banner,
		Description:           in.Description,
		Status:                enum.CampaignStatus.UPCOMING,
		RegistrationStartTime: in.RegistrationStartTime,
		RegistrationEndTime:   in.RegistrationEndTime,
		StartTime:             in.StartTime,
		EndTime:               in.EndTime,
		SellerCodes:           in.SellerCodes,
		CustomerScopes:        in.CustomerScopes,
		Regions:               in.Regions,
		Reward:                in.Reward,
		Fulfill:               in.Fulfill,

		TotalProduct: &ZERO,
		TotalSeller:  &ZERO,
		TotalRevenue: &ZERO,
		CreatedBy:    accountID,
		IsActive:     &FALSE,
		SaleType:     in.SaleType,
		Slug:         helper.NormalizeString(fmt.Sprintf("%s-%s", campaignCode, in.CampaignName)),
		// update sale time default
		FlashSaleTimes: in.FlashSaleTimes,
	}

	cam.ToHashTag()

	cam.FlashSaleTimesView = nil
	es := model.CampaignDB.Create(cam)
	if es.Status != common.APIStatus.Ok {
		return es
	}
	if len(saleTimeViewLst) > 0 && in.CampaignType == enum.CampaignType.FLASH_SALE {
		model.CampaignFlashSaleTimeItemDB.CreateMany(saleTimeViewLst)
	}
	if len(campaignSaleTimeLst) > 0 {
		es1 := model.CampaignSaleTimeDB.CreateMany(campaignSaleTimeLst)
		if es1.Status != common.APIStatus.Ok {
			return es1
		}
	}
	return es
}

func UpdateCampaign(accountID int64, in *model.CampaignUpdateRequest) *common.APIResponse {
	query := &model.Campaign{
		CampaignID: in.CampaignID,
	}
	result := model.CampaignDB.QueryOne(query)

	if result.Status != common.APIStatus.Ok {
		return result
	}

	campaign := result.Data.([]*model.Campaign)[0]
	turnOFF := false

	// ON -> OFF
	if campaign.IsActive != nil && *campaign.IsActive {
		if in.IsActive != nil && *in.IsActive == false {
			turnOFF = true
		}
	}

	// check data sku sale info in processing
	if in.IsActive != nil && *in.IsActive && campaign.IsActive != nil && !*campaign.IsActive {
		TRUE := true
		flagRes := cache.FlagDB.QueryOne(cache.Flag{
			IsProcessingSkuSaleInfo: &TRUE,
		})

		if flagRes.Status == common.APIStatus.Ok {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Đang xử lý dữ liệu, xin vui lòng đợi",
				ErrorCode: "IN_PROCESSING",
			}
		}
	}

	//if in.IsActive != nil && *in.IsActive {
	//	isValidIsActive, errMess := isValidCampaignTurnOn(campaign, "")
	//	if !isValidIsActive {
	//		return &common.APIResponse{
	//			Status:    common.APIStatus.Invalid,
	//			Message:   errMess,
	//			ErrorCode: "CONFLICT_CAMPAIGN",
	//		}
	//	}
	//}
	// update sale time ref
	ZERO := int64(0)
	ISVALID := false
	version := fmt.Sprintf("%d", time.Now().Unix())

	campaignSaleTimeLst := make([]*model.CampaignSaleTime, 0)
	saleTimeViewLst := make([]*model.CampaignFlashSaleTimeItem, 0)
	if campaign.CampaignType == enum.CampaignType.FLASH_SALE {
		campaignSaleTime := make(map[int64][]*model.CampaignSaleTime)

		for idx, item := range in.FlashSaleTimes {
			if item.EndTime == nil {
				in.FlashSaleTimes[idx].EndTime = &item.StartTime
			}
			in.FlashSaleTimes[idx].EndTime = GetLastDay(*in.FlashSaleTimes[idx].EndTime)
		}

		for i := 0; i < len(in.FlashSaleTimes); i++ {
			for j := i + 1; j < len(in.FlashSaleTimes); j++ {
				// start time
				if in.FlashSaleTimes[j].StartTime.Before(*in.FlashSaleTimes[i].EndTime) && (in.FlashSaleTimes[j].StartTime.After(in.FlashSaleTimes[i].StartTime) || in.FlashSaleTimes[j].StartTime.Equal(in.FlashSaleTimes[i].StartTime)) {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   fmt.Sprintf("Range time %s invalid", in.FlashSaleTimes[j].Name),
						ErrorCode: "START_TIME_INVALID",
					}
				}
				// end time
				if in.FlashSaleTimes[j].EndTime.After(in.FlashSaleTimes[i].StartTime) && (in.FlashSaleTimes[j].EndTime.Before(*in.FlashSaleTimes[i].EndTime) || in.FlashSaleTimes[j].EndTime.Equal(*in.FlashSaleTimes[i].EndTime)) {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   fmt.Sprintf("Range time %s invalid", in.FlashSaleTimes[j].Name),
						ErrorCode: "END_TIME_INVALID",
					}
				}

				if in.FlashSaleTimes[j].StartTime.After(in.FlashSaleTimes[i].StartTime) && in.FlashSaleTimes[j].EndTime.Before(*in.FlashSaleTimes[i].EndTime) {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   fmt.Sprintf("Range time %s invalid", in.FlashSaleTimes[j].Name),
						ErrorCode: "FLASH_SALE_TIME_IN_OTHER_RANGE_TIME",
					}
				}

				if in.FlashSaleTimes[j].StartTime.Before(in.FlashSaleTimes[i].StartTime) && in.FlashSaleTimes[j].EndTime.After(*in.FlashSaleTimes[i].EndTime) {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   fmt.Sprintf("Range time %s invalid", in.FlashSaleTimes[j].Name),
						ErrorCode: "FLASH_SALE_TIME_CONTAIN_OTHER_RANGE_TIME",
					}
				}
			}
		}

		for idx, item := range in.FlashSaleTimes {
			ISVALID = true
			in.FlashSaleTimes[idx].Code = fmt.Sprintf("%s_%d", campaign.CampaignCode, idx+1)
			in.FlashSaleTimes[idx].StartTime = GetBeginDay(in.FlashSaleTimes[idx].StartTime)
			if item.EndTime == nil {
				in.FlashSaleTimes[idx].EndTime = &item.StartTime
			}
			in.FlashSaleTimes[idx].EndTime = GetLastDay(*in.FlashSaleTimes[idx].EndTime)
			for idx2, t := range item.Detail {
				if model.CampaignFlashSaleTime[t.Code] == nil {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   fmt.Sprintf("Range time %s invalid", t.Code),
						ErrorCode: "FLASH_SALE_TIME_NOT_FOUND",
					}
				}
				in.FlashSaleTimes[idx].Detail[idx2].Name = model.CampaignFlashSaleTime[t.Code].Name
				in.FlashSaleTimes[idx].Detail[idx2].Ref = fmt.Sprintf("%s_%d", in.FlashSaleTimes[idx].Code, idx2+1)
				in.FlashSaleTimes[idx].Detail[idx2].Hour = model.CampaignFlashSaleTime[t.Code].Hour
				numDays := int(math.Ceil(in.FlashSaleTimes[idx].EndTime.Sub(in.FlashSaleTimes[idx].StartTime).Hours() / 24))

				for i := 0; i < numDays; i++ {
					subSaleTime := *in.FlashSaleTimes[idx].Detail[idx2]
					start := in.FlashSaleTimes[idx].StartTime.Add(time.Duration(24*i) * time.Hour).Add(subSaleTime.Hour[0])
					end := in.FlashSaleTimes[idx].StartTime.Add(time.Duration(24*i) * time.Hour).Add(subSaleTime.Hour[1])
					subSaleTime.StartTime = &start
					subSaleTime.EndTime = &end
					subSaleTime.Day = i + 1
					subSaleTime.CampaignID = campaign.CampaignID
					subSaleTime.RefTable = model.CampaignDB.ColName
					saleTimeViewLst = append(saleTimeViewLst, &subSaleTime)
				}

				if t.ProductIDs == nil || len(*t.ProductIDs) == 0 {
					if _, ok := campaignSaleTime[0]; !ok {
						campaignSaleTime[0] = make([]*model.CampaignSaleTime, 0)
					}

					saleTimeLst := make([]*model.CampaignFlashSaleTimeItem, 0)
					for i := 0; i < numDays; i++ {
						subSaleTime := *in.FlashSaleTimes[idx].Detail[idx2]
						start := in.FlashSaleTimes[idx].StartTime.Add(time.Duration(24*i) * time.Hour).Add(subSaleTime.Hour[0])
						end := in.FlashSaleTimes[idx].StartTime.Add(time.Duration(24*i) * time.Hour).Add(subSaleTime.Hour[1])
						subSaleTime.StartTime = &start
						subSaleTime.EndTime = &end
						subSaleTime.Day = i + 1
						saleTimeLst = append(saleTimeLst, &subSaleTime)
					}

					_, hashCode := model.GenCampaignID()

					campaignSaleTime[0] = append(campaignSaleTime[0], &model.CampaignSaleTime{
						CampaignID:   campaign.CampaignID,
						CampaignCode: campaign.CampaignCode,
						Code:         hashCode,
						ProductID:    &ZERO,
						NumDay:       numDays,
						Kind:         "ALL",
						SaleTime:     saleTimeLst,
						StartTime:    &in.FlashSaleTimes[idx].StartTime,
						EndTime:      in.FlashSaleTimes[idx].EndTime,
						Version:      version,
					})
				} else {
					for idx3, product := range *t.ProductIDs {
						p := product
						if _, ok := campaignSaleTime[p]; !ok {
							campaignSaleTime[p] = make([]*model.CampaignSaleTime, 0)
						}
						exist := false
						for _, check := range campaignSaleTime[p] {
							if check.ProductID != nil && *check.ProductID == p && check.Code == fmt.Sprintf("%s_%d", in.FlashSaleTimes[idx].Code, idx2+idx3+1) {
								exist = true
								break
							}
						}

						if exist {
							continue
						}
						saleTimeLst := make([]*model.CampaignFlashSaleTimeItem, 0)
						for i := 0; i < numDays; i++ {
							subSaleTime := *in.FlashSaleTimes[idx].Detail[idx2]
							start := in.FlashSaleTimes[idx].StartTime.Add(time.Duration(24*i) * time.Hour).Add(subSaleTime.Hour[0])
							end := in.FlashSaleTimes[idx].StartTime.Add(time.Duration(24*i) * time.Hour).Add(subSaleTime.Hour[1])
							subSaleTime.StartTime = &start
							subSaleTime.EndTime = &end
							subSaleTime.Day = i + 1
							saleTimeLst = append(saleTimeLst, &subSaleTime)
						}

						_, hashCode := model.GenCampaignID()
						campaignSaleTime[p] = append(campaignSaleTime[p], &model.CampaignSaleTime{
							CampaignID:   campaign.CampaignID,
							CampaignCode: campaign.CampaignCode,
							Code:         hashCode,
							ProductID:    &p,
							NumDay:       numDays,
							Kind:         "ALONE",
							SaleTime:     saleTimeLst,
							StartTime:    &in.FlashSaleTimes[idx].StartTime,
							EndTime:      in.FlashSaleTimes[idx].EndTime,
							Version:      version,
						})
					}
				}
			}
		}
		for _, item := range campaignSaleTime {
			campaignSaleTimeLst = append(campaignSaleTimeLst, item...)
		}
	}

	sort.Slice(saleTimeViewLst, func(i, j int) bool {
		return saleTimeViewLst[i].StartTime.Before(*saleTimeViewLst[j].StartTime)
	})

	if len(in.CampaignName) > 0 {
		in.HashTag = fmt.Sprintf("%s,%s,%s,%d", in.CampaignName, utils.NormalizeString(in.CampaignName), campaign.CampaignCode, campaign.CampaignID)
	}

	if turnOFF {
		in.NeedCheck = "OFF"
	}

	// validate conflict time with other campaign
	if (in.IsActive != nil && *in.IsActive == true) || (in.IsActive == nil && campaign.IsActive != nil && *campaign.IsActive) {
		if !in.StartTime.IsZero() {
			campaign.StartTime = in.StartTime
		}
		if !in.EndTime.IsZero() {
			campaign.EndTime = in.EndTime
		}

		campaignValidate := campaign
		if in.IsActive != nil && *in.IsActive == true {
			campaignValidate.IsActive = in.IsActive
		}
		if in.CustomerScopes != nil {
			campaignValidate.CustomerScopes = in.CustomerScopes
		}
		if in.Regions != nil {
			campaignValidate.Regions = in.Regions
		}

		isValidCampaignTime, errors := isValidCampaignTurnOn(campaignValidate, "")
		if !isValidCampaignTime {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Có sản phẩm đang hoạt động trong chương trình khác",
				ErrorCode: "CONFLICT_CAMPAIGN",
				Data:      errors,
			}
		}
	}

	if campaign.Status == enum.CampaignStatus.PROCESSING && in.StartTime.After(campaign.StartTime) && in.StartTime.After(time.Now()) {
		in.Status = enum.CampaignStatus.UPCOMING
	}
	resultUpdate := model.CampaignDB.UpdateOne(query, in)

	if resultUpdate.Status == common.APIStatus.Ok {
		if len(saleTimeViewLst) > 0 && campaign.CampaignType == enum.CampaignType.FLASH_SALE {
			_ = model.CampaignFlashSaleTimeItemDB.Delete(&model.CampaignFlashSaleTimeItem{CampaignID: in.CampaignID})
			_ = model.CampaignFlashSaleTimeItemDB.CreateMany(saleTimeViewLst)
		}

		TRUE := true
		FALSE := false

		cache.FlagDB.UpdateOne(cache.Flag{
			IsProcessingSkuSaleInfo: &FALSE,
		}, cache.Flag{
			IsProcessingSkuSaleInfo: &TRUE,
		})

		go func() {
			if ISVALID && campaign.CampaignType == enum.CampaignType.FLASH_SALE {
				es1 := model.CampaignSaleTimeDB.Delete(&model.CampaignSaleTime{
					CampaignCode: campaign.CampaignCode,
				})
				if es1.Status == common.APIStatus.Ok && len(campaignSaleTimeLst) > 0 {
					pInsert := model.CampaignSaleTimeDB.CreateMany(campaignSaleTimeLst)
					if pInsert.Status != common.APIStatus.Ok {
						return
					}
				}
				// reset sale time of campaign all product
				qCampaignProduct := model.CampaignProductDB.Query(&model.CampaignProduct{
					CampaignCode: campaign.CampaignCode,
				}, 0, 0, nil)
				if qCampaignProduct.Status == common.APIStatus.Ok {
					campaignProducts := qCampaignProduct.Data.([]*model.CampaignProduct)
					for _, product := range campaignProducts {
						saleTimeItems := make([]*model.CampaignFlashSaleTimeItem, 0)
						for _, saleTimeRef := range product.FlashSaleTime {
							for _, campaignSaleTime := range campaignSaleTimeLst {
								for _, saleTime := range campaignSaleTime.SaleTime {
									if saleTime.Ref == saleTimeRef {
										saleTimeItems = append(saleTimeItems, saleTime)
									}
								}
							}
						}

						_ = model.CampaignProductDB.UpdateOne(&model.CampaignProduct{
							CampaignProductCode: product.CampaignProductCode,
						}, &model.CampaignProduct{
							FlashSaleTimes: saleTimeItems,
							Version:        version,
						})
					}
				}
			}
			WarnUpAllCampaignProduct(campaign.CampaignCode)
			WarmUpCampaign(campaign.CampaignCode, nil)

			campaignData := resultUpdate.Data.([]*model.Campaign)[0]
			WarmupSkuSaleInfoByCampaign(campaignData)
			client.Services.Product.WarmupProductCache()
			cache.FlagDB.UpdateOne(cache.Flag{
				IsProcessingSkuSaleInfo: &TRUE,
			}, cache.Flag{
				IsProcessingSkuSaleInfo: &FALSE,
			})
		}()
	}

	return resultUpdate
}

func GetCampaign(query interface{}, isJoined bool, sellerCode string) *common.APIResponse {
	result := model.CampaignDB.QueryOne(query)

	if result.Status != common.APIStatus.Ok {
		return result
	}
	campaign := result.Data.([]*model.Campaign)[0]
	getCampaignFlashSaleTimeItems([]*model.Campaign{
		campaign,
	})
	resultCampaignSaleTime := model.CampaignSaleTimeDB.Query(&model.CampaignSaleTime{
		CampaignCode: campaign.CampaignCode,
	}, 0, 0, nil)
	if resultCampaignSaleTime.Status == common.APIStatus.Ok {
		campaign.CampaignSaleTime = resultCampaignSaleTime.Data.([]*model.CampaignSaleTime)
	}
	if isJoined && len(sellerCode) > 0 {
		campaignProductResult := model.TicketDB.Query(bson.M{
			"campaign_id": campaign.CampaignID,
			"seller_code": sellerCode,
		}, 0, 0, nil)
		TRUE := true
		FALSE := false
		campaign.IsJoined = &FALSE
		if campaignProductResult.Status == common.APIStatus.Ok {
			campaign.IsJoined = &TRUE
		}

		// campaignProductResult := model.CampaignProductDB.Query(bson.M{
		// 	"campaign_code": campaign.CampaignCode,
		// 	"seller_code":   sellerCode,
		// }, 0, 0, nil)
		// TRUE := true
		// FALSE := false
		// campaign.IsJoined = &FALSE
		// if campaignProductResult.Status == common.APIStatus.Ok {
		// 	campaign.IsJoined = &TRUE
		// }
	}
	result.Data = []*model.Campaign{
		campaign,
	}
	return result
}

func SellerCampaignCheck(sellerCode string, productIds []int64) *common.APIResponse {
	campaignResult := model.CampaignDB.Query(bson.M{"status": bson.M{
		"$in": []string{"UPCOMING", "PROCESSING"},
	}}, 0, 0, nil)
	if campaignResult.Status != common.APIStatus.Ok {
		campaignResult.Message = "Not found"
		return campaignResult
	}
	camapaigns := campaignResult.Data.([]*model.Campaign)
	getCampaignFlashSaleTimeItems(camapaigns)
	campaingnCodes := make([]string, 0)
	for _, item := range camapaigns {
		campaingnCodes = append(campaingnCodes, item.CampaignCode)
	}
	campaignProductResult := model.CampaignProductDB.Query(bson.M{
		"seller_code": sellerCode,
		"product_id": bson.M{
			"$in": productIds,
		},
		"campaign_code": bson.M{
			"$in": campaingnCodes,
		},
	}, 0, 0, nil)
	if campaignProductResult.Status != common.APIStatus.Ok {
		campaignProductResult.Message = "Not found"
		return campaignProductResult
	}
	return campaignProductResult
}

func SellerCreateTicketCampaign(accountID int64, in *model.SellerCampaignCreateRequest) *common.APIResponse {
	// todo check ticket
	confirmRemoveTicketID := int64(0)
	ticketCodeQ := ""
	ticket := &model.Ticket{}

	if in.TicketID != nil {
		queryCheckTic := model.TicketDB.QueryOne(&model.Ticket{
			SellerCode: in.SellerCode,
			TicketID:   *in.TicketID,
			// Status:     enum.TicketState.WaitApprove,
		})
		if queryCheckTic.Status == common.APIStatus.Ok {
			ticket = queryCheckTic.Data.([]*model.Ticket)[0]
			confirmRemoveTicketID = *in.TicketID
			ticketCodeQ = ticket.TicketCode
		}
	}
	queryTic := model.TicketDB.Query(&model.Ticket{
		SellerCode: in.SellerCode,
		ProductID:  in.ProductID,
		Status:     enum.TicketState.WaitApprove,
	}, 0, 0, nil)

	if queryTic.Status == common.APIStatus.Ok {
		tics := queryTic.Data.([]*model.Ticket)
		strTicCheck := strings.Join(in.FlashSaleTimes, ",")
		for _, tic := range tics {
			if tic.FlashSaleTimes != nil && tic.TicketID != confirmRemoveTicketID {
				for _, ticTime := range *tic.FlashSaleTimes {
					if strings.Contains(strTicCheck, ticTime) {
						return &common.APIResponse{
							Status:    common.APIStatus.Invalid,
							Message:   "This seller requested ticket before",
							ErrorCode: "TICKET_EXISTED",
						}
					}
				}
			}
		}
	}

	// todo check campaign

	queryCam := model.CampaignDB.QueryOne(&model.Campaign{
		CampaignID: in.CampaignID,
	})
	if queryCam.Status != common.APIStatus.Ok {
		return queryCam
	}

	// todo check again
	queryCamProduct := model.CampaignProductDB.QueryOne(&model.CampaignProduct{
		CampaignID: in.CampaignID,
		SellerCode: in.SellerCode,
		Sku:        in.Sku,
	})

	if queryCamProduct.Status == common.APIStatus.Ok {
		// return &common.APIResponse{
		// 	Status:    common.APIStatus.Invalid,
		// 	Message:   "Sku of seller existed",
		// 	ErrorCode: "SKU_EXISTED",
		// }
	}

	cam := queryCam.Data.([]*model.Campaign)[0]

	if cam.IsActive == nil || *cam.IsActive == false {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Campaign do not ready",
			ErrorCode: "CAMPAIGN_NOT_READY",
		}
	}
	isValid, errMess, errCode := isValidProductCampaignRegister(in.Sku, cam, nil)
	if !isValid {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   errMess,
			ErrorCode: errCode,
		}
	}

	skuInfo, err := client.Services.Product.GetSku(in.Sku)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Không tìm thấy thông tin sản phẩm: " + err.Error(),
		}
	}

	if skuInfo.Fulfill != nil && cam.Fulfill != nil && *skuInfo.Fulfill < *cam.Fulfill {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Sản phẩm không đạt tỷ lệ fulfillment tối thiểu của chương trình",
			ErrorCode: "UNQUALIFIED_SKU",
		}
	}

	if cam.CampaignType == enum.CampaignType.FLASH_SALE {
		if len(in.FlashSaleTimes) <= 0 {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Flash sale time invalid",
				ErrorCode: "RANGE_TIME_EMPTY",
			}
		}

		matchAll := true
		for _, fst := range in.FlashSaleTimes {
			match := false
			for _, campaignTime := range cam.FlashSaleTimes {
				for _, campaignDetailTime := range campaignTime.Detail {
					if fst == campaignDetailTime.Ref {
						match = true
						continue
					}
				}

			}
			matchAll = matchAll && match
		}

		if !matchAll {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Flash sale time invalid",
				ErrorCode: "RANGE_TIME_INVALID",
			}
		}
	}

	t := time.Now()
	if t.Before(cam.RegistrationStartTime) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Campaign do not open",
			ErrorCode: "CAMPAIGN_NOT_OPEN",
		}
	}

	if t.After(cam.RegistrationEndTime) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Campaign closed register",
			ErrorCode: "CAMPAIGN_CLOSE_REGISTER",
		}
	}

	if cam.Reward != nil &&
		cam.Reward.PercentageDiscount != nil &&
		cam.SaleType == enum.CampaignSaleType.ABSOLUTE && in.PercentageDiscount < *cam.Reward.PercentageDiscount {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   fmt.Sprintf("Percentage discount must to be greater than %d percent", *cam.Reward.PercentageDiscount),
			ErrorCode: "PERCENTAGE_INVALID",
		}
	}

	if cam.Reward != nil &&
		cam.Reward.AbsoluteDiscount != nil &&
		cam.SaleType == enum.CampaignSaleType.ABSOLUTE && in.AbsoluteDiscount < *cam.Reward.AbsoluteDiscount {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   fmt.Sprintf("Absolute discount must to be greater than %d", *cam.Reward.AbsoluteDiscount),
			ErrorCode: "ABSOLUTE_INVALID",
		}
	}

	if in.MaxQuantityPerOrder <= 0 {
		in.MaxQuantityPerOrder = 100000
	}

	ticketID := int64(0)
	ticketCode := ""

	if confirmRemoveTicketID > 0 && ticket.Status != enum.TicketState.Approved && ticket.Status != enum.TicketState.Rejected {
		_ = model.TicketDB.Delete(&model.Ticket{
			SellerCode: in.SellerCode,
			TicketID:   confirmRemoveTicketID,
		})
		ticketID = confirmRemoveTicketID
		ticketCode = ticketCodeQ
	} else {
		ticketID, ticketCode = model.GenTicketID()
	}

	tic := &model.Ticket{
		TicketCode:          ticketCode,
		TicketID:            ticketID,
		TicketType:          "CAMPAIGN",
		CampaignID:          cam.CampaignID,
		ProductID:           in.ProductID,
		ProductCode:         in.ProductCode,
		Sku:                 in.Sku,
		SellerCode:          in.SellerCode,
		Price:               in.Price,
		Quantity:            in.Quantity,
		MaxQuantityPerOrder: &in.MaxQuantityPerOrder,
		CreatedBy:           accountID,
		Status:              enum.TicketState.WaitApprove,
		SaleType:            in.SaleType,
		FlashSaleTimes:      &in.FlashSaleTimes,
		MaxDiscount:         in.MaxDiscount,
	}
	if cam.SaleType == enum.CampaignSaleType.UNLIMIT {
		if in.AbsoluteDiscount > 0 {
			tic.AbsoluteDiscount = &in.AbsoluteDiscount
		} else {
			tic.PercentageDiscount = &in.PercentageDiscount
		}
	} else {
		if cam.SaleType == enum.CampaignSaleType.ABSOLUTE {
			tic.AbsoluteDiscount = &in.AbsoluteDiscount
		} else {
			tic.PercentageDiscount = &in.PercentageDiscount
		}
	}

	es := model.TicketDB.Create(tic)
	if es.Status != common.APIStatus.Ok {
		return es
	}
	go func() {
		totalSeller := int64(0)
		countSellerResult := model.TicketDB.Distinct(&model.Ticket{
			CampaignID: tic.CampaignID,
		}, "seller_code")
		if countSellerResult.Status == common.APIStatus.Ok {
			totalSeller = int64(len(countSellerResult.Data.([]interface{})))
		}
		_ = model.CampaignDB.UpdateOne(model.Campaign{CampaignID: tic.CampaignID}, model.Campaign{TotalSeller: &totalSeller})
	}()

	return es
}

func GetListTicketCampaign(query interface{}, offset, limit int64, getTotal bool) *common.APIResponse {
	result := model.TicketDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if result.Status != common.APIStatus.Ok {
		return result
	}
	if getTotal {
		result.Total = model.TicketDB.Count(query).Total
	}
	return result
}

func UpdateTicketStatus(accountID int64, in *model.TicketUpdateStatusRequest) *common.APIResponse {
	startTime := time.Now()
	defer func() {
		fmt.Printf("UpdateTicketStatus %d, %v ms \n", in.TicketID, time.Since(startTime).Milliseconds())
	}()
	result := model.TicketDB.QueryOne(&model.Ticket{
		TicketID:   in.TicketID,
		SellerCode: in.SellerCode,
	})

	if result.Status != common.APIStatus.Ok {
		return result
	}

	tic := result.Data.([]*model.Ticket)[0]
	//TRUE := true
	query := &model.CampaignActiveRequest{}
	//query.IsActive = &TRUE
	query.CampaignID = tic.CampaignID
	query.ComplexOrQuery = []*bson.M{
		{
			"seller_codes": bson.M{
				"$size": 0,
			},
		},
		{
			"seller_codes": in.SellerCode,
		},
	}
	campaignResult := GetCampaign(query, false, "")

	if campaignResult.Status != common.APIStatus.Ok {
		return campaignResult
	}

	campaign := campaignResult.Data.([]*model.Campaign)[0]

	if in.Status == enum.TicketState.Approved {
		skuInfo, err := client.Services.Product.GetSku(tic.Sku)
		if err != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "Không tìm thấy thông tin sản phẩm: " + err.Error(),
			}
		}

		if skuInfo.Fulfill != nil && campaign.Fulfill != nil && *skuInfo.Fulfill < *campaign.Fulfill {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Tỷ lệ fulfill của sản phẩm chưa đạt giá trị để tham gia chương trình khuyến mãi",
				ErrorCode: "UNQUALIFIED_SKU",
			}
		}
	}

	t := time.Now()
	if tic.Status != in.Status && tic.Status == enum.TicketState.WaitApprove {
		updater := &model.Ticket{
			TicketID:   in.TicketID,
			SellerCode: in.SellerCode,
			Status:     in.Status,
			UpdatedBy:  accountID,
		}

		if in.Status == enum.TicketState.Approved {
			updater.ApprovedTime = &t
			ZERO := int64(0)
			TRUE := true
			product := &model.CampaignProduct{
				CampaignProductID:   tic.TicketID,
				CampaignProductCode: tic.TicketCode,
				CampaignType:        campaign.CampaignType,
				CampaignID:          tic.CampaignID,
				CampaignCode:        campaign.CampaignCode,
				ProductID:           tic.ProductID,
				ProductCode:         tic.ProductCode,
				Price:               tic.Price,
				SalePrice:           tic.SalePrice,
				Sku:                 tic.Sku,
				SellerCode:          tic.SellerCode,
				AbsoluteDiscount:    tic.AbsoluteDiscount,
				PercentageDiscount:  tic.PercentageDiscount,
				Quantity:            &tic.Quantity,
				MaxQuantityPerOrder: tic.MaxQuantityPerOrder,
				SoldQuantity:        &ZERO,
				CreatedBy:           accountID,
				MaxDiscount:         &tic.MaxDiscount,
				Status:              enum.CampaignProductStatus.NORMAL,
				IsActive:            &TRUE,
			}

			mapCategoryCode := make(map[string]bool)
			productData, err := client.Services.Product.GetProduct(tic.ProductCode)
			if err != nil {
				return &common.APIResponse{
					Status:  common.APIStatus.Error,
					Message: "Không tìm thấy thông tin sản phẩm: " + err.Error(),
				}
			}

			for _, categoryCode := range productData.CategoryCodes {
				mapCategoryCode[categoryCode] = true
			}

			flashSaleTime := make([]*model.CampaignFlashSaleTimeItem, 0)
			flashSaleTimeRef := make([]string, 0)
			mapFlashSaleTimeRef := make(map[string]*string)

			if campaign.CampaignType == enum.CampaignType.FLASH_SALE && tic.FlashSaleTimes != nil {
				qCampaignSaleTime := model.CampaignSaleTimeDB.Query(&model.CampaignSaleTime{CampaignCode: campaign.CampaignCode}, 0, 0, nil)
				if qCampaignSaleTime.Status == common.APIStatus.Ok {
					campaignSaleTimes := qCampaignSaleTime.Data.([]*model.CampaignSaleTime)
					saleTimeItems := make([]*model.CampaignFlashSaleTimeItem, 0)
					for _, campaignSaleTime := range campaignSaleTimes {
						for _, saleTime := range campaignSaleTime.SaleTime {
							saleTimeItems = append(saleTimeItems, saleTime)
						}
					}
					for _, itemCode := range *tic.FlashSaleTimes {
						match := false
						matchProductID := false
						matchProductCategoryCode := false
						for _, saleTime := range saleTimeItems {
							if saleTime.Ref == itemCode {
								match = true
								flashSaleTime = append(flashSaleTime, saleTime)
								// flashSaleTimeRef = append(flashSaleTimeRef, saleTime.Ref)
								mapFlashSaleTimeRef[saleTime.Ref] = &saleTime.Ref

								if saleTime.ProductIDs != nil && len(*saleTime.ProductIDs) != 0 {
									for _, productID := range *saleTime.ProductIDs {
										if tic.ProductID == productID {
											matchProductID = true
										}
									}
								} else {
									matchProductID = true
								}
								if saleTime.CategoryCodes != nil && len(*saleTime.CategoryCodes) != 0 {
									for _, categoryCode := range *saleTime.CategoryCodes {
										if mapCategoryCode[categoryCode] {
											matchProductCategoryCode = true
										}
									}
								} else {
									matchProductCategoryCode = true
								}
							}
						}

						if !match {
							return &common.APIResponse{
								Status:    common.APIStatus.Invalid,
								Message:   "Flash sale time not match",
								ErrorCode: "FLASH_SALE_TIME_NOT_MATCH",
							}
						}
						if !matchProductID {
							return &common.APIResponse{
								Status:    common.APIStatus.Invalid,
								Message:   "Product not match",
								ErrorCode: "PRODUCT_NOT_MATCH",
							}
						}

						if !matchProductCategoryCode {
							return &common.APIResponse{
								Status:    common.APIStatus.Invalid,
								Message:   "Product category not match",
								ErrorCode: "PRODUCT_CATEGORY_NOT_MATCH",
							}
						}
					}
				}
			} else if campaign.CampaignType == enum.CampaignType.NORMAL {
				flashSaleTime := campaign.FlashSaleTimes[0].Detail[0]
				matchProductID := false
				matchProductCategoryCode := false
				if flashSaleTime.ProductIDs != nil && len(*flashSaleTime.ProductIDs) != 0 {
					for _, productID := range *flashSaleTime.ProductIDs {
						if tic.ProductID == productID {
							matchProductID = true
						}
					}
				} else {
					matchProductID = true
				}
				if flashSaleTime.CategoryCodes != nil && len(*flashSaleTime.CategoryCodes) != 0 {
					for _, categoryCode := range *flashSaleTime.CategoryCodes {
						if mapCategoryCode[categoryCode] {
							matchProductCategoryCode = true
						}
					}
				} else {
					matchProductCategoryCode = true
				}
				if !matchProductID {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   "Product not match",
						ErrorCode: "PRODUCT_NOT_MATCH",
					}
				}

				if !matchProductCategoryCode {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   "Product category not match",
						ErrorCode: "PRODUCT_CATEGORY_NOT_MATCH",
					}
				}
			}

			for k := range mapFlashSaleTimeRef {
				flashSaleTimeRef = append(flashSaleTimeRef, k)
			}

			product.FlashSaleTimes = flashSaleTime
			product.FlashSaleTime = flashSaleTimeRef

			if campaign.SaleType == enum.CampaignSaleType.ABSOLUTE {
				product.SaleType = enum.CampaignSaleType.ABSOLUTE
			} else if campaign.SaleType == enum.CampaignSaleType.PERCENTAGE {
				product.SaleType = enum.CampaignSaleType.PERCENTAGE
			} else {
				if tic.AbsoluteDiscount != nil && *tic.AbsoluteDiscount > 0 {
					product.SaleType = enum.CampaignSaleType.ABSOLUTE
				} else if tic.PercentageDiscount != nil && *tic.PercentageDiscount >= 0 {
					product.SaleType = enum.CampaignSaleType.PERCENTAGE
				}
			}

			// check data sku sale info in processing
			flagRes := cache.FlagDB.QueryOne(cache.Flag{
				IsProcessingSkuSaleInfo: &TRUE,
			})

			if flagRes.Status == common.APIStatus.Ok {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   "Đang xử lý dữ liệu, xin vui lòng đợi",
					ErrorCode: "IN_PROCESSING",
				}
			}

			isValidSku, errMess, errCode := isValidProductCampaignRegister(tic.Sku, campaign, nil)
			if !isValidSku {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   errMess,
					ErrorCode: errCode,
				}
			}

			if campaign.Reward != nil && campaign.SaleType != enum.CampaignSaleType.UNLIMIT {
				if product.SaleType == enum.CampaignSaleType.ABSOLUTE && product.AbsoluteDiscount != nil && campaign.Reward.AbsoluteDiscount != nil {
					if *product.AbsoluteDiscount < *campaign.Reward.AbsoluteDiscount {
						return &common.APIResponse{
							Status:    common.APIStatus.Invalid,
							Message:   "Giá trị giảm phải lớn hơn hoặc bằng số giá trị giảm của chương trình",
							ErrorCode: "ABSOLUTE_DISCOUNT_INVALID",
						}
					}
				}
				if product.SaleType == enum.CampaignSaleType.PERCENTAGE && product.PercentageDiscount != nil && campaign.Reward.PercentageDiscount != nil {
					if *product.PercentageDiscount < *campaign.Reward.PercentageDiscount {
						return &common.APIResponse{
							Status:    common.APIStatus.Invalid,
							Message:   "Số % giảm phải lớn hơn hoặc bằng số % giảm của chương trình",
							ErrorCode: "PERCENTAGE_DISCOUNT_INVALID",
						}
					}
				}
			}

			checkCampaignProductResult := model.CampaignProductDB.QueryOne(&model.CampaignProduct{
				CampaignCode: product.CampaignCode,
				SellerCode:   product.SellerCode,
				Sku:          product.Sku,
				Status:       enum.CampaignProductStatus.NORMAL,
			})
			if checkCampaignProductResult.Status == common.APIStatus.Ok {
				campaignProductResult := checkCampaignProductResult.Data.([]*model.CampaignProduct)[0]
				// chặn reset quantity
				if campaignProductResult.SoldQuantity != nil && *campaignProductResult.SoldQuantity > 0 {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   "Sản phẩm này đã tham gia chương trình này và đã có khách đặt hàng",
						ErrorCode: "QUANTITY_INVALID",
					}
				}

				// for _, saleTime := range campaignProductResult.FlashSaleTimes {
				// 	match := true
				// 	for _, newSaleTime := range flashSaleTime {
				// 		if saleTime.Ref == newSaleTime.Ref {
				// 			match = false
				// 			continue
				// 		}
				// 	}
				// 	if match {
				// 		flashSaleTime = append(flashSaleTime, saleTime)
				// 	}
				// }
				es := model.CampaignProductDB.UpdateOne(&model.CampaignProduct{
					CampaignCode: product.CampaignCode,
					SellerCode:   product.SellerCode,
					Sku:          product.Sku,
					Status:       enum.CampaignProductStatus.NORMAL,
				}, &model.CampaignProduct{
					FlashSaleTimes:      flashSaleTime,
					Price:               tic.Price,
					SalePrice:           tic.SalePrice,
					AbsoluteDiscount:    tic.AbsoluteDiscount,
					PercentageDiscount:  tic.PercentageDiscount,
					Quantity:            &tic.Quantity,
					MaxQuantityPerOrder: tic.MaxQuantityPerOrder,
					SoldQuantity:        &ZERO,
					MaxDiscount:         &tic.MaxDiscount,
					SaleType:            tic.SaleType,
					FlashSaleTime:       flashSaleTimeRef,
					Status:              enum.CampaignProductStatus.NORMAL,
					IsActive:            &TRUE,
					Note:                "Approved overwrite",
				})
				if es.Status != common.APIStatus.Ok {
					return es
				}

				// go func() {
				if campaign.IsActive != nil && *campaign.IsActive == true && (campaign.Status == enum.CampaignStatus.UPCOMING || campaign.Status == enum.CampaignStatus.PROCESSING) {
					WarmupSkuSaleInfo(&model.SkuSaleInfo{
						SkuCode:             product.Sku,
						CampaignCode:        &campaign.CampaignCode,
						CampaignProductCode: &campaignProductResult.CampaignProductCode,
						IsActive:            &TRUE,
						Status:              product.Status,
						LocationCodes:       campaign.Regions,
						CustomerScopeCodes:  campaign.CustomerScopes,
					})
				}
				// }()
			} else {
				uniqueSkuActive := fmt.Sprintf("%s_%s", product.CampaignCode, product.Sku)
				product.UniqueSkuActive = &uniqueSkuActive
				es := model.CampaignProductDB.Create(product)
				if es.Status != common.APIStatus.Ok {
					return es
				}

				// go func() {
				if campaign.IsActive != nil && *campaign.IsActive == true && (campaign.Status == enum.CampaignStatus.UPCOMING || campaign.Status == enum.CampaignStatus.PROCESSING) {
					WarmupSkuSaleInfo(&model.SkuSaleInfo{
						SkuCode:             product.Sku,
						Name:                campaign.CampaignName,
						CampaignCode:        &campaign.CampaignCode,
						CampaignProductCode: &product.CampaignProductCode,
						StartTime:           campaign.StartTime,
						EndTime:             campaign.EndTime,
						SaleType:            enum.SaleType.CAMPAIGN,
						IsActive:            &TRUE,
						Status:              product.Status,
						LocationCodes:       campaign.Regions,
						CustomerScopeCodes:  campaign.CustomerScopes,
					})
				}
				// }()
			}

			totalSku := int64(0)

			countSkuResult := model.CampaignProductDB.Distinct(&model.CampaignProduct{
				CampaignCode: campaign.CampaignCode,
			}, "sku")
			if countSkuResult.Status == common.APIStatus.Ok {
				totalSku = int64(len(countSkuResult.Data.([]interface{})))
			}

			_ = model.CampaignDB.UpdateOne(&model.Campaign{
				CampaignCode: campaign.CampaignCode,
			}, &model.Campaign{
				TotalProduct: &totalSku,
			})

			// go func() {
			// WarnUpAllCampaignProduct(campaign.CampaignCode)
			WarmUpCampaignProduct(product.Sku, campaign.CampaignCode, nil)
			// }()
		} else if in.Status == enum.TicketState.Rejected {
			updater.RejectedTime = &t
			updater.Note = in.Note
		}

		updateTicketRes := model.TicketDB.UpdateOne(&model.Ticket{
			TicketID:   in.TicketID,
			SellerCode: in.SellerCode,
			Status:     tic.Status,
		}, updater)
		if updateTicketRes.Status == common.APIStatus.Ok && updater.Status == enum.TicketState.Approved {
			go func() {
				client.Services.Notification.CreateNotification(
					&model.Notification{
						UserID:       tic.CreatedBy,
						Username:     fmt.Sprintf("SELLER_%d", tic.CreatedBy),
						ReceiverType: utils.ParseStringToPointer("SELLER"),
						Topic:        "ANNOUNCEMENT",
						Title:        "Congratulations, you have successfully participated in the promotion.",
						Description:  fmt.Sprintf("Campaign %s", campaign.CampaignName),
						Link:         fmt.Sprintf("/deals/campaigns/%s", campaign.CampaignCode),
					})
			}()
		}
		return updateTicketRes
	}

	return &common.APIResponse{
		Status:    common.APIStatus.Invalid,
		Message:   "Ticket has status invalid",
		ErrorCode: "STATUS_INVALID",
	}
}

func SellerUpdateTicket(accountID int64, in *model.SellerTicketUpdateRequest) *common.APIResponse {
	result := model.TicketDB.QueryOne(&model.Ticket{
		TicketID:   in.TicketID,
		SellerCode: in.SellerCode,
	})

	if result.Status != common.APIStatus.Ok {
		return result
	}

	tic := result.Data.([]*model.Ticket)[0]

	if tic.Status != enum.TicketState.WaitApprove {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Ticket has status invalid",
			ErrorCode: "STATUS_INVALID",
		}
	}

	if len(in.Status) > 0 && in.Status != enum.TicketState.WaitApprove && in.Status != enum.TicketState.Cancelled {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Ticket has status invalid",
			ErrorCode: "STATUS_INVALID",
		}
	}

	return model.TicketDB.UpdateOne(&model.Ticket{
		TicketID:   in.TicketID,
		SellerCode: in.SellerCode,
		Status:     tic.Status,
	}, in)
}

func CreateCampaignProduct(acc *model.Account, input *model.CampaignProductCreateRequest, checked bool, syncCampaign bool) *common.APIResponse {
	qCheck := model.CampaignDB.QueryOne(&model.Campaign{CampaignID: input.CampaignID, CampaignCode: input.CampaignCode})
	if qCheck.Status != common.APIStatus.Ok {
		return qCheck
	}
	campaign := qCheck.Data.([]*model.Campaign)[0]

	if campaign.SaleType != enum.CampaignSaleType.UNLIMIT && campaign.SaleType != input.SaleType {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Discount type does not match the program",
			ErrorCode: "SALE_TYPE_INVALID",
		}
	}
	if input.SaleType == enum.CampaignSaleType.ABSOLUTE {
		if input.AbsoluteDiscount == nil {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Please enter a discount value",
				ErrorCode: "ABSOLUTE_DISCOUNT_NOT_FOUND",
			}
		}
		if campaign.Reward.AbsoluteDiscount != nil && *campaign.Reward.AbsoluteDiscount > *input.AbsoluteDiscount {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "The reduction value must be greater than or equal to the program's reduction value",
				ErrorCode: "ABSOLUTE_DISCOUNT_INVALID",
			}
		}
	}
	if input.SaleType == enum.CampaignSaleType.PERCENTAGE {
		if input.PercentageDiscount == 0 {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Please enter % value",
				ErrorCode: "PERCENTAGE_NOT_FOUND",
			}
		}
		if campaign.Reward.PercentageDiscount != nil && *campaign.Reward.PercentageDiscount > input.PercentageDiscount {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "The % reduction must be greater than or equal to the % reduction of the program",
				ErrorCode: "PERCENTAGE_DISCOUNT_INVALID",
			}
		}
	}

	input.CampaignType = campaign.CampaignType
	if qCheck := model.CampaignProductDB.QueryOne(&model.CampaignProduct{CampaignID: input.CampaignID, Sku: input.Sku, Status: enum.CampaignProductStatus.NORMAL}); qCheck.Status == common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Existed,
			Message:   "This product already exists in campaign",
			ErrorCode: "SKU_EXISTED",
		}
	}
	if !checked {
		isValidSku, errMess, errCode := isValidProductCampaignRegister(input.Sku, campaign, nil)
		if !isValidSku {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   errMess,
				ErrorCode: errCode,
			}
		}
	}

	sku, err := client.Services.Product.GetSku(input.Sku)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "GET_SKU_FAIL",
		}
	}

	product, err := client.Services.Product.GetProduct(sku.ProductCode)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "GET_PRODUCT_FAIL",
		}
	}
	if product.SellerCategoryCode != "" {
		input.SellerCategoryCode = product.SellerCategoryCode
	}
	if product.SellerSubCategoryCode != nil {
		input.SellerSubCategoryCode = product.SellerSubCategoryCode
	}

	mapCategoryCode := make(map[string]bool)
	for _, categoryCode := range product.CategoryCodes {
		mapCategoryCode[categoryCode] = true
	}
	if !(sku.SellerCode == "BUYMED" || sku.SellerCode == "BUYMED" || sku.SellerCode == "MARKETING") {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Cannot add external seller products",
			ErrorCode: "INVALID_SELLER",
		}
	}

	if campaign.SaleType != enum.CampaignSaleType.UNLIMIT && input.SaleType == enum.CampaignSaleType.ABSOLUTE {
		if sku.RetailPriceValue < *input.AbsoluteDiscount {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "The reduced value must be less than or equal to the original price",
				ErrorCode: "ABSOLUTE_DISCOUNT_INVALID",
			}
		}
	}

	if sku.MaxQuantityPerOrder != 0 && sku.MaxQuantityPerOrder < input.MaxQuantityPerOrder {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   fmt.Sprintf("The maximum order quantity per order cannot exceed %d", sku.MaxQuantityPerOrder),
			ErrorCode: "MAX_QUANTITY_PER_ORDER_INVALID",
		}
	}
	if input.Quantity == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "The quantity sold must be greater than 0",
			ErrorCode: "QUANTITY_INVALID",
		}
	}
	input.CreatedBy = acc.AccountID
	input.IsActive = true
	if len(input.Status) == 0 {
		input.Status = enum.CampaignProductStatus.NORMAL
	}
	input.ProductID, input.ProductCode, input.SellerCode, input.Price = sku.ProductID, sku.ProductCode, sku.SellerCode, sku.RetailPriceValue

	if campaign.CampaignType == enum.CampaignType.FLASH_SALE {
		qCampaignSaleTime := model.CampaignSaleTimeDB.Query(&model.CampaignSaleTime{CampaignCode: campaign.CampaignCode}, 0, 0, nil)
		if qCampaignSaleTime.Status == common.APIStatus.Ok {
			campaignSaleTimes := qCampaignSaleTime.Data.([]*model.CampaignSaleTime)
			saleTimeItems := make([]*model.CampaignFlashSaleTimeItem, 0)
			for _, campaignSaleTime := range campaignSaleTimes {
				for _, saleTime := range campaignSaleTime.SaleTime {
					saleTimeItems = append(saleTimeItems, saleTime)
				}
			}
			if len(input.FlashSaleTime) == 0 {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   "Flash sale time not match",
					ErrorCode: "FLASH_SALE_TIME_NOT_MATCH",
				}
			}
			for _, itemCode := range input.FlashSaleTime {
				match := false
				matchProductID := false
				matchProductCategoryCode := false
				for _, saleTime := range saleTimeItems {
					if saleTime.Ref == itemCode {
						match = true
						input.FlashSaleTimes = append(input.FlashSaleTimes, saleTime)
						if saleTime.ProductIDs != nil && len(*saleTime.ProductIDs) != 0 {
							for _, productID := range *saleTime.ProductIDs {
								if input.ProductID == productID {
									matchProductID = true
								}
							}
						} else {
							matchProductID = true
						}
						if saleTime.CategoryCodes != nil && len(*saleTime.CategoryCodes) != 0 {
							for _, categoryCode := range *saleTime.CategoryCodes {
								if mapCategoryCode[categoryCode] {
									matchProductCategoryCode = true
								}
							}
						} else {
							matchProductCategoryCode = true
						}
					}

				}
				if !match {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   "Flash sale time not match",
						ErrorCode: "FLASH_SALE_TIME_NOT_MATCH",
					}
				}
				if !matchProductID {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   "Product not match",
						ErrorCode: "PRODUCT_NOT_MATCH",
					}
				}

				if !matchProductCategoryCode {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   "Product category not match",
						ErrorCode: "PRODUCT_CATEGORY_NOT_MATCH",
					}
				}
			}
		}
	} else {
		flashSaleTime := campaign.FlashSaleTimes[0].Detail[0]
		matchProductID := false
		matchProductCategoryCode := false
		if flashSaleTime.ProductIDs != nil && len(*flashSaleTime.ProductIDs) != 0 {
			for _, productID := range *flashSaleTime.ProductIDs {
				if input.ProductID == productID {
					matchProductID = true
				}
			}
		} else {
			matchProductID = true
		}
		if flashSaleTime.CategoryCodes != nil && len(*flashSaleTime.CategoryCodes) != 0 {
			for _, categoryCode := range *flashSaleTime.CategoryCodes {
				if mapCategoryCode[categoryCode] {
					matchProductCategoryCode = true
				}
			}
		} else {
			matchProductCategoryCode = true
		}
		if !matchProductID {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Product not match",
				ErrorCode: "PRODUCT_NOT_MATCH",
			}
		}

		if !matchProductCategoryCode {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Product category not match",
				ErrorCode: "PRODUCT_CATEGORY_NOT_MATCH",
			}
		}
	}

	input.CampaignProductID, input.CampaignProductCode = model.GenTicketID()

	uniqueSkuActive := fmt.Sprintf("%s_%s", input.CampaignCode, input.Sku)
	input.UniqueSkuActive = &uniqueSkuActive

	res := model.CampaignProductDB.Create(input)
	if res.Status == common.APIStatus.Ok {
		go func() {
			totalSku := int64(0)

			countSkuResult := model.CampaignProductDB.Distinct(&model.CampaignProduct{
				CampaignCode: campaign.CampaignCode,
			}, "sku")
			if countSkuResult.Status == common.APIStatus.Ok {
				totalSku = int64(len(countSkuResult.Data.([]interface{})))
			}

			_ = model.CampaignDB.UpdateOne(&model.Campaign{
				CampaignCode: campaign.CampaignCode,
			}, &model.Campaign{
				TotalProduct: &totalSku,
			})

			campaignPrd := res.Data.([]*model.CampaignProduct)[0]
			TRUE := true
			if campaign.IsActive != nil && *campaign.IsActive == true && (campaign.Status == enum.CampaignStatus.UPCOMING || campaign.Status == enum.CampaignStatus.PROCESSING) {
				WarmupSkuSaleInfo(&model.SkuSaleInfo{
					SkuCode:             campaignPrd.Sku,
					Name:                campaign.CampaignName,
					CampaignCode:        &campaign.CampaignCode,
					CampaignProductCode: &campaignPrd.CampaignProductCode,
					StartTime:           campaign.StartTime,
					EndTime:             campaign.EndTime,
					SaleType:            enum.SaleType.CAMPAIGN,
					IsActive:            &TRUE,
					Status:              campaignPrd.Status,
					LocationCodes:       campaign.Regions,
					CustomerScopeCodes:  campaign.CustomerScopes,
				})
			}

			WarmUpCampaignProduct(campaignPrd.Sku, campaign.CampaignCode, nil)
			if syncCampaign {
				WarnUpAllCampaignProduct(campaign.CampaignCode)
			}
		}()
	}
	return res
}

func UpdateCampaignProduct(acc *model.Account, input *model.CampaignProductUpdateRequest) *common.APIResponse {
	if input.CampaignProductCode == "" && input.CampaignProductID == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			Message:   "",
			ErrorCode: "MISSING_ID",
		}
	}
	query := &model.CampaignProduct{
		CampaignProductCode: input.CampaignProductCode,
		CampaignProductID:   input.CampaignProductID,
		Status:              enum.CampaignProductStatus.NORMAL}

	if input.IsActive != nil && *input.IsActive == false {
		TRUE := true
		query.IsActive = &TRUE
	}

	qCampaignProduct := model.CampaignProductDB.QueryOne(query)
	if qCampaignProduct.Status != common.APIStatus.Ok {
		return qCampaignProduct
	}
	campaignProduct := qCampaignProduct.Data.([]*model.CampaignProduct)[0]

	qCampaign := model.CampaignDB.QueryOne(&model.Campaign{
		CampaignCode: campaignProduct.CampaignCode,
	})
	if qCampaign.Status != common.APIStatus.Ok {
		return qCampaign
	}
	campaign := qCampaign.Data.([]*model.Campaign)[0]

	if input.IsActive != nil && *input.IsActive {
		TRUE := true
		flagRes := cache.FlagDB.QueryOne(cache.Flag{
			IsProcessingSkuSaleInfo: &TRUE,
		})

		// check data sku sale info in processing
		if flagRes.Status == common.APIStatus.Ok {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Đang xử lý dữ liệu, xin vui lòng đợi",
				ErrorCode: "IN_PROCESSING",
			}
		}

		if campaignProduct.SellerCode != "BUYMED" && campaignProduct.SellerCode != "BUYMED" && campaignProduct.SellerCode != "MARKETING" {
			isValidSku, errMsg, errCode := isValidSkuFulfill(campaignProduct.Sku, campaign)
			if !isValidSku {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   errMsg,
					ErrorCode: errCode,
				}
			}
		}
	}

	if input.IsActive != nil && *input.IsActive && campaign.IsActive != nil && *campaign.IsActive {
		isValidIsActive, errors := isValidCampaignTurnOn(campaign, campaignProduct.CampaignProductCode)
		if !isValidIsActive {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Có sản phẩm đang hoạt động trong chương trình khác",
				ErrorCode: "CONFLICT_CAMPAIGN",
				Data:      errors,
			}
		}
		input.PrivateNote = ""
	}
	if input.Sku != "" {
		sku, err := client.Services.Product.GetSku(input.Sku)
		if err != nil {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   err.Error(),
				ErrorCode: "GET_SKU_FAIL",
			}
		}
		maxQuantityPerOrder := campaignProduct.MaxQuantityPerOrder
		if input.MaxQuantityPerOrder != nil {
			maxQuantityPerOrder = input.MaxQuantityPerOrder
		}
		if sku.MaxQuantityPerOrder != 0 && maxQuantityPerOrder != nil && sku.MaxQuantityPerOrder < *maxQuantityPerOrder {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   fmt.Sprintf("Số lượng đặt tối đa trên 1 đơn hàng không được vượt quá %d", sku.MaxQuantityPerOrder),
				ErrorCode: "MAX_QUANTITY_PER_ORDER_INVALID",
			}
		}
		input.ProductID, input.ProductCode, input.SellerCode, input.Price = sku.ProductID, sku.ProductCode, sku.SellerCode, sku.RetailPriceValue
	}

	if input.FlashSaleTime != nil {
		qCampaignSaleTime := model.CampaignSaleTimeDB.Query(&model.CampaignSaleTime{CampaignCode: campaign.CampaignCode}, 0, 0, nil)
		if qCampaignSaleTime.Status == common.APIStatus.Ok {
			campaignSaleTimes := qCampaignSaleTime.Data.([]*model.CampaignSaleTime)
			saleTimeItems := make([]*model.CampaignFlashSaleTimeItem, 0)
			for _, campaignSaleTime := range campaignSaleTimes {
				for _, saleTime := range campaignSaleTime.SaleTime {
					saleTimeItems = append(saleTimeItems, saleTime)
				}
			}
			for _, itemCode := range *input.FlashSaleTime {
				match := false
				for _, saleTime := range saleTimeItems {
					if saleTime.Ref == itemCode {
						match = true
						input.FlashSaleTimes = append(input.FlashSaleTimes, saleTime)
						continue
					}
				}
				if !match {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   "Flash sale time not match",
						ErrorCode: "FLASH_SALE_TIME_NOT_MATCH",
					}
				}
			}
		}
	}
	res := model.CampaignProductDB.UpdateOne(query, input)
	if res.Status == common.APIStatus.Ok {
		go func() {
			campaignPrd := res.Data.([]*model.CampaignProduct)[0]
			isActive := false

			if campaignPrd.IsActive != nil && *campaignPrd.IsActive == true && campaignPrd.Status == enum.CampaignProductStatus.NORMAL {
				isActive = true
			}

			WarmupSkuSaleInfo(&model.SkuSaleInfo{
				SkuCode:             campaignPrd.Sku,
				CampaignCode:        &campaign.CampaignCode,
				CampaignProductCode: &campaignPrd.CampaignProductCode,
				IsActive:            &isActive,
				Status:              campaignPrd.Status,
				LocationCodes:       campaign.Regions,
				CustomerScopeCodes:  campaign.CustomerScopes,
			})
			WarmUpCampaignProduct(campaignPrd.Sku, campaign.CampaignCode, nil)
			WarnUpAllCampaignProduct(campaign.CampaignCode)
		}()
	}

	if input.Status == enum.CampaignProductStatus.CANCELLED {
		updateData := bson.M{
			"unique_sku_active": nil,
		}

		return model.CampaignProductDB.UpdateOneWithOption(&model.CampaignProduct{
			CampaignProductID: query.CampaignProductID,
		}, bson.M{
			"$unset": updateData,
		})
	}

	return res
}

func DeleteCampaignProduct(acc *model.Account, code string, id int64) *common.APIResponse {
	if code == "" && id == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			Message:   "No products found in campaign",
			ErrorCode: "MISSING_ID",
		}
	}
	query := &model.CampaignProduct{CampaignProductCode: code, CampaignProductID: id}
	qCheck := model.CampaignProductDB.QueryOne(query)
	if qCheck.Status != common.APIStatus.Ok {
		return qCheck
	}
	result := model.CampaignProductDB.Delete(query)
	if result.Status == common.APIStatus.Ok {
		campaignPrd := qCheck.Data.([]*model.CampaignProduct)[0]
		model.SkuSaleInfoCacheDB.Delete(&model.SkuSaleInfo{
			SkuCode:             campaignPrd.Sku,
			CampaignCode:        &campaignPrd.CampaignCode,
			CampaignProductCode: &campaignPrd.CampaignProductCode,
		})

		totalSku := int64(0)

		countSkuResult := model.CampaignProductDB.Distinct(&model.CampaignProduct{
			CampaignCode: campaignPrd.CampaignCode,
		}, "sku")
		if countSkuResult.Status == common.APIStatus.Ok {
			totalSku = int64(len(countSkuResult.Data.([]interface{})))
		}

		_ = model.CampaignDB.UpdateOne(&model.Campaign{
			CampaignCode: campaignPrd.CampaignCode,
		}, &model.Campaign{
			TotalProduct: &totalSku,
		})
	}

	return result
}

func CheckCampaignWithCart(acc *model.Account, cart *model.Cart) *common.APIResponse {
	skus := make([]string, 0)
	campaignCodes := make([]string, 0)
	mapProductCampaign := make(map[string][]*model.CampaignProduct)
	mapCampaign := make(map[string]*model.Campaign)

	type data struct {
		SKU          string `json:"sku"`
		Type         string `json:"type"`
		ErrorMessage string `json:"errorMessage"`
		ErrorCode    string `json:"errorCode"`
		IsValid      bool   `json:"isValid"`
		CampaignCode string `json:"campaignCode"`
	}
	dataRes := make([]*data, 0)

	t := true
	qCampaign := model.CampaignDB.Query(model.Campaign{IsActive: &t, ComplexQuery: []*bson.M{
		{
			"$or": []*bson.M{
				{
					"regions": bson.M{"$in": cart.RegionCodes},
				},
				{
					"regions": bson.M{"$size": 0},
				},
			},
		},
		{
			"$or": []*bson.M{
				{
					"customer_scopes": cart.CustomerScope,
				},
				{
					"customer_scopes": bson.M{"$size": 0},
				},
			},
		},
	}}, 0, 0, nil)
	if qCampaign.Status == common.APIStatus.Ok {
		for _, campaign := range qCampaign.Data.([]*model.Campaign) {
			mapCampaign[campaign.CampaignCode] = campaign
			campaignCodes = append(campaignCodes, campaign.CampaignCode)
		}
	}
	for _, item := range cart.CartItems {
		skus = append(skus, item.ProductSKU)
	}
	qProductCampaign := model.CampaignProductDB.Query(model.CampaignProduct{ComplexQuery: []*bson.M{
		{
			"sku":           bson.M{"$in": skus},
			"campaign_code": bson.M{"$in": campaignCodes},
			"is_active":     true,
		},
	}, Status: enum.CampaignProductStatus.NORMAL}, 0, 0, nil)
	if qProductCampaign.Status == common.APIStatus.Ok {
		for _, pCampaign := range qProductCampaign.Data.([]*model.CampaignProduct) {
			mapProductCampaign[pCampaign.Sku] = append(mapProductCampaign[pCampaign.Sku], pCampaign)
		}
	}

	for _, item := range cart.CartItems {
		itemRes := &data{
			SKU:          item.ProductSKU,
			Type:         item.Type,
			IsValid:      false,
			ErrorMessage: "Campaign not found",
			ErrorCode:    "CAMPAIGN_NOT_FOUND",
		}
		if pCampaigns, ok := mapProductCampaign[item.ProductSKU]; ok {
			for _, pCampaign := range pCampaigns {
				isValid, errMessage := isValidProductCampaign(&item, pCampaign, mapCampaign[pCampaign.CampaignCode])
				if isValid {
					isValidCampaign, errMessage := isValidCampaign(cart, mapCampaign[pCampaign.CampaignCode])
					if isValidCampaign {
						itemRes.IsValid = true
						itemRes.ErrorCode = ""
						itemRes.ErrorMessage = ""
						itemRes.CampaignCode = pCampaign.CampaignCode
						break
					} else {
						itemRes.ErrorCode = "INVALID_CAMPAIGN"
						itemRes.ErrorMessage = errMessage
					}
				} else {
					itemRes.ErrorCode = "INVALID_PRODUCT_CAMPAIGN"
					itemRes.ErrorMessage = errMessage
				}
			}
		}
		dataRes = append(dataRes, itemRes)
	}
	return &common.APIResponse{
		Status: common.APIStatus.Ok,
		Data:   dataRes,
	}
}

func isValidCampaign(cart *model.Cart, campaign *model.Campaign) (bool, string) {
	if campaign == nil {
		return false, "Không tìm thấy chương trình khuyến mãi"
	}
	t := time.Now()
	if campaign.StartTime.After(t) {
		return false, fmt.Sprintf("Chương trình khuyến mãi %s này chưa được áp dụng", strings.ToLower(campaign.CampaignName))
	}

	if campaign.EndTime.Before(t) {
		return false, fmt.Sprintf("Chương trình khuyến mãi %s đã hết hạn", strings.ToLower(campaign.CampaignName))
	}

	if campaign.IsActive != nil && !*campaign.IsActive {
		return false, fmt.Sprintf("Chương trình khuyến mãi %s hiện đang tắt", strings.ToLower(campaign.CampaignName))
	}

	//isValidRegion := false
	//if campaign.Regions != nil && len(*campaign.Regions) > 0 {
	//	for _, region := range *campaign.Regions {
	//		if cart.RegionCode == region {
	//			isValidRegion = true
	//		}
	//	}
	//} else {
	//	isValidRegion = true
	//}
	//
	//if !isValidRegion {
	//	return false, fmt.Sprintf("Chương trình khuyến mãi %s không áp dụng ở khu vực này", strings.ToLower(campaign.CampaignName))
	//}

	return true, ""
}

func isValidProductCampaign(cart *model.CartItemInternal, productCampaign *model.CampaignProduct, campaign *model.Campaign) (bool, string) {
	isValidFlashSaleTime := false
	mapFlashSaleTime := make(map[string]*model.FlashSaleTime)
	for _, flashSaleTime := range campaign.FlashSaleTimes {
		mapFlashSaleTime[flashSaleTime.Code] = flashSaleTime
	}
	if productCampaign.FlashSaleTimes != nil {
		now := time.Now()
		for _, flashSaleTime := range productCampaign.FlashSaleTimes {
			if flashSaleTime.StartTime.Before(now) && flashSaleTime.EndTime.After(now) {
				isValidFlashSaleTime = true
				break
			}
		}
	} else {
		isValidFlashSaleTime = true
	}

	if productCampaign.SoldQuantity != nil && productCampaign.Quantity != nil && *productCampaign.SoldQuantity >= *productCampaign.Quantity {
		return false, fmt.Sprintf("Sản phẩm này đã bán hết")
	}

	if !isValidFlashSaleTime {
		return false, fmt.Sprintf("Chương trình khuyến mãi chưa được mở hoặc đã kết thúc")
	}

	return true, ""
}

func CheckSyncCampaign() {
	if conf.Config.Env == "uat" {
		return
	}

	t := time.Now()
	// fmt.Println("START CheckSyncCampaign ver=", t.Unix())

	results := model.CampaignDB.Query(
		bson.M{
			"$or": []*bson.M{
				{
					"registration_end_time": bson.M{
						"$lt": t,
					},
				},
				{
					"start_time": bson.M{
						"$lt": t,
					},
				},
			},
			"status": bson.M{
				"$in": []string{"NEW",
					"UPCOMING",
					"PROCESSING",
					"EXPIRED",
				},
			},
			// "is_active": true,
		},
		0, 0, &primitive.M{"last_updated_time": 1},
	)

	if results.Status == common.APIStatus.Ok {
		campaigns := results.Data.([]*model.Campaign)
		for _, item := range campaigns {
			// haveUpdate, turnOff := false, false
			if time.Now().After(item.StartTime) && time.Now().Before(item.EndTime) {
				// fmt.Printf("[1] Campaign %s,%s -> %s\n", item.CampaignCode, item.Status, enum.CampaignStatus.PROCESSING)
				// haveUpdate = true
				updater := &model.Campaign{
					Status: enum.CampaignStatus.PROCESSING,
				}

				if string(item.Status) == string(updater.Status) {
					continue
				}

				if item.Status != enum.CampaignStatus.PROCESSING || len(item.Version) == 0 {
					updater.Version = fmt.Sprint(time.Now().Unix())
				}
				_ = model.CampaignDB.UpdateOne(&model.Campaign{
					CampaignCode: item.CampaignCode,
					Status:       item.Status,
				}, updater)
				WarmUpCampaign(item.CampaignCode, nil)
				WarmupSkuSaleInfoByCampaign(item)
			} else if time.Now().After(item.EndTime) {
				// fmt.Printf("[2] Campaign %s,%s -> %s\n", item.CampaignCode, item.Status, enum.CampaignStatus.EXPIRED)
				// haveUpdate = true
				updater := &model.Campaign{
					Status: enum.CampaignStatus.EXPIRED,
				}

				if string(item.Status) == string(updater.Status) {
					continue
				}

				if item.Status != enum.CampaignStatus.EXPIRED || len(item.Version) == 0 {
					updater.Version = fmt.Sprint(time.Now().Unix())
				}
				_ = model.CampaignDB.UpdateOne(&model.Campaign{
					CampaignCode: item.CampaignCode,
					Status:       item.Status,
				}, updater)
				WarmUpCampaign(item.CampaignCode, nil)
				WarmupSkuSaleInfoByCampaign(item)

			} else {
				// fmt.Printf("[3] Campaign %s,%s -> %s\n", item.CampaignCode, item.Status, enum.CampaignStatus.UPCOMING)
				// haveUpdate = true
				updater := &model.Campaign{
					Status: enum.CampaignStatus.UPCOMING,
				}

				if string(item.Status) == string(updater.Status) {
					continue
				}

				if item.Status != enum.CampaignStatus.UPCOMING || len(item.Version) == 0 {
					updater.Version = fmt.Sprint(time.Now().Unix())
				}
				_ = model.CampaignDB.UpdateOne(&model.Campaign{
					CampaignCode: item.CampaignCode,
					Status:       item.Status,
				}, updater)
				WarmUpCampaign(item.CampaignCode, nil)
				WarmupSkuSaleInfoByCampaign(item)
				continue
			}

			if item.NeedCheck != nil && len(*item.NeedCheck) > 0 {
				// fmt.Println("[4]")
				// haveUpdate = true
				// turnOff = true
				EMPTY := ""
				_ = model.CampaignDB.UpdateOne(&model.Campaign{
					CampaignCode: item.CampaignCode,
				}, &model.Campaign{
					NeedCheck: &EMPTY,
					Version:   fmt.Sprint(time.Now().Unix()),
				})
				WarmUpCampaign(item.CampaignCode, nil)
				WarmupSkuSaleInfoByCampaign(item)
			}

			// if item.IsActive != nil && *item.IsActive == false && turnOff == false {
			// 	continue
			// }

			// if !haveUpdate || item.Status == enum.CampaignStatus.EXPIRED {
			// 	continue
			// }

			// syncCampaignProduct(item, turnOff)
		}
	}
	// fmt.Printf("END CheckSyncCampaign ver=%d, after %v\n", t.Unix(), time.Since(t))
}

// syncCampaignProduct ...
func syncCampaignProduct(item *model.Campaign, turnOff bool) {
	offset := int64(0)
	limit := int64(1000)
	for {
		productResult := model.CampaignProductDB.Query(&model.CampaignProduct{
			CampaignCode: item.CampaignCode,
		}, offset*limit, limit, nil)

		if productResult.Status != common.APIStatus.Ok {
			return
		}

		updater := &model.UpdateSkuCampaignRequest{
			Page: offset,
		}
		lstSku := make([]*model.UpdateSkuCampaignItem, 0)

		if productResult.Status != common.APIStatus.Ok {
			// reset sku if exsisted
			updater.CampaignCode = item.CampaignCode
			updater.ListSku = lstSku
		} else {
			products := productResult.Data.([]*model.CampaignProduct)
			mapCheckProducts := make(map[string]*model.CampaignProduct)
			for _, product := range products {
				if product.IsActive != nil && *product.IsActive == false {
					continue
				}
				if mapCheckProducts[product.Sku] == nil || mapCheckProducts[product.Sku].Status == enum.CampaignProductStatus.CANCELLED && product.Status != enum.CampaignProductStatus.CANCELLED {
					mapCheckProducts[product.Sku] = product
				}
			}

			for _, product := range mapCheckProducts {
				lstSku = append(lstSku, &model.UpdateSkuCampaignItem{
					Sku:          product.Sku,
					SellerCode:   product.SellerCode,
					ProductID:    product.ProductID,
					Quantity:     product.Quantity,
					SoldQuantity: product.SoldQuantity,
				})
			}

			if turnOff || len(lstSku) <= 0 || item.Status == enum.CampaignStatus.EXPIRED {
				updater.ListSku = lstSku
			} else {
				updater.ListSku = lstSku
				updater.CampaignCode = item.CampaignCode
			}
		}

		// err := client.Services.Product.UpdateSku(updater)
		// if err != nil {
		// }
		offset++
	}
}

func UpdateSoldQuantityFromOrder(acc *model.Account, req *model.UpdateSoldQuantityFromOrderRequest) *common.APIResponse {
	query := model.CampaignProduct{CampaignCode: req.CampaignCode, Sku: req.Sku, Status: enum.CampaignProductStatus.NORMAL}
	qCampaignProduct := model.CampaignProductDB.QueryOne(query)
	if qCampaignProduct.Status != common.APIStatus.Ok {
		return qCampaignProduct
	}
	campaignProduct := qCampaignProduct.Data.([]*model.CampaignProduct)[0]

	res := model.CampaignProductDB.UpdateOne(query, model.CampaignProduct{SoldQuantity: &req.Quantity})
	if res.Status == common.APIStatus.Ok {
		WarmUpCampaignProduct(campaignProduct.Sku, campaignProduct.CampaignCode, nil)
		go func() {
			model.CampaignHistoryDB.Create(&model.CampaignHistory{
				CustomerID:          req.CustomerId,
				OrderID:             req.OrderId,
				Quantity:            int(req.CheckoutQuantity),
				CampaignCode:        req.CampaignCode,
				CampaignProductCode: campaignProduct.CampaignProductCode,
				Sku:                 req.Sku,
			})
		}()
	}
	return res
}

func GetListCampaignHistory(query *model.CampaignHistory, offset, limit int64, getTotal bool) *common.APIResponse {
	result := model.CampaignHistoryDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if result.Status != common.APIStatus.Ok {
		return result
	}
	if getTotal {
		result.Total = model.CampaignHistoryDB.Count(query).Total
	}
	return result
}

func SyncSkuInfoInCampaign(sku string) *common.APIResponse {
	skuData, err := client.Services.Product.GetSku(sku)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: err.Error(),
		}
	}
	skuLocations := make([]map[string]int, 0)
	skuLocationMap := make(map[string]int)
	for _, location := range skuData.LocationCodes {
		skuLocations = append(skuLocations, map[string]int{location: 1})
		skuLocationMap[location] = 1
	}
	updater := model.CampaignProduct{}
	updater.LocationCodeMap = skuLocationMap
	if skuData.Status != nil {
		updater.StatusPriority = enum.SortPrioritySkuStatus[*skuData.Status]
	}
	return model.CampaignProductCacheDB.UpdateMany(&model.CampaignProduct{Sku: sku}, updater)
}

func WarmUpCampaign(campaignCode string, campaign *model.Campaign) {
	query := &model.Campaign{
		CampaignCode: campaignCode,
	}

	if campaign == nil {
		result := model.CampaignDB.QueryOne(query)
		if result.Status != common.APIStatus.Ok {
			return
		}
		campaign = result.Data.([]*model.Campaign)[0]
		if campaign.CampaignType == enum.CampaignType.FLASH_SALE {
			getCampaignFlashSaleTimeItems([]*model.Campaign{campaign})
		}
	}

	isRemove, isUpdate := false, false
	if campaign.IsActive != nil && !*campaign.IsActive || campaign.Status == enum.CampaignStatus.EXPIRED {
		isRemove = true
	}

	if isRemove {
		_ = model.CampaignCacheDB.Delete(query)
		isUpdate = true
	}

	if campaign.LastUpdatedTime != nil {
		isUpdate = true
	}

	if isUpdate && len(campaign.Version) > 0 {
		// update cache
		_ = model.CampaignCacheDB.Upsert(query, campaign)

		checkCampaignProductResult := model.CampaignProductCacheDB.QueryOne(bson.M{
			"campaign.version": bson.M{
				"$ne": campaign.Version,
			},
			"campaign_code": campaign.CampaignCode,
		})
		if checkCampaignProductResult.Status == common.APIStatus.Ok {
			// update object campaign embed in campaign product
			_ = model.CampaignProductCacheDB.UpdateMany(model.CampaignProduct{CampaignCode: campaignCode}, model.CampaignProduct{Campaign: campaign})
			// fmt.Println("Update campaign product - OK")
			return
		}
		// fmt.Println("Skip update campaign product - SKIP")
	}
}

func WarnUpAllCampaignProduct(campaignCoce string) {
	offset := int64(0)
	limit := int64(1000)
	mapProducts := make(map[string]int)
	lstSku := make(map[string]*model.CampaignProduct)
	query := &model.CampaignProduct{
		CampaignCode: campaignCoce,
	}
	for {
		result := model.CampaignProductDB.Query(query, offset*limit, limit, nil)
		if result.Status != common.APIStatus.Ok {
			fmt.Println("WarmUpAllCampaignProduct", campaignCoce, result.Message)
			break
		}
		products := result.Data.([]*model.CampaignProduct)

		for _, item := range products {
			lstSku[item.Sku] = item
			if (item.IsActive != nil && *item.IsActive == false) || item.Status == enum.CampaignProductStatus.CANCELLED {
				continue
			}
			mapProducts[item.Sku] = 1
		}
		offset++
	}

	offset2 := int64(0)
	limit2 := int64(1000)
	for {
		resultCache := model.CampaignProductCacheDB.Query(query, offset2*limit2, limit2, nil)
		if resultCache.Status != common.APIStatus.Ok {
			fmt.Println("WarmUpAllCampaignProduct CampaignProductCacheDB", campaignCoce, resultCache.Message)
			break
		}
		products := resultCache.Data.([]*model.CampaignProduct)
		for _, item := range products {
			// cache dư sản phẩm
			if mapProducts[item.Sku] < 1 {
				_ = model.CampaignProductCacheDB.Delete(&model.CampaignProduct{
					Sku:          item.Sku,
					CampaignCode: campaignCoce,
				})
			}
		}
		offset2++
	}
	for _, item := range lstSku {
		WarmUpCampaignProduct(item.Sku, item.CampaignCode, nil)
	}
}

func WarmUpCampaignProduct(sku string, campaignCode string, campaignProduct *model.CampaignProduct) {
	query := &model.CampaignProduct{
		Sku:          sku,
		CampaignCode: campaignCode,
	}

	skuData, err := client.Services.Product.GetSkuMain(sku)
	if err != nil {
		fmt.Println("WarmUpCampaignProduct > GetSkuMain", sku, err.Error())
		return
	}

	// get Product Info
	// TODO: B2B-397: warm up data seller category code
	productData, _ := client.Services.Product.GetProduct(skuData.Code)

	if productData != nil {
		// product update info
		// fmt.Println("warm up campaign product")
		campaignProduct.ProductCode = productData.Code
		campaignProduct.SellerCategoryCode = productData.SellerCategoryCode
		campaignProduct.SellerSubCategoryCode = productData.SellerSubCategoryCode

		if campaignProduct.SellerCategoryCode != "" {
			campaignProduct.FilterRaw = append(campaignProduct.FilterRaw, &model.DataRaw{
				Type: "seller_category_code",
				Val:  campaignProduct.SellerCategoryCode,
			})
		}

		if campaignProduct.SellerSubCategoryCode != nil && *campaignProduct.SellerSubCategoryCode != "" {
			campaignProduct.FilterRaw = append(campaignProduct.FilterRaw, &model.DataRaw{
				Type: "seller_sub_category_code",
				Val:  *campaignProduct.SellerSubCategoryCode,
			})
		}
	}

	if campaignProduct == nil {
		result := model.CampaignProductDB.Query(query, 0, 0, nil)
		if result.Status != common.APIStatus.Ok {
			return
		}
		campaignProducts := result.Data.([]*model.CampaignProduct)
		for _, item := range campaignProducts {
			if (item.IsActive != nil && *item.IsActive == true) && item.Status == enum.CampaignProductStatus.NORMAL {
				campaignProduct = item
				qCampaign := model.CampaignDB.QueryOne(model.Campaign{CampaignCode: campaignCode})
				if qCampaign.Status == common.APIStatus.Ok {
					campaignProduct.Campaign = qCampaign.Data.([]*model.Campaign)[0]
				}
				break
			}
		}

		if campaignProduct == nil {
			_ = model.CampaignProductCacheDB.Delete(query)
			return
		}
	}
	skuLocations := make([]map[string]int, 0)
	skuLocationMap := make(map[string]int)
	areaCodesRaw := make([]string, 0)
	locationSkuRaw := make([]string, 0)

	for _, skuItem := range skuData.SkuItems {
		if skuItem.LocationCodes != nil && skuItem.IsActive != nil && *skuItem.IsActive &&
			skuItem.Status != nil && (*skuItem.Status == enum.SkuStatus.NORMAL || *skuItem.Status == enum.SkuStatus.LIMIT) {
			for _, location := range *skuItem.LocationCodes {
				tmp := location
				skuLocations = append(skuLocations, map[string]int{location: 1})
				skuLocationMap[location] = 1
				if tmp == "ALL" {
					tmp = "00"
				}
				if len(mapRegionCache[tmp]) > 0 {
					locationSkuRaw = append(locationSkuRaw, mapRegionCache[tmp]...)
					continue
				}
				locationSkuRaw = append(locationSkuRaw, tmp)
			}
		}
	}
	locationSkuRaw = uniqueSliceString(locationSkuRaw)

	// region
	if campaignProduct.Campaign != nil && campaignProduct.Campaign.Regions != nil {
		if len(*campaignProduct.Campaign.Regions) == 0 {
			areaCodesRaw = append(areaCodesRaw, mapRegionCache["00"]...)
		} else {
			for _, region := range *campaignProduct.Campaign.Regions {
				areaCodesRaw = append(areaCodesRaw, mapRegionCache[region]...)
			}
		}
		areaCodesRaw = uniqueSliceString(areaCodesRaw)
	}

	// customer scope
	scopeCodesRaw := make([]string, 0)
	if campaignProduct.Campaign != nil && campaignProduct.Campaign.CustomerScopes != nil {
		if len(*campaignProduct.Campaign.CustomerScopes) == 0 {
			scopeCodesRaw = append(scopeCodesRaw, mapScopeCache["ALL"]...)
		} else {
			for _, level := range *campaignProduct.Campaign.CustomerScopes {
				scopeCodesRaw = append(scopeCodesRaw, mapScopeCache[level]...)
			}
		}
		scopeCodesRaw = uniqueSliceString(scopeCodesRaw)
	}
	campaignProduct.FilterRaw = make([]*model.DataRaw, 0)
	for _, item := range areaCodesRaw {
		campaignProduct.FilterRaw = append(campaignProduct.FilterRaw, &model.DataRaw{
			Type: "area_code",
			Val:  item,
		})
	}
	for _, item := range scopeCodesRaw {
		campaignProduct.FilterRaw = append(campaignProduct.FilterRaw, &model.DataRaw{
			Type: "customer_scope",
			Val:  item,
		})
	}
	for _, item := range locationSkuRaw {
		campaignProduct.FilterRaw = append(campaignProduct.FilterRaw, &model.DataRaw{
			Type: "location_sku",
			Val:  item,
		})
	}

	// range price
	priceRange, _ := client.Services.Product.GetPriceRange()
	if len(priceRange) > 0 && campaignProduct.SalePrice > 0 {
		// compare
		for _, item := range priceRange {
			if campaignProduct.SalePrice >= item.MinPrice && (campaignProduct.SalePrice <= item.MaxPrice || item.MaxPrice == 0) {
				campaignProduct.FilterRaw = append(campaignProduct.FilterRaw, &model.DataRaw{
					Type: "price_range",
					Val:  item.Code,
				})
				campaignProduct.PriceRange = item.Code
				break
			}
		}

	}

	campaignProduct.LocationCodeMap = skuLocationMap
	if skuData.Status != nil {
		campaignProduct.StatusPriority = enum.SortPrioritySkuStatus[*skuData.Status]
	}
	// update cache
	model.CampaignProductCacheDB.Upsert(query, campaignProduct)
	client.Services.Order.UpdateDealApplyResult(model.DealApplyRequest{
		SKU:         campaignProduct.Sku,
		Quantity:    campaignProduct.SoldQuantity,
		MaxQuantity: campaignProduct.Quantity,
		Type:        "CAMPAIGN",
		DealCode:    campaignProduct.CampaignCode,
	})
}

func WarnUpAllCampaign() {
	offset := int64(0)
	limit := int64(200)
	for true {
		result := model.CampaignDB.Query(&model.Campaign{IsActive: utils.ParseBoolToPointer(true), ComplexQuery: []*bson.M{
			{
				"status": bson.M{"$ne": enum.CampaignStatus.EXPIRED},
			},
		}}, offset, limit, &primitive.M{"_id": -1})
		if result.Status == common.APIStatus.Ok {
			list := result.Data.([]*model.Campaign)
			for _, item := range list {
				WarmUpCampaign(item.CampaignCode, nil)
				WarnUpAllCampaignProduct(item.CampaignCode)
			}
		} else {
			return
		}
		offset += limit
	}
}

func checkValidCampaign(campaign *model.Campaign) bool {
	if campaign == nil {
		return false
	}

	if campaign.Status == enum.CampaignStatus.EXPIRED {
		return false
	}
	if time.Now().Before(campaign.StartTime) || time.Now().Before(campaign.EndTime) {
		return false
	}

	return true
}

func GetBeginDay(t time.Time) time.Time {
	t1 := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, time.UTC).Add(-7 * time.Hour)
	return t1
}

func GetLastDay(t time.Time) *time.Time {
	t1 := time.Date(t.Year(), t.Month(), t.Day(), 23, 59, 59, 0, time.UTC).Add(-7 * time.Hour)
	return &t1
}

func CheckProductCampaignRegister(sku, code string) *common.APIResponse {
	qCampaign := model.CampaignDB.QueryOne(&model.Campaign{CampaignCode: code})
	if qCampaign.Status != common.APIStatus.Ok {
		return qCampaign
	}

	campaign := qCampaign.Data.([]*model.Campaign)[0]
	// if campaign.Status == enum.CampaignStatus.EXPIRED {
	// 	return &common.APIResponse{
	// 		Status:  common.APIStatus.Ok,
	// 		Message: "Đăng kí sản phẩm hợp lệ",
	// 	}
	// }

	isValid, errMess, errCode := isValidProductCampaignRegister(sku, campaign, nil)
	if isValid {
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "Đăng kí sản phẩm hợp lệ",
		}
	} else {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   errMess,
			ErrorCode: errCode,
		}
	}
}

func getLocationCodeRaw(regions *[]string) []string {
	locationCodeRaw := make([]string, 0)
	if regions != nil && len(*regions) > 0 {
		for _, region := range *regions {
			tmp := region
			if len(mapRegionCache[tmp]) > 0 {
				locationCodeRaw = append(locationCodeRaw, mapRegionCache[tmp]...)
				continue
			}
			locationCodeRaw = append(locationCodeRaw, tmp)
		}
	} else if regions != nil && len(*regions) == 0 {
		tmp := "00"
		if len(mapRegionCache[tmp]) > 0 {
			locationCodeRaw = append(locationCodeRaw, mapRegionCache[tmp]...)
		}
		locationCodeRaw = append(locationCodeRaw, tmp)
	}
	return uniqueSliceString(locationCodeRaw)
}

func getCustomerScopeRaw(customerScopes *[]string) []string {
	scopeCodesRaw := make([]string, 0)
	if customerScopes != nil && len(*customerScopes) > 0 {
		for _, region := range *customerScopes {
			if len(mapScopeCache[region]) > 0 {
				scopeCodesRaw = append(scopeCodesRaw, mapScopeCache[region]...)
				continue
			}
		}
	} else if customerScopes != nil && len(*customerScopes) == 0 {
		tmp := "ALL"
		if len(mapScopeCache[tmp]) > 0 {
			scopeCodesRaw = append(scopeCodesRaw, mapScopeCache[tmp]...)
		}
		scopeCodesRaw = append(scopeCodesRaw, tmp)
	}
	return uniqueSliceString(scopeCodesRaw)
}

func isValidWithOtherCampaign(sku string, campaign *model.Campaign) (bool, string, string) {
	TRUE := true
	errCode := "CONFLICT_CAMPAIGN_PRODUCT"
	locationCodeRaw := getLocationCodeRaw(campaign.Regions)
	customerScopeCodeRaw := getCustomerScopeRaw(campaign.CustomerScopes)

	if campaign.IsActive != nil && *campaign.IsActive {
		condition := bson.M{
			"$and": []*bson.M{
				{
					"sku_code":  sku,
					"is_active": &TRUE,
					"status": bson.M{
						"$ne": enum.CampaignProductStatus.CANCELLED,
					},
					"campaign_code": bson.M{
						"$ne": campaign.CampaignCode,
					},
					"location_codes": bson.M{
						"$in": locationCodeRaw,
					},
					"$or": []bson.M{
						{"start_time": bson.M{"$lte": campaign.StartTime}, "end_time": bson.M{"$gte": campaign.StartTime}},
						{"start_time": bson.M{"$lte": campaign.EndTime}, "end_time": bson.M{"$gte": campaign.EndTime}},
						{"start_time": bson.M{"$gt": campaign.StartTime}, "end_time": bson.M{"$lt": campaign.EndTime}},
					},
				},
				{
					"$or": []bson.M{
						{"customer_scope_codes": bson.M{"$in": customerScopeCodeRaw}},
						{"customer_scope_codes": nil},
					},
				},
			},
		}

		checkExist := model.SkuSaleInfoCacheDB.QueryOne(condition)

		if checkExist.Status == common.APIStatus.Ok {
			skuSaleInfo := checkExist.Data.([]*model.SkuSaleInfo)[0]

			if skuSaleInfo.SaleType == enum.SaleType.CAMPAIGN {
				return false, fmt.Sprintf("The product has existed in the campaign %s - %s .", *skuSaleInfo.CampaignCode, skuSaleInfo.Name), errCode
			}

			return false, fmt.Sprintf("The product has existed in the deal %s - %s .", *skuSaleInfo.DealCode, skuSaleInfo.Name), errCode
		}
	}

	return true, "", ""
}

// func compareTime(campaign *model.Campaign, saleInfo *model.SkuSaleInfo) bool {
// 	if saleInfo.StartTime.Before(campaign.EndTime) && (saleInfo.StartTime.After(campaign.StartTime) || saleInfo.StartTime.Equal(campaign.StartTime)) {
// 		return false
// 	}
// 	if saleInfo.EndTime.After(campaign.StartTime) && (saleInfo.EndTime.Before(campaign.EndTime) || saleInfo.EndTime.Equal(campaign.EndTime)) {
// 		return false
// 	}
// 	if saleInfo.StartTime.After(campaign.StartTime) && (saleInfo.EndTime.Before(campaign.EndTime)) {
// 		return false
// 	}
// 	if saleInfo.StartTime.Before(campaign.StartTime) && (saleInfo.EndTime.After(campaign.EndTime)) {
// 		return false
// 	}
// 	// check location
// 	if saleInfo.LocationCodes != nil && len(*saleInfo.LocationCodes) == 0 && campaign.Regions != nil && len(*campaign.Regions) != 0 {
// 		return false
// 	}
// 	return true
// }

func isValidTime(campaign *model.Campaign, saleInfo *model.SkuSaleInfo) (bool, string) {
	if saleInfo.StartTime.Before(campaign.EndTime) && (saleInfo.StartTime.After(campaign.StartTime) || saleInfo.StartTime.Equal(campaign.StartTime)) {
		return false, "Invalid time range: StartTime "
	}
	if saleInfo.EndTime.After(campaign.StartTime) && (saleInfo.EndTime.Before(campaign.EndTime) || saleInfo.EndTime.Equal(campaign.EndTime)) {
		return false, "Invalid time range: EndTime"
	}
	if saleInfo.StartTime.After(campaign.StartTime) && (saleInfo.EndTime.Before(campaign.EndTime)) {
		return false, "Invalid time range: StartTime"
	}
	if saleInfo.StartTime.Before(campaign.StartTime) && (saleInfo.EndTime.After(campaign.EndTime)) {
		return false, "Invalid time range: StartTime"
	}
	// check location
	if saleInfo.LocationCodes != nil && len(*saleInfo.LocationCodes) == 0 && campaign.Regions != nil && len(*campaign.Regions) != 0 {
		return false, "Invalid location & Regions"
	}

	if saleInfo.LocationCodes != nil && len(*saleInfo.LocationCodes) > 0 {
		if campaign.Regions != nil && len(*campaign.Regions) == 0 {
			return false, "Missing region"
		}
		locationMap := map[string]*string{}
		for _, location := range *saleInfo.LocationCodes {
			locationMap[location] = &location
		}
		for _, location := range *campaign.Regions {
			if locationMap[location] != nil {
				// nếu trùng vùng bán thì mới check thời gian trùng
				return false, "Invalid location"
			}
		}
	}

	return true, ""
}

func isValidManySKU(keys []string, skus map[string]*model.ImportDataRequest, itemCode string, campaign *model.Campaign) (map[string]*model.ImportDataRequest, int) {
	TRUE := true
	errCode := "CONFLICT_CAMPAIGN_PRODUCT"
	failed := 0

	skuExists := map[string]*model.ImportDataRequest{}
	skuRemains := map[string]*model.ImportDataRequest{}

	locationCodeRaw := getLocationCodeRaw(campaign.Regions)
	customerScopeCodeRaw := getCustomerScopeRaw(campaign.CustomerScopes)

	condition := bson.M{
		"sku_code": bson.M{
			"$in": keys,
		},
		"is_active": &TRUE,
		"status": bson.M{
			"$ne": enum.CampaignProductStatus.CANCELLED,
		},
		"location_codes": bson.M{
			"$in": locationCodeRaw,
		},
		"$or": []bson.M{
			{"customer_scope_codes": bson.M{"$in": customerScopeCodeRaw}},
			{"customer_scope_codes": nil},
		},
	}

	checkExist := model.SkuSaleInfoCacheDB.Query(condition, 0, 0, nil)

	if checkExist.Status == common.APIStatus.Ok {
		skuSaleInfo := checkExist.Data.([]*model.SkuSaleInfo)

		for _, info := range skuSaleInfo {
			if campaign.IsActive == nil || (campaign.IsActive != nil && !*campaign.IsActive) {
				continue
			}
			if info.CampaignCode != nil && *info.CampaignCode == campaign.CampaignCode {
				continue
			}

			var message string
			if info.DealCode != nil && len(*info.DealCode) > 0 {
				message = fmt.Sprintf("The product has existed in the deal %s - %s ,", *info.DealCode, info.Name)
			} else if info.CampaignCode != nil && len(*info.CampaignCode) > 0 {
				message = fmt.Sprintf("The product has existed in the campaign %s - %s ,", *info.CampaignCode, info.Name)
			}
			isValid, mess := isValidTime(campaign, info)
			if !isValid {
				skuExists[info.SkuCode] = skus[info.SkuCode]
				if resp := updateImportResultDetail(itemCode, skus[info.SkuCode].ResultDetail.Code, &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   message + " " + mess,
					ErrorCode: errCode,
				}); resp.Status == common.APIStatus.Ok {
					failed++
				}
				continue
			}
		}
	}

	if len(skuExists) <= 0 {
		return skus, failed
	}

	for key, val := range skus {
		if skuExists[key] == nil {
			skuRemains[key] = val
		}
	}

	return skuRemains, failed
}

func isValidProductCampaignRegister(skuCode string, campaign *model.Campaign, sku *model.SkuMain) (bool, string, string) {
	if sku == nil {
		skuRes := model.SkuMainCacheDB.QueryOne(bson.M{"code": skuCode})
		if skuRes.Status != common.APIStatus.Ok {
			return false, skuRes.Message, "GET_SKU_FAIL"
		}

		sku = skuRes.Data.([]*model.SkuMain)[0]
	}

	if sku.IsActive == nil || (sku.IsActive != nil && !*sku.IsActive) {
		return false, "The product has been turned off", "WRONG_STATUS"
	} else if sku.Status != nil && (*sku.Status == enum.SkuStatus.OUT_OF_STOCK || *sku.Status == enum.SkuStatus.SUSPENDED || *sku.Status == enum.SkuStatus.GIFT || *sku.Status == enum.SkuStatus.STOP_PRODUCING) {
		return false, "The status of product is invalid", "WRONG_STATUS"
	}

	return isValidWithOtherCampaign(skuCode, campaign)
}

func isValidCampaignTurnOn(campaign *model.Campaign, campaignProductCode string) (bool, []*model.CampaignPrdResponse) {
	TRUE := true
	var offset int64 = 0
	var limit int64 = 1000
	locationCodeRaw := getLocationCodeRaw(campaign.Regions)
	customerScopeCodeRaw := getCustomerScopeRaw(campaign.CustomerScopes)

	// lấy danh sách mã sku đang hoạt động trong chương trình
	listError := []*model.CampaignPrdResponse{}
	query := &model.CampaignProduct{CampaignCode: campaign.CampaignCode, Status: enum.CampaignProductStatus.NORMAL, IsActive: &TRUE}
	if campaignProductCode != "" {
		query = &model.CampaignProduct{CampaignProductCode: campaignProductCode}
	}

	for {
		qProductCampaign := model.CampaignProductDB.Query(query, offset*limit, limit, nil)
		skus := []string{}
		if qProductCampaign.Status != common.APIStatus.Ok {
			break
		}

		for _, pCampaign := range qProductCampaign.Data.([]*model.CampaignProduct) {
			skus = append(skus, pCampaign.Sku)
		}

		condition := bson.M{
			"$and": []*bson.M{
				{
					"sku_code": bson.M{
						"$in": skus,
					},
					"is_active": &TRUE,
					"status": bson.M{
						"$ne": enum.CampaignProductStatus.CANCELLED,
					},
					"location_codes": bson.M{
						"$in": locationCodeRaw,
					},
					"$or": []bson.M{
						{"start_time": bson.M{"$lte": campaign.StartTime}, "end_time": bson.M{"$gte": campaign.StartTime}},
						{"start_time": bson.M{"$lte": campaign.EndTime}, "end_time": bson.M{"$gte": campaign.EndTime}},
						{"start_time": bson.M{"$gt": campaign.StartTime}, "end_time": bson.M{"$lt": campaign.EndTime}},
					},
				},
				{
					"$or": []bson.M{
						{"customer_scope_codes": bson.M{"$in": customerScopeCodeRaw}},
						{"customer_scope_codes": nil},
					},
				},
			},
		}

		checkExist := model.SkuSaleInfoCacheDB.Query(condition, 0, 0, nil)

		if checkExist.Status == common.APIStatus.Ok {
			skuSaleInfo := checkExist.Data.([]*model.SkuSaleInfo)

			for _, info := range skuSaleInfo {
				if info.CampaignCode != nil && *info.CampaignCode == campaign.CampaignCode {
					continue
				}

				var message string
				if info.DealCode != nil && len(*info.DealCode) > 0 {
					message = fmt.Sprintf("The product has existed in the deal %s - %s ", *info.DealCode, info.Name)
				} else if info.CampaignCode != nil && len(*info.CampaignCode) > 0 {
					message = fmt.Sprintf("The product has existed in the campaign %s - %s ", *info.CampaignCode, info.Name)
				}

				listError = append(listError, &model.CampaignPrdResponse{
					Sku:     info.SkuCode,
					Message: message,
				})
			}
		}

		offset++
	}

	if len(listError) > 0 {
		return false, listError
	}

	return true, nil
}

func SyncProductCampaignSaleTime(campaignCode string) *common.APIResponse {
	qCampaign := model.CampaignDB.QueryOne(model.Campaign{CampaignCode: campaignCode})
	if qCampaign.Status != common.APIStatus.Ok {
		return qCampaign
	}
	campaign := qCampaign.Data.([]*model.Campaign)[0]
	offset, limit := int64(0), int64(1000)
	for {
		qProductCampaign := model.CampaignProductDB.Query(model.CampaignProduct{CampaignCode: campaignCode}, offset*limit, limit, nil)
		if qProductCampaign.Status != common.APIStatus.Ok {
			break
		}
		products := qProductCampaign.Data.([]*model.CampaignProduct)
		for _, product := range products {
			qCampaignSaleTime := model.CampaignSaleTimeDB.Query(&model.CampaignSaleTime{CampaignCode: campaign.CampaignCode}, 0, 0, nil)
			if qCampaignSaleTime.Status == common.APIStatus.Ok {
				campaignSaleTimes := qCampaignSaleTime.Data.([]*model.CampaignSaleTime)
				saleTimeItems := make([]*model.CampaignFlashSaleTimeItem, 0)
				for _, campaignSaleTime := range campaignSaleTimes {
					for _, saleTime := range campaignSaleTime.SaleTime {
						saleTimeItems = append(saleTimeItems, saleTime)
					}
				}
				product.FlashSaleTimes = make([]*model.CampaignFlashSaleTimeItem, 0)
				for _, itemCode := range product.FlashSaleTime {
					match := false
					for _, saleTime := range saleTimeItems {
						if saleTime.Ref == itemCode {
							match = true
							product.FlashSaleTimes = append(product.FlashSaleTimes, saleTime)
							continue
						}
					}
					if !match {
						return &common.APIResponse{
							Status:    common.APIStatus.Invalid,
							Message:   "Flash sale time not match",
							ErrorCode: "FLASH_SALE_TIME_NOT_MATCH",
						}
					}
				}
			}
			if updateRes := model.CampaignProductDB.UpdateOne(model.CampaignProduct{CampaignProductCode: product.CampaignProductCode}, product); updateRes.Status != common.APIStatus.Ok {
				return updateRes
			}
		}
		offset++
	}
	WarnUpAllCampaignProduct(campaign.CampaignCode)
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật khung giờ cho các sản phẩm trong chương trình thành công",
	}
}

func GetCampaignActiveBySku(sku string, startTime, endTime time.Time) *common.APIResponse {
	qCampaignProduct := model.CampaignProductDB.Query(model.CampaignProduct{
		Sku: sku, Status: enum.CampaignProductStatus.NORMAL, IsActive: utils.ParseBoolToPointer(true)}, 0, 0, nil)
	if qCampaignProduct.Status == common.APIStatus.Ok {
		campaignCodes := make([]string, 0)
		for _, p := range qCampaignProduct.Data.([]*model.CampaignProduct) {
			campaignCodes = append(campaignCodes, p.CampaignCode)
		}
		qCampaign := model.CampaignDB.Query(bson.M{
			"campaign_code": bson.M{"$in": campaignCodes},
			"is_active":     true,
		}, 0, 0, nil)
		if qCampaign.Status == common.APIStatus.Ok {
			isExistCampaignActive := false
			campaigns := qCampaign.Data.([]*model.Campaign)
			for _, otherCampaign := range campaigns {
				if otherCampaign.StartTime.Before(endTime) && (otherCampaign.StartTime.After(startTime) || otherCampaign.StartTime.Equal(startTime)) {
					isExistCampaignActive = true
				}
				if otherCampaign.EndTime.After(startTime) && (otherCampaign.EndTime.Before(endTime) || otherCampaign.EndTime.Equal(endTime)) {
					isExistCampaignActive = true
				}
				if otherCampaign.StartTime.After(startTime) && (otherCampaign.EndTime.Before(endTime)) {
					isExistCampaignActive = true
				}
				if otherCampaign.StartTime.Before(startTime) && (otherCampaign.EndTime.After(endTime)) {
					isExistCampaignActive = true
				}
				if isExistCampaignActive {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Data:      []*model.Campaign{otherCampaign},
						Message:   fmt.Sprintf("Sản phẩm đang hoạt động trong chương trình %s - %s", otherCampaign.CampaignCode, otherCampaign.CampaignName),
						ErrorCode: "EXISTED_CAMPAIGN_ACTIVE",
					}
				}
			}
		}
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "",
	}
}

func TurnOffProductInCampaignBySku(query interface{}) *common.APIResponse {

	qCampaign := model.CampaignProductDB.Query(query, 0, 100, &primitive.M{"_id": -1})
	if qCampaign.Status != common.APIStatus.Ok {
		return qCampaign
	}
	campaigns := qCampaign.Data.([]*model.CampaignProduct)
	campaignProductCodes := make([]string, 0)
	campaignProductList := make([]*model.CampaignProduct, 0)
	if len(campaigns) > 0 {
		for _, item := range campaigns {
			if item.Status != enum.CampaignProductStatus.CANCELLED {
				campaignProductCodes = append(campaignProductCodes, item.CampaignProductCode)
				campaignProductList = append(campaignProductList, item)
			}
		}
	}
	FALSE := false
	dt := time.Now().Add(7 * time.Hour)
	note := fmt.Sprintln("Sản phẩm bị tắt hiển thị bởi hệ thống do sai trạng thái vào lúc: ", dt.Format("02-01-2006 15:04:05"))
	res := model.CampaignProductDB.UpdateMany(&model.CampaignProduct{ComplexQuery: []*bson.M{
		{
			"campaign_product_code": bson.M{"$in": campaignProductCodes},
		},
	}}, &model.CampaignProduct{IsActive: &FALSE, PrivateNote: note})
	if res.Status == common.APIStatus.Ok {
		go func() {
			for _, item := range campaignProductList {
				WarmUpCampaignProduct(item.Sku, item.CampaignCode, nil)
				WarmupSkuSaleInfo(&model.SkuSaleInfo{
					SkuCode:             item.Sku,
					CampaignCode:        &item.CampaignCode,
					CampaignProductCode: &item.CampaignProductCode,
					IsActive:            &FALSE,
				})
			}
		}()
	}
	return res

}

func isValidSkuFulfill(sku string, campaign *model.Campaign) (bool, string, string) {
	skuRes, errSku := client.Services.Product.GetSku(sku)
	if errSku != nil {
		return false, errSku.Error(), "GET_SKU_FAIL"
	}

	if skuRes.Fulfill != nil && campaign.Fulfill != nil && *skuRes.Fulfill < *campaign.Fulfill {
		return false, "The product does not meet the program's minimum fulfillment rate", "UNQUALIFIED_SKU"
	}

	return true, "", ""
}

func ImportCampaignProduct(acc *model.Account, input *model.ImportCampaignProductRequest) *common.APIResponse {
	importResultCode := model.GenCodeWithTime()

	importResult := &model.ImportResult{
		CreatedBy:    acc.AccountID,
		ModelName:    "campaign_product",
		Status:       "IN_PROGRESS",
		Code:         importResultCode,
		Total:        len(input.Data),
		CampaignCode: input.CampaignCode,
	}
	res := model.ImportResultDB.Create(importResult)
	if res.Status == common.APIStatus.Ok {
		go func() {
			listDetail := make([]*model.ImportResultDetail, 0)
			importJobCode := model.GenCodeWithTime()
			for index, item := range input.Data {
				importResultDetailCode := model.GenCodeWithTime(index + 1)
				request, _ := json.Marshal(item)
				importResultDetail := &model.ImportResultDetail{
					ImportResultCode: importResultCode,
					ImportJobCode:    importJobCode,
					Code:             importResultDetailCode,
					Request:          string(request),
					Status:           "PENDING",
				}
				listDetail = append(listDetail, importResultDetail)

				if len(listDetail) == 200 {
					model.ImportResultDetailDB.CreateMany(listDetail)
					model.CampaignProductImportJob.Push(model.ImportItem{
						Code:          importResultCode,
						Username:      acc.Username,
						AccountID:     acc.AccountID,
						ImportJobCode: importJobCode,
					}, &job.JobItemMetadata{
						Topic: "default",
						Keys:  []string{"IMPORT_CAMPAIGN_PRODUCT", importResultCode},
					})
					listDetail = make([]*model.ImportResultDetail, 0)
					importJobCode = model.GenCodeWithTime()
				}
			}

			if len(listDetail) > 0 {
				model.ImportResultDetailDB.CreateMany(listDetail)
				model.CampaignProductImportJob.Push(model.ImportItem{
					Code:          importResultCode,
					Username:      acc.Username,
					AccountID:     acc.AccountID,
					ImportJobCode: importJobCode,
				}, &job.JobItemMetadata{
					Topic: "default",
					Keys:  []string{"IMPORT_CAMPAIGN_PRODUCT", importResultCode},
				})
			}
		}()
	}
	return res
}

func ImportCampaignProductJob(item *job.JobItem) error {
	data, err := bson.Marshal(item.Data)
	if err != nil {
		return err
	}
	var importResultCode model.ImportItem
	err = bson.Unmarshal(data, &importResultCode)
	if err != nil {
		return err
	}
	return ImportCampaignProductJobExecute(&importResultCode)
}

func updateImportResultDetail(importCode, importDetailCode string, resp *common.APIResponse) *common.APIResponse {
	response, _ := json.Marshal(resp)
	return model.ImportResultDetailDB.UpdateOne(&model.ImportResultDetail{
		Code:             importDetailCode,
		ImportResultCode: importCode,
		Status:           "PENDING",
	}, &model.ImportResultDetail{
		Response: string(response),
		Status:   resp.Status,
	})
}

func updateImportResult(importCode string, succeed, failed int) *common.APIResponse {
	return model.ImportResultDB.UpdateOneWithOption(&model.ImportResult{
		Code: importCode,
	}, &bson.M{
		"$inc": bson.M{
			"success": succeed,
			"fail":    failed,
		},
		"$set": bson.M{
			"last_updated_time": time.Now(),
		},
	})
}

func ImportCampaignProductJobExecute(item *model.ImportItem) error {
	succeed := 0
	failed := 0

	importResp := model.ImportResultDetailDB.Query(&model.ImportResultDetail{
		ImportResultCode: item.Code,
		ImportJobCode:    item.ImportJobCode,
	}, 0, 0, nil)

	if importResp.Status != common.APIStatus.Ok {
		return nil
	}

	importResultDetailList := importResp.Data.([]*model.ImportResultDetail)
	skus := map[string]*model.ImportDataRequest{}
	listSku := []string{}
	campaign := &model.Campaign{}

	for _, resultDetail := range importResultDetailList {
		if resultDetail.Status != "PENDING" {
			continue
		}

		var campaignPrd *model.CampaignProductCreateRequest
		err := json.Unmarshal([]byte(resultDetail.Request), &campaignPrd)
		if err != nil {
			if resp := updateImportResultDetail(item.Code, resultDetail.Code, &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không thể đọc dữ liệu",
			}); resp.Status == common.APIStatus.Ok {
				failed++
			}
			continue
		} else if len(campaignPrd.Sku) == 0 {
			if resp := updateImportResultDetail(item.Code, resultDetail.Code, &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Sku không hợp lệ",
				ErrorCode: "SKU_INVALID",
			}); resp.Status == common.APIStatus.Ok {
				failed++
			}
			continue
		}

		qCheck := model.CampaignDB.QueryOne(&model.Campaign{CampaignID: campaignPrd.CampaignID, CampaignCode: campaignPrd.CampaignCode})
		if qCheck.Status != common.APIStatus.Ok {
			if resp := updateImportResultDetail(item.Code, resultDetail.Code, qCheck); resp.Status == common.APIStatus.Ok {
				failed++
			}
			continue
		}

		campaign = qCheck.Data.([]*model.Campaign)[0]

		skuRes, errSku := client.Services.Product.GetSkuMain(campaignPrd.Sku)
		if errSku != nil {
			if resp := updateImportResultDetail(item.Code, resultDetail.Code, &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   errSku.Error(),
				ErrorCode: "GET_SKU_FAIL",
			}); resp.Status == common.APIStatus.Ok {
				failed++
			}
			continue
		}

		if skuRes.IsActive != nil && !*skuRes.IsActive {
			if resp := updateImportResultDetail(item.Code, resultDetail.Code, &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "This product is off display so it cannot participate in the promotion",
				ErrorCode: "WRONG_STATUS",
			}); resp.Status == common.APIStatus.Ok {
				failed++
			}
			continue
		} else if skuRes.Status != nil && (*skuRes.Status == enum.SkuStatus.OUT_OF_STOCK || *skuRes.Status == enum.SkuStatus.SUSPENDED || *skuRes.Status == enum.SkuStatus.GIFT || *skuRes.Status == enum.SkuStatus.STOP_PRODUCING) {
			if resp := updateImportResultDetail(item.Code, resultDetail.Code, &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   fmt.Sprintf("This product has the status " + enum.SkuStatusName[*skuRes.Status] + " so it cannot participate in campaign"),
				ErrorCode: "WRONG_STATUS",
			}); resp.Status == common.APIStatus.Ok {
				failed++
			}
			continue
		}

		skus[campaignPrd.Sku] = &model.ImportDataRequest{
			Request:      campaignPrd,
			ResultDetail: resultDetail,
		}
		listSku = append(listSku, campaignPrd.Sku)
	}
	// https://buymed.atlassian.net/browse/B2B-3163
	/*
		In importing file: templayte as attached file
		Add column “Status”. Status can be: SELLING or CANCEL
		Add column Cancel reason. If status is cancel, reason is required
		When user import file:
		If SKUs not exist in Campaign, status is SELLING: add new SKUs to campaign as current rule
		If SKUs exist, status in the file is the same as status in the system:
		Result: Failed
		Reason: Cannot find new update
		If SKUs exist, status in the file Cancel, status in the system SELLING:
		Result: Success
		If SKUs exsist, status in the file SELLING, status in the system CANCEL:
		Result: Failed
		Reason: Cannot update status of cancelled product
		If SKUs not exist in Campaign, status is cancel:
		Result: Failed
		Reason: Only cancel existed SKUs in campaign
		Update template file as attached file
	*/

	// listSkuCancel

	// cancel
	// cancel => phải nằm trong campaign

	// TODO: import campaign product
	listSkuCancel := []string{}
	listSkuSelling := []string{}
	for _, val := range listSku {
		if skus[val].Request.StatusItem != nil && *skus[val].Request.StatusItem == "CANCEL" {
			listSkuCancel = append(listSkuCancel, val)
		} else {
			listSkuSelling = append(listSkuSelling, val)
		}
	}

	// cancel
	if len(listSkuCancel) > 0 {

		for _, val := range listSkuCancel {
			//

			req := skus[val].Request
			campaignImport := skus[val]

			if req.CancelReason == nil || *req.CancelReason == "" {
				updateImportResultDetail(item.Code, campaignImport.ResultDetail.Code, &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   "Cancel reason is required",
					ErrorCode: "CANCEL_REASON_REQUIRED",
				})
				failed++
				continue
			}

			campaignProductResp := model.CampaignProductDB.QueryOne(&bson.M{
				"sku":           val,
				"campaign_code": campaign.CampaignCode,
				"status":        "NORMAL",
			})

			if campaignProductResp.Status != common.APIStatus.Ok {
				campaignProductResp.Message = "Only cancel existed SELLING SKUs in campaign"
				updateImportResultDetail(item.Code, campaignImport.ResultDetail.Code, campaignProductResp)
				failed++
			} else {
				//cancel
				campaign := campaignProductResp.Data.([]*model.CampaignProduct)[0]
				cancelReason := fmt.Sprintf("%s (%s)", *req.CancelReason, utils.GetCurrentTimeByGMT(7).Format("2006-01-02 15:04:05"))
				request := &model.CampaignProductUpdateRequest{
					CampaignProductID: campaign.CampaignProductID,
					CancelReason:      &cancelReason,
					Status:            enum.CampaignProductStatus.CANCELLED,
				}
				updateResp := UpdateCampaignProduct(nil, request)
				// if updateResp.Status == common.APIStatus.Ok {
				// 	succeed++
				// } else {
				// 	failed++
				// }

				if resp := updateImportResultDetail(item.Code, campaignImport.ResultDetail.Code, updateResp); resp.Status == common.APIStatus.Ok {
					if updateResp.Status == common.APIStatus.Ok {
						succeed++
					} else {
						failed++
					}

				}
			}

		}
	}

	// bỏ những sku cancel trong skus
	for _, val := range listSkuCancel {
		delete(skus, val)
	}

	// listSkuSelling
	if len(skus) > 0 && len(listSkuSelling) > 0 {
		listImport, count := isValidManySKU(listSkuSelling, skus, item.Code, campaign)
		failed += count

		for _, val := range listImport {
			createResp := CreateCampaignProduct(&model.Account{AccountID: item.AccountID}, val.Request, true, false)

			if resp := updateImportResultDetail(item.Code, val.ResultDetail.Code, createResp); resp.Status == common.APIStatus.Ok {
				if createResp.Status == common.APIStatus.Ok {
					succeed++
				} else {
					failed++
				}
			}
		}
	}

	// warm up all product campaign
	// WarnUpAllCampaignProduct(campaign.CampaignCode)

	_ = updateImportResult(item.Code, succeed, failed)
	importResultResp := model.ImportResultDB.QueryOne(&model.ImportResult{Code: item.Code})
	if importResultResp.Status != common.APIStatus.Ok {
		return nil
	}
	importResult := importResultResp.Data.([]*model.ImportResult)[0]
	if importResult.Status == "DONE" {
		return nil
	}

	if importResult.Fail+importResult.Success == importResult.Total {
		if resp := model.ImportResultDB.UpdateOne(&model.ImportResult{Code: item.Code, Status: "IN_PROGRESS"}, &model.ImportResult{Status: "DONE"}); resp.Status == common.APIStatus.Ok {
			_ = client.Services.Notification.CreateNotification(&model.Notification{
				Username:     item.Username,
				UserID:       item.AccountID,
				ReceiverType: utils.ParseStringToPointer("EMPLOYEE"),
				Topic:        "ANNOUNCEMENT",
				Title:        fmt.Sprintln("Importing the list of products for the promotion has been successful. "),
				Link:         fmt.Sprintf("/marketing/detail-import-result-sale-campaign?code=%s", item.Code),
			})
		}
		return nil
	}
	return fmt.Errorf("Import campaign product not done yet")
}

func getCampaignFlashSaleTimeItems(campaigns []*model.Campaign) {
	if len(campaigns) == 0 {
		return
	}
	campaignIDs := make([]int64, 0)
	for _, item := range campaigns {
		if item.CampaignType == enum.CampaignType.FLASH_SALE {
			campaignIDs = append(campaignIDs, item.CampaignID)
		}
	}
	offset, limit := int64(0), int64(1000)
	listItemsView := make([]*model.CampaignFlashSaleTimeItem, 0)
	for {
		itemsRs := model.CampaignFlashSaleTimeItemDB.Query(&model.CampaignFlashSaleTimeItem{
			RefTable: model.CampaignDB.ColName,
			ComplexQuery: []*bson.M{
				{
					"campaign_id": &bson.M{
						"$in": campaignIDs,
					},
				},
			},
		}, offset, limit, nil)
		if itemsRs.Status == common.APIStatus.Ok {
			items := itemsRs.Data.([]*model.CampaignFlashSaleTimeItem)
			listItemsView = append(listItemsView, items...)
			offset += limit
		} else {
			break
		}
	}
	mapItemsView := make(map[int64][]*model.CampaignFlashSaleTimeItem)
	for _, item := range listItemsView {
		mapItemsView[item.CampaignID] = append(mapItemsView[item.CampaignID], item)
	}
	for _, item := range campaigns {
		item.FlashSaleTimesView = mapItemsView[item.CampaignID]
	}
}

func MigrateCampaignFlashSaleItems() *common.APIResponse {
	go func() {
		offset, limit := int64(0), int64(100)
		for {
			campaignRs := model.CampaignDB.Query(&model.Campaign{CampaignType: enum.CampaignType.FLASH_SALE}, offset, limit, nil)
			if campaignRs.Status != common.APIStatus.Ok {
				break
			}
			campaigns := campaignRs.Data.([]*model.Campaign)
			for _, campaign := range campaigns {
				if len(campaign.FlashSaleTimesView) > 0 {
					for _, item := range campaign.FlashSaleTimesView {
						item.CampaignID = campaign.CampaignID
						item.RefTable = model.CampaignDB.ColName
					}
					_ = model.CampaignFlashSaleTimeItemDB.Delete(&model.CampaignFlashSaleTimeItem{CampaignID: campaign.CampaignID, RefTable: model.CampaignDB.ColName})
					_ = model.CampaignFlashSaleTimeItemDB.CreateMany(campaign.FlashSaleTimesView)
					WarmUpCampaign(campaign.CampaignCode, nil)
				}
			}
			offset += limit
		}
	}()
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "run migrate success",
	}
}

func WarmupCampaignProductBySku(input *model.WarmupCampaignProductRequest) *common.APIResponse {
	go func() {
		offset, limit := int64(0), int64(500)
		for {
			res := model.CampaignProductDB.Query(&model.CampaignProduct{Sku: input.Sku}, offset, limit, nil)
			if res.Status != common.APIStatus.Ok {
				break
			}
			campaignProducts := res.Data.([]*model.CampaignProduct)
			for _, campaignProduct := range campaignProducts {
				WarmUpCampaignProduct(campaignProduct.Sku, campaignProduct.CampaignCode, nil)
			}
			offset += limit
		}
	}()
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Warmup campaign product success",
	}
}

func WarnUpAllCampaignByLocation(location string) {
	offset := int64(0)
	limit := int64(200)
	for true {
		result := model.CampaignDB.Query(&model.Campaign{ComplexQuery: []*bson.M{
			{
				"regions": location,
			},
		}}, offset, limit, &primitive.M{"_id": -1})
		if result.Status == common.APIStatus.Ok {
			list := result.Data.([]*model.Campaign)
			for _, item := range list {
				WarnUpAllCampaignProduct(item.CampaignCode)
			}
		} else {
			return
		}
		offset += limit
	}
}

func WorkerCancelSoldOutProduct() {
	fmt.Println("WorkerCancelSoldOutProduct")
	defer fmt.Println("WorkerCancelSoldOutProduct DONE")

	processingCampaigns := model.CampaignReaderDB.Query(
		&bson.M{"status": enum.CampaignStatus.PROCESSING, "is_active": true},
		0,
		0,
		nil,
	)
	if processingCampaigns.Status != common.APIStatus.Ok {
		return
	}
	campaigns, ok := processingCampaigns.Data.([]*model.Campaign)
	if !ok {
		return
	}

	for _, campaign := range campaigns {
		query := bson.M{
			"status":      enum.CampaignProductStatus.NORMAL,
			"campaign_id": campaign.CampaignID,
			"$expr": bson.M{
				"$gte": []string{"$sold_quantity", "$quantity"},
			},
		}

		var (
			offset                         int64 = 0
			arrCancelledCampaignProductIds []int64
		)
		const LIMIT int64 = 100
		for {
			products := model.CampaignProductReaderDB.Query(&query, offset, LIMIT, nil)
			if products.Status != common.APIStatus.Ok {
				break
			}
			offset += LIMIT

			campaignProducts, ok := products.Data.([]*model.CampaignProduct)
			if ok {
				for _, product := range campaignProducts {
					arrCancelledCampaignProductIds = append(arrCancelledCampaignProductIds, product.CampaignProductID)
				}
			}
		}

		CANCEL_REASON := fmt.Sprintf("This product is sold out of campaign (%s)", utils.GetCurrentTimeByGMT(7).Format("2006-01-02 15:04:05"))
		for _, campaignProductId := range arrCancelledCampaignProductIds {
			request := &model.CampaignProductUpdateRequest{
				CampaignProductID: campaignProductId,
				CancelReason:      &CANCEL_REASON,
				Status:            enum.CampaignProductStatus.CANCELLED,
			}
			UpdateCampaignProduct(nil, request)
		}
		time.Sleep(50 * time.Millisecond)
	}
}
