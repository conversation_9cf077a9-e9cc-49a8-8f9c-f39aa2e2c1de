package enum

type CampaignLogStatusValue string

type campaignLogStatus struct {
	IN_PROCESS CampaignLogStatusValue
	TODO       CampaignLogStatusValue
	DONE       CampaignLogStatusValue
	FAIL       CampaignLogStatusValue
}

var CampaignLogStatus = &campaignLogStatus{
	IN_PROCESS: "IN_PROCESS",
	TODO:       "TODO",
	DONE:       "DONE",
	FAIL:       "FAIL",
}

type CheckProductFulfillmentType string

type checkProductFulfillment struct {
	AUTO   CheckProductFulfillmentType
	MANUAL CheckProductFulfillmentType
}

var CheckProductFulfillment = &checkProductFulfillment{
	AUTO:   "AUTO",
	MANUAL: "MANUAL",
}
