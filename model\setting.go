package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type Setting struct {
	ID                             primitive.ObjectID    `json:"-" bson:"_id,omitempty" `
	CreatedTime                    *time.Time            `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime                *time.Time            `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	SystemDisplay                  string                `json:"systemDisplay,omitempty" bson:"system_display,omitempty"`
	NumberOfVoucherAuto            int                   `json:"numberOfVoucherAuto,omitempty" bson:"number_of_voucher_auto,omitempty"`
	NumberOfVoucherManual          int                   `json:"numberOfVoucherManual,omitempty" bson:"number_of_voucher_manual,omitempty"`
	NumberOfVoucherAutoPerSeller   int                   `json:"numberOfVoucherAutoPerSeller,omitempty" bson:"number_of_voucher_auto_per_seller,omitempty"`
	NumberOfVoucherManualPerSeller int                   `json:"numberOfVoucherManualPerSeller,omitempty" bson:"number_of_voucher_manual_per_seller,omitempty"`
	VoucherGroupConnectionCases    map[string]bool       `json:"voucherGroupConnectionCases,omitempty" bson:"voucher_group_connection_cases,omitempty"`
	SkipLuckyWheelItemSpecial      *map[string]bool      `json:"skipLuckyWheelItemSpecial,omitempty" bson:"skip_lucky_wheel_item_special,omitempty"`
	ApplyDiscountVoucher           *ApplyDiscountOptions `json:"applyDiscountVoucher,omitempty" bson:"apply_discount_voucher,omitempty"`
}

var SettingDB = &db.Instance{
	ColName:        "setting",
	TemplateObject: &Setting{},
}

// InitSettingModel is func init model
func InitSettingModel(s *mongo.Database) {
	SettingDB.ApplyDatabase(s)
}
