package conf

import (
	"fmt"
	"os"

	"gitlab.buymed.tech/sdk/golang/configuration"
)

type config struct {
	Env         string ``
	Protocol    string
	Version     string
	MainDBName  string
	MainAuthDB  string
	CacheDBName string
	LogDBName   string
	LogAuthDB   string
	APIHost     string
	APIKey      string
	JobDBName   string
	JobAuthDB   string
	SkipIndex   bool

	// DB
	MainDBConf  configuration.Database
	LogDBConf   configuration.Database
	CacheDBConf configuration.Database
	JobDBConf   configuration.Database
}

// Config main config object
var Config *config

func init() {
	env := os.Getenv("env")
	if env == "" {
		env = "dev"
	}
	protocol := os.Getenv("protocol")
	version := os.Getenv("version")
	switch env {

	// config for dev
	case "dev":
		Config = &config{
			Env:      "dev",
			Protocol: protocol,
			Version:  version,
			// MainDBName:  "marketplace_dev_promotion",
			// MainAuthDB:  "admin",
			// CacheDBName: "marketplace_dev_product-v2",
			// LogDBName:   "marketplace_dev_promotion_log",
			// LogAuthDB:   "admin",
			// APIHost:     "https://api.dev.buymed.co.th",
			// APIKey:      "Basic UEFSVE5FUi92Mi5jdXN0b21lci5jdXN0b21lcjpWNHRqTDI5UVQ0",
			// JobDBName:   "marketplace_dev_promotion_job",
			// JobAuthDB:   "admin",
			SkipIndex: false,
		}
		break

	// config for staging
	case "stg":
		Config = &config{
			Env:      "stg",
			Protocol: protocol,
			Version:  version,
			// MainDBName: "marketplace_stg_promotion",
			// MainAuthDB: "admin",
			// LogDBName:  "marketplace_stg_promotion_log",
			// LogAuthDB:  "admin",
			// // APIHost:     "http://proxy-service.frontend-th-stg",
			// APIHost:     "https://api.stg.th.buymed.tech",
			// APIKey:      "Basic UEFSVE5FUi92Mi5jdXN0b21lci5jdXN0b21lcjpWNHRqTDI5UVQ0",
			// JobDBName:   "marketplace_stg_promotion_job",
			// JobAuthDB:   "admin",
			SkipIndex: false,
			// CacheDBName: "marketplace_stg_product-v2",
		}
		break
	case "uat":
		Config = &config{
			Env:      "uat",
			Protocol: protocol,
			Version:  version,
			// MainDBName:  "marketplace_prd_promotion",
			// MainAuthDB:  "marketplace_prd_promotion",
			// LogDBName:   "marketplace_uat_promotion_log",
			// LogAuthDB:   "admin",
			// APIHost:     "https://api.uat.buymed.co.th",
			// APIKey:      "Basic UEFSVE5FUi92Mi5tYXJrZXRwbGFjZS5wcm9tb3Rpb246U0xRSlFIUktESEZLRFJFSUNCMjM4NDI3",
			// JobDBName:   "marketplace_uat_promotion_job",
			// JobAuthDB:   "admin",
			SkipIndex: true,
			// CacheDBName: "marketplace_prd_product-v2",
		}
		break
	case "prd":
		Config = &config{
			Env:      "prd",
			Protocol: protocol,
			Version:  version,
			// MainDBName:  "marketplace_prd_promotion",
			// MainAuthDB:  "marketplace_prd_promotion",
			// CacheDBName: "marketplace_prd_product-v2",
			// LogDBName:   "marketplace_prd_promotion_log",
			// LogAuthDB:   "admin",
			// APIHost:     "http://proxy-service.frontend-th-prd",
			// APIKey:      "Basic UEFSVE5FUi92Mi5tYXJrZXRwbGFjZS5wcm9tb3Rpb246U0xRSlFIUktESEZLRFJFSUNCMjM4NDI3",
			// JobDBName:   "marketplace_prd_promotion_job",
			// JobAuthDB:   "marketplace_prd_promotion_job",
			// JobAuthDB: "admin",
			SkipIndex: true,
		}
		break
	}

	// init config db here
	// TODO: please change the db name format here
	{
		dbFormat := "marketplace_%s_promotion"
		dbFormatPRD := "marketplace_%s_promotion"
		dbFormatCache := "marketplace_%s_product-v2"

		dbLogFormat := dbFormatPRD + "_log"
		dbJobFormat := dbFormatPRD + "_job"

		// if format stg differenc with prd
		if env == "stg" || env == "local-stg" || env == "dev" {
			Config.MainDBConf = initMainDBConfig(configuration.Get("db").ToDatabaseConfig(), dbFormat, env)

		} else {
			Config.MainDBConf = initMainDBConfig(configuration.Get("db").ToDatabaseConfig(), dbFormatPRD, env)
		}

		// db log job cache
		Config.LogDBConf = initLogDBConfig(configuration.Get("logDB").ToDatabaseConfig(), dbLogFormat, env)
		Config.JobDBConf = initQueueDBConfig(configuration.Get("jobDB").ToDatabaseConfig(), dbJobFormat, env)
		Config.CacheDBConf = initCacheDBConfig(configuration.Get("cacheDB").ToDatabaseConfig(), dbFormatCache, env)
	}

	// init service outbound or another API
	{
		buymedTHClient := configuration.Get("buymed-th-client").ToServiceConfig()

		// API HOST :
		Config.APIHost = buymedTHClient.Host
		Config.APIKey = buymedTHClient.Authorization

		// Config.APIHost = "https://api.stg.th.buymed.tech"
		// Config.APIKey = "Basic UEFSVE5FUi92Mi5jdXN0b21lci5jdXN0b21lcjpWNHRqTDI5UVQ0"

		// storage key

		// StorageClient := configuration.Get("storage").ToServiceConfig()
		// decoded, err := base64.URLEncoding.DecodeString(StorageClient.Key)
		// if err != nil {
		// 	fmt.Println("[Parse config] Convert B64 config string error: " + err.Error())
		// 	return
		// }
		// Config.StorageKey = decoded

		// if we have outbound api
		// Config.MessagingServiceConfig = &OutboundAPICredential{
		// 	Url:  buymedTHClient.Host + "/integration/messaging/v1",
		// 	Auth: buymedTHClient.Authorization,
		// }
	}
}

// cloneConfig clone config object
func initMainDBConfig(_config configuration.Database, dbNameFormat, env string) configuration.Database {

	// UAT và PRD dùng chung db main của prd
	if env == "uat" {
		env = "prd"
	}
	_config.DatabaseName = fmt.Sprintf(dbNameFormat, env)

	if _config.Auth == "" {
		_config.Auth = _config.DatabaseName
	}
	return _config
}

func initQueueDBConfig(_config configuration.Database, dbNameFormat, env string) configuration.Database {

	_config.DatabaseName = fmt.Sprintf(dbNameFormat, env)

	if _config.Auth == "" {
		_config.Auth = _config.DatabaseName
	}
	return _config
}

func initLogDBConfig(_config configuration.Database, dbNameFormat, env string) configuration.Database {

	_config.DatabaseName = fmt.Sprintf(dbNameFormat, env)

	if _config.Auth == "" {
		_config.Auth = _config.DatabaseName
	}
	return _config
}

func initCacheDBConfig(_config configuration.Database, dbNameFormat, env string) configuration.Database {
	// UAT và PRD dùng chung db main của prd
	if env == "uat" {
		env = "prd"
	}

	_config.DatabaseName = fmt.Sprintf(dbNameFormat, env)

	if _config.Auth == "" {
		_config.Auth = _config.DatabaseName
	}
	return _config
}
