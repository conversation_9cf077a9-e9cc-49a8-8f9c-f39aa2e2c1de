package model

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"
)

type ProductSearchConfig struct {
	Code string `json:"code,omitempty" bson:"code,omitempty"`

	ProductSearchScopeConfigs []*ProductSearchScopeConfig `json:"productSearchScopeConfigs,omitempty" bson:"product_search_scope_configs,omitempty"`

	// add new field for search

	// run time
	LastRunTime *time.Time `json:"lastRunTime,omitempty" bson:"last_run_time,omitempty"`
}

type ProductSearchScopeConfig struct {
	ID   int64  `json:"id,omitempty" bson:"id,omitempty"`
	Code string `json:"code,omitempty" bson:"code,omitempty"`

	// required
	Scopes         []string  `json:"scopes,omitempty" bson:"scopes,omitempty"`                  // PHARMACY, CLINIC, HOSPITAL, DRUGSTORE , pharmacy company , beauty salon , health center
	CustomerLevels *[]string `json:"customerLevels,omitempty" bson:"customer_levels,omitempty"` // DIAMOND, PLATINUM, GOLD, SILVER, BRONZE, NORMAL , BANNED , BLACK LIST , see in crm/customer

	// required
	Status                 string    `json:"status,omitempty" bson:"status,omitempty"`                                    // ACTIVE, INACTIVE
	SellerCategoryCodes    []string  `json:"sellerCategoryCodes,omitempty" bson:"seller_category_codes,omitempty"`        // /cms/product/edit?
	SellerSubCategoryCodes *[]string `json:"sellerSubCategoryCodes,omitempty" bson:"seller_sub_category_codes,omitempty"` // /cms/product/edit?

	// db control
	CreatedBy       int64      `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	UpdatedBy       int64      `json:"updatedBy,omitempty" bson:"updated_by,omitempty"`
	CreatedTime     *time.Time `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	// complexQuery
	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
}

type ProductSearchRangePriceConfig struct {
	ID   int64  `json:"id,omitempty" bson:"id,omitempty"`
	Code string `json:"code,omitempty" bson:"code,omitempty"`

	//
	Name string `json:"name,omitempty" bson:"name,omitempty"`
	// MinValue float64 `json:"minValue,omitempty" bson:"min_value,omitempty"`
	// MaxValue float64 `json:"maxValue,omitempty" bson:"max_value,omitempty"`
	MinPrice float64 `json:"minPrice,omitempty" bson:"min_price,omitempty"`
	MaxPrice float64 `json:"maxPrice,omitempty" bson:"max_price,omitempty"`
	Status   string  `json:"status,omitempty" bson:"status,omitempty"`

	// db control
	CreatedBy       int64      `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	UpdatedBy       int64      `json:"updatedBy,omitempty" bson:"updated_by,omitempty"`
	CreatedTime     *time.Time `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
}

type ProductSearchRangePriceResponse struct {
	BaseAPIResponse
	Data []*ProductSearchRangePriceConfig `json:"data"`
}
