package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/buymed.th/marketplace/promotion/client"
)

var (
	mapRegionCache       map[string][]string
	isSyncMapRegionCache bool
	mapLevelCache        map[string][]string
	mapScopeCache        = map[string][]string{
		"ALL": {"PHARMACY",
			"C<PERSON>IN<PERSON>",
			"DRUGSTORE",
			"HOSPITAL",
			"PHARMA_COMPANY",
			"DENTISTRY",
			"BEAUTY_SALON",
			"HEALTH_CENTER",
			//
			"ANIMAL_PHARMACY",
			"WHOLE<PERSON>LE_PHARMACY",
			"ANIMAL_CLINIC",
			"NURSE_CLINIC",
		},
		"PHARMACY":           {"PHARMACY"},
		"CLINIC":             {"CLIN<PERSON>"},
		"DRUGSTORE":          {"DRUGSTORE"},
		"HOSPITAL":           {"HOSPITAL"},
		"PHARMA_COMPANY":     {"PHARMA_COMPANY"},
		"DENTISTRY":          {"DENTISTRY"},
		"BEAUTY_SALON":       {"BEAUTY_SALON"},
		"HEALTH_CENTER":      {"HEALTH_CENTER"},
		"ANIMAL_PHARMACY":    {"ANIMAL_PHARMACY"},
		"WHOLESALE_PHARMACY": {"WHOLESALE_PHARMACY"},
		"ANIMAL_CLINIC":      {"ANIMAL_CLINIC"},
		"NURSE_CLINIC":       {"NURSE_CLINIC"},
	}
)

// WarmupRegionMasterdataCache ...
func WarmupRegionMasterdataCache() {
	if isSyncMapRegionCache {
		return
	}
	isSyncMapRegionCache = true
	defer func() {
		isSyncMapRegionCache = false
	}()
	tmpMapRegionCache := make(map[string][]string)
	if mapRegionCache == nil {
		mapRegionCache = make(map[string][]string)
	}
	regionResp := client.LocationClient.GetRegionList([]string{})
	if regionResp.Status != common.APIStatus.Ok || len(regionResp.Data) == 0 {
		// reset
		return
	}
	for _, region := range regionResp.Data {
		tmpMapRegionCache[region.Code] = region.ProvinceCodes
	}

	listProvinces := make([]string, 0)
	provinceResp := client.LocationClient.GetProvinceList()
	if provinceResp.Status == common.APIStatus.Ok {
		for _, province := range provinceResp.Data {
			listProvinces = append(listProvinces, province.Code)
		}
	}
	tmpMapRegionCache["00"] = listProvinces

	// changeRegion := CompareMapString(mapRegionCache, tmpMapRegionCache)
	// isEmpty := len(mapRegionCache) == 0
	mapRegionCache = tmpMapRegionCache
	// if len(changeRegion) > 0 && !isEmpty {
	// 	for _, region := range changeRegion {
	// 		WarnUpAllCampaignByLocation(region)
	// 	}
	// }
}

// WarmupLevelMasterdataCache ...
func WarmupLevelMasterdataCache() {
	if mapLevelCache == nil {
		mapLevelCache = make(map[string][]string)
	}

	levelCodes := []string{}

	levels, err := client.Services.Customer.GetListLevelCustomer()
	if err != nil {
		return
	}

	for _, level := range levels {
		levelCodes = append(levelCodes, level.Code)
	}

	mapLevelCache["ALL"] = levelCodes
}

func uniqueSliceString(stringSlice []string) []string {
	keys := make(map[string]bool)
	list := []string{}
	for _, entry := range stringSlice {
		if _, value := keys[entry]; !value {
			keys[entry] = true
			list = append(list, entry)
		}
	}
	return list
}

func CompareMapString(curMap, newMap map[string][]string) []string {
	changeMap := make([]string, 0)
	for keyCur, valCur := range curMap {
		if newMap[keyCur] == nil || len(newMap[keyCur]) != len(valCur) {
			changeMap = append(changeMap, keyCur)
		}
	}

	for keyNew, valNew := range newMap {
		if curMap[keyNew] == nil || len(curMap[keyNew]) != len(valNew) {
			changeMap = append(changeMap, keyNew)
		}
	}
	return uniqueSliceString(changeMap)
}

func isNilOrDefaultValue(val interface{}) bool {
	switch v := val.(type) {
	case *int:
		if v == nil {
			return true
		} else {
			return *v == 0
		}
	case int:
		return v == 0
	case *float64:
		if v == nil {
			return true
		} else {
			return *v == 0
		}
	case float64:
		return v == 0
	case *int64:
		if v == nil {
			return true
		} else {
			return *v == 0
		}
	case int64:
		return v == 0
	case nil:
		return true
	}
	return false
}
