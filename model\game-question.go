package model

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type GameQuestion struct {
	Code      string       `json:"code" bson:"code"`
	Title     string       `json:"title" bson:"title"`
	Question  string       `json:"question" bson:"question"`
	Answers   []GameAnswer `json:"answers" bson:"answers"`
	IsEnabled bool         `json:"isEnabled" bson:"is_enabled"`
	IsDeleted *bool        `json:"isDeleted,omitempty" bson:"is_deleted,omitempty"`
}

type GameAnswer struct {
	AnswerID  int64  `json:"answerId" bson:"answer_id"`
	Content   string `json:"content" bson:"content"`
	IsCorrect *bool  `json:"isCorrect,omitempty" bson:"is_correct,omitempty"`
}

var GameQuestionDB = &db.Instance{
	ColName:        "game_question",
	TemplateObject: &GameQuestion{},
}

func InitGameQuestionModel(s *mongo.Database) {
	GameQuestionDB.ApplyDatabase(s)

	TRUE := true
	GameQuestionDB.CreateIndex(bson.D{
		primitive.E{Key: "code", Value: -1},
	}, &options.IndexOptions{
		Background: &TRUE,
		Unique:     &TRUE,
	})

	GameQuestionDB.CreateIndex(bson.D{
		primitive.E{Key: "code", Value: -1},
		primitive.E{Key: "is_deleted", Value: -1},
	}, &options.IndexOptions{
		Background: &TRUE,
	})

	GameQuestionDB.CreateIndex(bson.D{
		primitive.E{Key: "is_enabled", Value: -1},
	}, &options.IndexOptions{
		Background: &TRUE,
	})
}
