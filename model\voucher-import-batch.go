package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/buymed.th/marketplace/promotion/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// VoucherImportBatch - Bảng ch<PERSON>h lưu thông tin batch import voucher
type VoucherImportBatch struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	CompletedTime   *time.Time         `json:"completedTime,omitempty" bson:"completed_time,omitempty"`

	// Thông tin batch
	BatchCode         string   `json:"batchCode,omitempty" bson:"batch_code,omitempty"` // Mã batch unique
	AccountID         int64    `json:"accountID,omitempty" bson:"account_id,omitempty"` // Account tạo batch
	AccountFullname   string   `json:"accountFullname,omitempty" bson:"account_fullname,omitempty"`
	Username          string   `json:"username,omitempty" bson:"username,omitempty"`
	CodeVoucherFailed []string `json:"codeFailed,omitempty" bson:"code_failed,omitempty"`

	// Trạng thái và thống kê
	Status         *enum.StatusVoucherImportBatchValue `json:"status,omitempty" bson:"status,omitempty"`                  // PENDING, PROCESSING, COMPLETED, FAILED
	Total          int                                 `json:"total,omitempty" bson:"total,omitempty"`                    // Tổng số voucher cần tạo
	Success        int                                 `json:"success,omitempty" bson:"success,omitempty"`                // Số voucher tạo thành công
	Failed         int                                 `json:"failed,omitempty" bson:"failed,omitempty"`                  // Số voucher tạo thất bại
	ProcessingTime int                                 `json:"processingTime,omitempty" bson:"processing_time,omitempty"` // Thời gian xử lý (ms)
	ModelName      string                              `json:"modelName,omitempty" bson:"model_name,omitempty"`

	// Metadata
	ErrorMessage string `json:"errorMessage,omitempty" bson:"error_message,omitempty"`

	// Query fields
	ComplexQuery      []*bson.M  `json:"-" bson:"$and,omitempty"`
	CreatedTimeFrom   *time.Time `json:"createdTimeFrom,omitempty" bson:"-"`
	CreatedTimeTo     *time.Time `json:"createdTimeTo,omitempty" bson:"-"`
	CompletedTimeFrom *time.Time `json:"completedTimeFrom,omitempty" bson:"-"`
	CompletedTimeTo   *time.Time `json:"completedTimeTo,omitempty" bson:"-"`
}

// VoucherImportBatchDetail - Bảng chi tiết lưu từng voucher trong batch
type VoucherImportBatchDetail struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	CompletedTime   *time.Time         `json:"completedTime,omitempty" bson:"completed_time,omitempty"`

	// Liên kết với batch
	BatchCode  string `json:"batchCode,omitempty" bson:"batch_code,omitempty"`   // Mã batch
	DetailCode string `json:"detailCode,omitempty" bson:"detail_code,omitempty"` // Mã chi tiết unique
	// JobCode    string `json:"jobCode,omitempty" bson:"job_code,omitempty"`       // Mã job xử lý
	OrderIndex int `json:"orderIndex,omitempty" bson:"order_index,omitempty"` // Thứ tự trong batch

	// Dữ liệu voucher
	VoucherData   string `json:"voucherData,omitempty" bson:"voucher_data,omitempty"` // JSON string của voucher input
	ImportJobCode string `json:"importJobCode,omitempty" bson:"import_job_code,omitempty"`

	// Trạng thái và kết quả
	Status       *enum.StatusVoucherImportBatchValue `json:"status,omitempty" bson:"status,omitempty"`     // PENDING, PROCESSING, COMPLETED, FAILED
	Response     string                              `json:"response,omitempty" bson:"response,omitempty"` // JSON response từ việc tạo voucher
	ErrorMessage string                              `json:"errorMessage,omitempty" bson:"error_message,omitempty"`
	ErrorCode    string                              `json:"errorCode,omitempty" bson:"error_code,omitempty"`

	// Query fields
	ComplexQuery      []*bson.M  `json:"-" bson:"$and,omitempty"`
	CreatedTimeFrom   *time.Time `json:"createdTimeFrom,omitempty" bson:"-"`
	CreatedTimeTo     *time.Time `json:"createdTimeTo,omitempty" bson:"-"`
	CompletedTimeFrom *time.Time `json:"completedTimeFrom,omitempty" bson:"-"`
	CompletedTimeTo   *time.Time `json:"completedTimeTo,omitempty" bson:"-"`
}

// Database instances
var VoucherImportBatchDB = &db.Instance{
	ColName:        "voucher_import_batch",
	TemplateObject: &VoucherImportBatch{},
}

var VoucherImportBatchDetailDB = &db.Instance{
	ColName:        "voucher_import_batch_detail",
	TemplateObject: &VoucherImportBatchDetail{},
}

// InitVoucherImportBatchModel - Khởi tạo model
func InitVoucherImportBatchModel(s *mongo.Database) {
	VoucherImportBatchDB.ApplyDatabase(s)

	// Tạo index cho performance
	t := true
	VoucherImportBatchDB.CreateIndex(bson.D{
		primitive.E{Key: "batch_code", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
		Unique:     &t,
	})

	VoucherImportBatchDB.CreateIndex(bson.D{
		primitive.E{Key: "account_id", Value: 1},
		primitive.E{Key: "created_time", Value: -1},
	}, &options.IndexOptions{
		Background: &t,
	})

	VoucherImportBatchDB.CreateIndex(bson.D{
		primitive.E{Key: "status", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})
	VoucherImportBatchDB.CreateIndex(bson.D{
		primitive.E{Key: "voucher_ids", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})
}

// InitVoucherImportBatchDetailModel - Khởi tạo model detail
func InitVoucherImportBatchDetailModel(s *mongo.Database) {
	VoucherImportBatchDetailDB.ApplyDatabase(s)

	// Tạo index cho performance
	t := true
	VoucherImportBatchDetailDB.CreateIndex(bson.D{
		primitive.E{Key: "batch_code", Value: 1},
		primitive.E{Key: "order_index", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	VoucherImportBatchDetailDB.CreateIndex(bson.D{
		primitive.E{Key: "detail_code", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
		Unique:     &t,
	})

	VoucherImportBatchDetailDB.CreateIndex(bson.D{
		primitive.E{Key: "status", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})
}
