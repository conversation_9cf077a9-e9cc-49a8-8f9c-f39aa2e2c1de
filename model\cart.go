package model

// CartCheckRequest ...
type CartCheckRequest struct {
	Cart      *Cart `json:"cart"`
	AccountID int64 `json:"accountID"`
}

type CartItemInternal struct {
	CartNo               *string            `json:"cartNo,omitempty" bson:"cart_no,omitempty"` // cart render as token
	Quantity             int64              `json:"quantity" bson:"quantity,omitempty"`
	UnitPrice            float64            `json:"price" bson:"unit_price,omitempty"`
	Total                float64            `json:"total" bson:"total,omitempty"`
	MaxQuantity          int64              `json:"maxQuantity" bson:"max_quantity,omitempty"`
	ProductSKU           string             `json:"sku,omitempty" bson:"sku,omitempty"`
	ProducerCode         string             `json:"producerCode,omitempty" bson:"producer_code,omitempty"`
	CategoryCodes        []string           `json:"categoryCodes,omitempty" bson:"category_codes,omitempty"`
	SellerCode           string             `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	Ingredients          []string           `json:"ingredients,omitempty" bson:"ingredients,omitempty"`
	ProductTags          []string           `json:"productTags,omitempty" bson:"product_tags,omitempty"`
	ExportVAT            *bool              `json:"exportVat" bson:"export_vat,omitempty"`
	ProductID            int64              `json:"productId"`
	Type                 string             `json:"type,omitempty"`
	CampaignCode         string             `json:"campaignCode,omitempty"`
	ProductCode          string             `json:"productCode"`
	IsSelected           *bool              `json:"isSelected,omitempty" bson:"is_selected,omitempty"`
	TotalTagPrice        map[string]float64 `json:"-"`
	TotalTagQuantity     map[string]float64 `json:"-"`
	TotalProductPrice    float64            `json:"-"`
	TotalProductQuantity int64              `json:"-"`
}

type Cart struct {
	CartNo        string  `json:"cartNo" bson:"cart_no,omitempty"`         // cart render as token
	AccountID     int64   `json:"accountID" bson:"account_id,omitempty"`   // mã khách hàng
	CustomerID    int64   `json:"customerId" bson:"customer_id,omitempty"` // mã khách hàng
	PaymentMethod *string `json:"paymentMethod" bson:"payment_method,omitempty"`
	CustomerLevel *string `json:"-" bson:"customer_level,omitempty"` // phương thức thanh toán cod/chuyển khoản
	CustomerScope string  `json:"-" bson:"customer_scope,omitempty"` // phương thức thanh toán cod/chuyển khoản
	//CustomerRegistrationTime *time.Time          `json:"customerRegistrationTime" bson:"customer_registration_time,omitempty"`
	//CustomerShippingAddress  *string             `json:"customerShippingAddress" bson:"customer_shipping_address,omitempty"` // địa chỉ người nhận
	//CustomerDistrictCode     *string             `json:"customerDistrictCode" bson:"customer_district_code,omitempty"`       // khu vực nhận
	//CustomerWardCode         *string             `json:"customerWardCode" bson:"customer_ward_code,omitempty"`               //
	//TotalPrice               int64               `json:"totalPrice" bson:"total_price,omitempty"`                            // tổng tiền giỏ hàng sau cùng
	Price float64 `json:"price" bson:"price,omitempty"` // tổng tiền giỏ hàng sau cùng
	//SubTotalPrice            int64               `json:"subTotalPrice" bson:"sub_total_price,omitempty"`                     // tổng tiển chưa trừ các khoản khác
	//Status                   enum.CartStateValue `json:"status" bson:"status,omitempty"`
	TotalItem int                `json:"totalItem,omitempty" bson:"total_item,omitempty"`
	CartItems []CartItemInternal `json:"cartItems,omitempty" bson:"cart_items,omitempty"`
	//RegionCode               string              `json:"regionCode,omitempty" bson:"region_code,omitempty"`
	ProvinceCode      string              `json:"-" bson:"province_code,omitempty"`
	RegionCodes       []string            `json:"regionCodes,omitempty" bson:"region_codes,omitempty"` //
	RedeemCode        []string            `json:"redeemCode,omitempty" bson:"redeem_code,omitempty"`
	RedeemApplyResult []*PromoApplyResult `json:"redeemApplyResult,omitempty" bson:"redeem_apply_result,omitempty"` //
	SaleRegionCodes   []string            `json:"-" bson:"-"`
	ScopeRegionCodes  map[string][]string `json:"-" bson:"-"` //
	TagValue          map[string]struct {
		Quantity int64
		Total    float64
		Products int64
	} `json:"-"`
	ProductValue map[string]struct {
		Quantity int64
		Total    float64
	}
	SkuValue map[string]struct {
		Quantity int64
		Total    float64
	}
	ValidateOrder bool               `json:"validateOrder,omitempty" bson:"-"`
	SourceDetail  *OrderSourceDetail `json:"-" bson:"-"`
	IsCartEmpty   bool               `json:"" bson:"-"`

	NumberOfVoucherMap map[string]int  `json:"-" bson:"-"`
	VoucherInCartMap   map[string]bool `json:"-" bson:"-"`
}

type PromoApplyResult struct {
	Code        string `json:"code" bson:"code,omitempty"`
	CanUse      bool   `json:"canUse" bson:"can_use,omitempty"`
	Message     string `json:"message" bson:"message,omitempty"`
	Gift        []Gift `json:"gifts" bson:"gifts,omitempty"`
	AutoApply   bool   `json:"autoApply,omitempty" bson:"auto_apply,omitempty"`
	MatchSeller string `json:"matchSeller,omitempty" bson:"match_seller,omitempty"`

	DiscountValue float64 `json:"discountValue,omitempty" bson:"discount_value,omitempty"`

	SellerCode  string   `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	SellerCodes []string `json:"sellerCodes,omitempty" bson:"seller_codes,omitempty"`
}
