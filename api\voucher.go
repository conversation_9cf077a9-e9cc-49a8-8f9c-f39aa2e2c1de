package api

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"go.mongodb.org/mongo-driver/bson"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/buymed.th/marketplace/promotion/action"
	"gitlab.com/buymed.th/marketplace/promotion/model"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// GetVouchers get voucher
func GetVouchers(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		str       = req.GetParam("voucherId")
		sortField = req.GetParam("sortField")
		sortType  = req.GetParam("sortType")
	)
	if len(sortField) == 0 {
		sortField = "_id"
	}
	sortTypeInt := -1
	if sortType == "ASC" {
		sortTypeInt = 1
	}
	if str != "" {
		voucherID := sdk.ParseInt64(str, 0)
		if voucherID > 0 {
			return resp.Respond(action.GetVoucherByID(voucherID))
		}
	}

	str = req.GetParam("voucherCode")
	if str != "" {
		return resp.Respond(action.GetPromotionByCode(str))
	}

	id := sdk.ParseInt64(req.GetParam("customerId"), 0)
	if id != 0 {
		return resp.Respond(action.GetPromotionByCustomerID(id))
	}

	var voucher model.Voucher
	str = req.GetParam("q")
	if str == "" {
		str = "{}"
	}

	err := json.Unmarshal([]byte(str), &voucher)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không thể phân tích tham số truyền vào.",
			ErrorCode: "JSON_INVALID",
		})
	}

	if voucher.TimeFrom != nil {
		voucher.ComplexQuery = append(
			voucher.ComplexQuery,
			&primitive.M{
				"created_time": bson.M{
					"$gte": voucher.TimeFrom,
				},
			},
		)
	}

	if voucher.TimeTo != nil {
		voucher.ComplexQuery = append(
			voucher.ComplexQuery,
			&primitive.M{
				"created_time": bson.M{
					"$lte": voucher.TimeTo,
				},
			},
		)
	}

	if voucher.CustomerID != nil {
		voucher.ComplexQuery = append(voucher.ComplexQuery, &bson.M{
			"$or": []*bson.M{
				{
					"applied_customers": *voucher.CustomerID,
				},
				{
					"applied_customers": bson.M{"$size": 0},
				},
				{
					"applied_customers": nil,
				},
			},
		})
	}

	search := req.GetParam("search")
	if search != "" {
		searchItem := parserQ(search)
		voucher.ComplexQuery = append(voucher.ComplexQuery, &bson.M{
			"hash_tag": primitive.Regex{Pattern: fmt.Sprintf(".*%s.*", searchItem), Options: ""},
		})
	}

	var offset = int64(sdk.ParseInt(req.GetParam("offset"), 0))
	var limit = int64(sdk.ParseInt(req.GetParam("limit"), 20))
	var getTotal = req.GetParam("getTotal") == "true"

	tabType := req.GetParam("type")

	return resp.Respond(action.GetVouchers(&voucher, offset, limit, getTotal, sortField, sortTypeInt, tabType))
}

// VoucherGetList ...
func VoucherGetList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q         = req.GetParam("q")
		offset    = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit     = sdk.ParseInt64(req.GetParam("limit"), 1000)
		getTotal  = req.GetParam("getTotal") == "true"
		codes     = req.GetParam("voucherCodes")
		ids       = req.GetParam("voucherIds")
		sortField = req.GetParam("sortField")
		sortType  = req.GetParam("sortType")
	)
	if len(sortField) == 0 {
		sortField = "_id"
	}
	sortTypeInt := -1
	if sortType == "ASC" {
		sortTypeInt = 1
	}
	var query = model.Voucher{}
	if len(q) > 0 {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Không thể đọc dữ liệu",
				ErrorCode: "PAYLOAD_INVALID",
			})
		}
	}

	if len(codes) > 0 {
		codesArr := strings.Split(codes, ",")
		query.ComplexQuery = []*bson.M{
			{
				"code": &bson.M{
					"$in": codesArr,
				},
			},
		}
	}

	if len(ids) > 0 {
		idsArr := strings.Split(ids, ",")
		listIDs := []int64{}
		for _, id := range idsArr {
			intID, _ := strconv.Atoi(id)
			if intID > 0 {
				listIDs = append(listIDs, int64(intID))
			}
		}
		query.ComplexQuery = []*bson.M{
			{
				"voucher_id": &bson.M{
					"$in": listIDs,
				},
			},
		}
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.GetListVoucher(&query, offset, limit, getTotal, sortField, sortTypeInt))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

//
//// GetActiveVoucher get active voucher
//func GetActiveVoucher(req sdk.APIRequest, resp sdk.APIResponder) error {
//	acc := getActionSource(req)
//	if acc == nil {
//		return resp.Respond(&common.APIResponse{
//			Status:    common.APIStatus.Unauthorized,
//			Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
//			ErrorCode: "ACTION_NOT_FOUND",
//		})
//	}
//
//	str := req.GetParam("voucherCode")
//	if str != "" {
//		return resp.Respond(action.SearchActiveVoucher(str, acc))
//	}
//	return resp.Respond(action.GetActiveVoucher(nil, acc))
//}
//
//// GetAvailableVoucher get active voucher
//func GetAvailableVoucher(req sdk.APIRequest, resp sdk.APIResponder) error {
//	var input model.CartCheckRequest
//	if err := req.GetContent(&input); err != nil {
//		return resp.Respond(&common.APIResponse{
//			Status:    common.APIStatus.Invalid,
//			Message:   "Không thể phân tích tham số truyền vào.",
//			ErrorCode: "JSON_INVALID",
//		})
//	}
//
//	str := req.GetParam("voucherCode")
//	if str != "" {
//		return resp.Respond(action.SearchActiveVoucher(str, &model.Account{
//			AccountID: input.AccountID,
//		}))
//	}
//	return resp.Respond(action.GetActiveVoucher(input.Cart, &model.Account{
//		AccountID: input.AccountID,
//	}))
//}

// CreateVoucher create a voucher
func CreateVoucher(req sdk.APIRequest, resp sdk.APIResponder) error {
	var voucher model.Voucher
	err := req.GetContent(&voucher)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không thể phân tích tham số truyền vào.",
			ErrorCode: "JSON_INVALID",
		})
	}

	// Check user info
	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Không thể phân tích thông tin xác thực.",
				ErrorCode: "TOKEN_INVALID",
			})
		}
	}

	if UserInfo.Account == nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Thiếu thông tin xác thực.",
			ErrorCode: "TOKEN_INVALID",
		})
	}

	return resp.Respond(action.CreateVoucher(&voucher, UserInfo.Account))
}

func CreateManyVoucher(req sdk.APIRequest, resp sdk.APIResponder) error {
	var vouchers model.CreateManyVoucherRequest
	err := req.GetContent(&vouchers)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không thể phân tích tham số truyền vào.",
			ErrorCode: "JSON_INVALID",
		})
	}

	// Check user info
	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Không thể phân tích thông tin xác thực.",
				ErrorCode: "TOKEN_INVALID",
			})
		}
	}

	if UserInfo.Account == nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Thiếu thông tin xác thực.",
			ErrorCode: "TOKEN_INVALID",
		})
	}

	return resp.Respond(action.CreateManyVouchers(&vouchers, UserInfo.Account))
}

// ListVoucherGetByCodes is func to get list vouchers by list codes
func ListVoucherGetByCodes(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.ListVoucherCodesRequest
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data. " + err.Error(),
		})
	}

	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	if lt := len(input.VoucherCodes); lt > 0 {
		if lt > 100 {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Data limit 100 records",
			})
		}
	}

	return resp.Respond(action.GetVouchersByCodes(input.VoucherCodes))
}

// UpdateVoucher update a voucher
func UpdateVoucher(req sdk.APIRequest, resp sdk.APIResponder) error {
	var voucher model.UpdateVoucherRequest
	if err := req.GetContent(&voucher); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data. " + err.Error(),
		})
	}

	// Check user info
	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Không thể phân tích thông tin xác thực.",
				ErrorCode: "TOKEN_INVALID",
			})
		}
	}

	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Thiếu thông tin xác thực.",
			ErrorCode: "TOKEN_INVALID",
		})
	}

	return resp.Respond(action.UpdateVoucher(&voucher, UserInfo.Account.AccountID))
}

// RefundVoucher is func to refund voucher
func RefundVoucher(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.PromotionRefundRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}
	if input.VoucherCode != "" && len(input.VoucherCodes) == 0 {
		input.VoucherCodes = append(input.VoucherCodes, input.VoucherCode)
	}
	return resp.Respond(action.RefundVoucher(&input))
}

// UpdateVoucherStatus update a voucher status
func UpdateVoucherStatus(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.Voucher
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}

	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}

	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}

	return resp.Respond(action.UpdateVoucherStatus(&input, UserInfo.Account.AccountID))
}

func GetCustomerVouchers(req sdk.APIRequest, resp sdk.APIResponder) error {
	var offset = int64(sdk.ParseInt(req.GetParam("offset"), 0))
	var limit = int64(sdk.ParseInt(req.GetParam("limit"), 20))
	var getTotal = req.GetParam("getTotal") == "true"
	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}

	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}

	return resp.Respond(action.GetCustomerVouchers(UserInfo.Account.AccountID, offset, limit, getTotal))
}

// MigrateHashTagVoucher update hash tag for voucher data
func MigrateHashTagVoucher(req sdk.APIRequest, resp sdk.APIResponder) error {
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.MigrateHashTagVoucher())
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

// DeleteAppliedCustomers delete data appliedCustomers in voucher
func DeleteAppliedCustomers(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.DeleteAppliedCustomersRequest
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}

	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.DeleteAppliedCustomers(&input, acc.AccountID))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

// GetVoucherUsageHistoryList get list voucher usage history
func GetVoucherUsageHistoryList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q        = req.GetParam("q")
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		tabType  = req.GetParam("type")
	)

	var query = model.Voucher{}
	if len(q) > 0 {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Không thể đọc dữ liệu",
				ErrorCode: "PAYLOAD_INVALID",
			})
		}
	}

	if query.CustomerID != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"$or": []*bson.M{
				{
					"applied_customers": *query.CustomerID,
				},
				{
					"applied_customers": bson.M{"$size": 0},
				},
				{
					"applied_customers": nil,
				},
			},
		})
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.GetVoucherUsageHistoryList(&query, offset, limit, getTotal, tabType))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})

}

// GetVoucherBySku is func get voucher info
func GetVoucherBySku(req sdk.APIRequest, resp sdk.APIResponder) error {
	var payload struct {
		Skus           []string `json:"skus,omitempty"`
		GetVoucherInfo bool     `json:"getVoucherInfo"`
		Offset         int      `json:"offset"`
		Limit          int      `json:"limit"`
		GetTotal       bool     `json:"getTotal"`
	}
	if err := req.GetContent(&payload); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if err := model.Checker.Validate(&payload); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}
	return resp.Respond(action.GetVoucherBySku(getActionSource(req), payload.Skus, payload.GetVoucherInfo, payload.Offset, payload.Limit, payload.GetTotal))
}

func CreateMultipleVoucher(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.CreateMMultiVoucherRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "PAYLOAD_INVALID",
		})
	}
	limitData := 100
	if (len(input.Data)) > limitData {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: fmt.Sprintf("Data over limit %d", limitData),
		})
	}

	if acc := getActionSource(req); acc != nil {

		return resp.Respond(action.CreateMultipleVoucher(acc, &input))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}
