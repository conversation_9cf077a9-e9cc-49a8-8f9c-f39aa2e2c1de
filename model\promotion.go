package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/buymed.th/marketplace/promotion/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type Promotion struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	CreatedBy       int64              `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	UpdatedBy       int64              `json:"updatedBy,omitempty" bson:"updated_by,omitempty"`
	VersionNo       string             `json:"versionNo,omitempty" bson:"version_no,omitempty"`

	PromotionID        int64                         `json:"promotionId,omitempty" bson:"promotion_id,omitempty"`
	CampaignID         int64                         `json:"campaignId,omitempty" bson:"campaign_id,omitempty"`
	PromotionName      string                        `json:"promotionName,omitempty" bson:"promotion_name,omitempty"`
	ShortName          string                        `json:"shortName,omitempty" bson:"short_name,omitempty"`
	Slug               string                        `json:"slug,omitempty" bson:"slug,omitempty"`
	PromotionType      *enum.PromotionTypeValue      `json:"promotionType,omitempty" bson:"promotion_type,omitempty"`
	ApplyType          enum.ApplyTypeValue           `json:"applyType,omitempty" bson:"apply_type,omitempty"`
	PromotionOrganizer *enum.PromotionOrganizerValue `json:"promotionOrganizer,omitempty" bson:"promotion_organizer,omitempty"`
	Icon               string                        `json:"icon,omitempty" bson:"icon,omitempty"`
	Description        string                        `json:"description,omitempty" bson:"description,omitempty"`
	Status             *enum.PromotionStatusValue    `json:"status,omitempty" bson:"status,omitempty"`
	VoucherGroupCode   *string                       `json:"voucherGroupCode,omitempty" bson:"voucher_group_code,omitempty"`
	VoucherImage       *string                       `json:"voucherImage,omitempty" bson:"voucher_image,omitempty"`
	Tag                *string                       `json:"tag,omitempty" bson:"tag,omitempty"`

	StartTime  *time.Time `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime    *time.Time `json:"endTime,omitempty" bson:"end_time,omitempty"`
	PublicTime *time.Time `json:"publicTime,omitempty" bson:"public_time,omitempty"`

	Scopes       []Scope     `json:"scopes,omitempty" bson:"scopes,omitempty"`
	Conditions   []Condition `json:"conditions" bson:"conditions,omitempty"`
	Rewards      *[]Reward   `json:"rewards,omitempty" bson:"rewards,omitempty"`
	VoucherCodes []*Voucher  `json:"voucherCodes,omitempty" bson:"-"`
	ComplexQuery []*bson.M   `json:"-" bson:"$and,omitempty"`

	OrConditions         map[enum.ConditionTypeValue]PromotionType `json:"orConditions,omitempty" bson:"or_conditions,omitempty"`
	AndConditions        map[enum.ConditionTypeValue]PromotionType `json:"andConditions,omitempty" bson:"and_conditions,omitempty"`
	ConditionDescription *string                                   `json:"conditionDescription,omitempty" bson:"condition_description,omitempty"`
	HashTag              string                                    `json:"-" bson:"hash_tag,omitempty"`

	MaxAutoApplyCount   *int64                 `json:"maxAutoApplyCount,omitempty" bson:"max_auto_apply_count,omitempty"`
	MaxUsage            *int64                 `json:"maxUsage" bson:"max_usage,omitempty"`
	MaxUsagePerCustomer *int64                 `json:"maxUsagePerCustomer" bson:"max_usage_per_customer,omitempty"`
	Priority            *int                   `json:"priority,omitempty" bson:"priority,omitempty"`
	VoucherType         *enum.VoucherTypeValue `json:"type,omitempty" bson:"type,omitempty"`

	// ApplyDiscount *ApplyDiscountOptions `json:"applyDiscount,omitempty" bson:"apply_discount,omitempty"`

	LevelScopeMap    map[string]bool `json:"-" bson:"level_scope_map,omitempty"`
	RegionScopeMap   map[string]bool `json:"-" bson:"region_scope_map,omitempty"`
	CustomerScopeMap map[string]bool `json:"-" bson:"customer_scope_map,omitempty"`
}

type Scope struct {
	Type               *enum.ScopeTypeValue    `json:"type,omitempty" bson:"type,omitempty"`
	QuantityType       *enum.QuantityTypeValue `json:"quantityType,omitempty" bson:"quantity_type,omitempty"`
	Products           []int64                 `json:"products,omitempty" bson:"products,omitempty"`
	ProducerCodes      []string                `json:"producerCodes,omitempty" bson:"producer_codes,omitempty"`
	SellerCodes        []string                `json:"sellerCodes,omitempty" bson:"seller_codes,omitempty"`
	CategoryCodes      []string                `json:"categoryCodes,omitempty" bson:"category_codes,omitempty"`
	IngredientCodes    []string                `json:"ingredientCodes,omitempty" bson:"ingredient_codes,omitempty"`
	AreaCodes          []string                `json:"areaCodes,omitempty" bson:"area_codes,omitempty"`
	CustomerLevelCodes []string                `json:"customerLevelCodes,omitempty" bson:"customer_level_codes,omitempty"`
	CustomerScopes     []string                `json:"customerScopes,omitempty" bson:"customer_scopes,omitempty"`
	ProductTags        []string                `json:"productTags,omitempty" bson:"product_tags,omitempty"`
	RegisteredBefore   *time.Time              `json:"registeredBefore,omitempty" bson:"registered_before,omitempty"`
	RegisteredAfter    *time.Time              `json:"registeredAfter,omitempty" bson:"registered_after,omitempty"`
	DisplayPlatforms   []enum.PlatformType     `json:"displayPlatforms,omitempty" bson:"display_platforms,omitempty"`
	MinClientVersion   *string                 `json:"minClientVersion,omitempty" bson:"min_client_version,omitempty"`
}

type Condition struct {
	Type              *enum.ConditionTypeValue `json:"type,omitempty" bson:"type,omitempty"`
	MinOrderValue     *int64                   `json:"minOrderValue,omitempty" bson:"min_order_value,omitempty"`
	ProductConditions []ProductCondition       `json:"productConditions,omitempty" bson:"product_conditions,omitempty"`
	// CustomerHistoryCondition *CustomerCondition       `json:"customerHistoryCondition,omitempty" bson:"customer_history_condition,omitempty"`

	// FIXME: tách riêng thành điều kiện theo customer, hiện tại do vướng UI nên phải để là điều kiện của order
	MinTotalOrder  *int64 `json:"minTotalOrder,omitempty" bson:"min_total_order,omitempty"`
	MaxTotalOrder  *int64 `json:"maxTotalOrder,omitempty" bson:"max_total_order,omitempty"`
	MinDaysNoOrder *int64 `json:"minDaysNoOrder,omitempty" bson:"min_days_no_order,omitempty"`
}

type Reward struct {
	Type               *enum.RewardTypeValue `json:"type,omitempty" bson:"type,omitempty"`
	PercentageDiscount float64               `json:"percentageDiscount,omitempty" bson:"percentage_discount,omitempty"`
	AbsoluteDiscount   float64               `json:"absoluteDiscount,omitempty" bson:"absolute_discount,omitempty"`
	MaxDiscount        float64               `json:"maxDiscount,omitempty" bson:"max_discount,omitempty"`
	PointValue         int64                 `json:"pointValue,omitempty" bson:"point_value,omitempty"`
	Gifts              []Gift                `json:"gifts,omitempty" bson:"gifts,omitempty"`
	TotalGiftValue     float64               `json:"totalGiftValue,omitempty" bson:"total_gift_value,omitempty"`
}

type ProductCondition struct {
	SellerCodes        []string                `json:"sellerCodes,omitempty" bson:"seller_codes,omitempty"`
	SellerQuantityType *enum.QuantityTypeValue `json:"sellerQuantityType,omitempty" bson:"seller_quantity_type,omitempty"`
	ProductID          int64                   `json:"productId,omitempty" bson:"product_id,omitempty"`
	ProductTag         string                  `json:"productTag,omitempty" bson:"product_tag,omitempty"`
	CategoryCode       string                  `json:"categoryCode,omitempty" bson:"category_code,omitempty"`
	IngredientCode     string                  `json:"ingredientCode,omitempty" bson:"ingredient_code,omitempty"`
	ProducerCode       string                  `json:"producerCode,omitempty" bson:"producer_code,omitempty"`
	MinQuantity        int64                   `json:"minQuantity,omitempty" bson:"min_quantity,omitempty"`
	MinTotalValue      float64                 `json:"minTotalValue,omitempty" bson:"min_total_value,omitempty"`
}

type CustomerCondition struct {
	MinTotalOrder       *int64 `json:"minTotalOrder,omitempty" bson:"min_total_order,omitempty"`
	MaxTotalOrder       *int64 `json:"maxTotalOrder,omitempty" bson:"max_total_order,omitempty"`
	MinDayFromLastOrder *int64 `json:"minDayFromLastOrder,omitempty" bson:"min_day_from_last_order,omitempty"`
}

type OrderCondition struct {
	MinOrderValue *int64 `json:"minOrderValue,omitempty" bson:"min_order_value,omitempty"`
}

type Gift struct {
	Sku       string  `json:"sku,omitempty" bson:"sku,omitempty"`
	Quantity  int64   `json:"quantity,omitempty" bson:"quantity,omitempty"`
	GiftValue float64 `json:"giftValue,omitempty" bson:"gift_value,omitempty"`
}

var PromotionDB = &db.Instance{
	ColName:        "promotion",
	TemplateObject: &Promotion{},
}

// InitPromotionModel is func init model
func InitPromotionModel(s *mongo.Database) {
	PromotionDB.ApplyDatabase(s)

	// t := true
	// PromotionDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "promotion_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })

	// PromotionDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "account_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// PromotionDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "status", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// // index for query expired promotion
	// PromotionDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "end_time", Value: 1},
	// 	primitive.E{Key: "status", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// // index for query need to start promotion
	// PromotionDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "start_time", Value: 1},
	// 	primitive.E{Key: "status", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
}
