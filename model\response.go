package model

import (
	"time"

	"gitlab.com/buymed.th/marketplace/promotion/model/enum"
)

// CustomerLevelResponse is model define CustomerLevelResponse
type CustomerLevelResponse struct {
	BaseAPIResponse
	Data []*Level `json:"data"`
}

// Level is model define Level
type Level struct {
	LevelID  int64   `json:"levelId"`
	Name     string  `json:"name"`
	Code     string  `json:"code"`
	FeeValue float64 `json:"feeValue"`
}

type OrderGetResponse struct {
	BaseAPIResponse
	Data []*Order `json:"data"`
}

// CustomerResponse is resp from customer client
type CustomerResponse struct {
	BaseAPIResponse
	Data []*Customer `json:"data"`
}

type DetailVoucherResponse struct {
	Voucher       *Voucher       `json:"voucher,omitempty"`
	UserPromotion *UserPromotion `json:"userPromotion,omitempty"`
}

type ViewVoucherResponse struct {
	Voucher            *VoucherViewWebOnly `json:"voucher,omitempty"`
	UserPromotion      *UserPromotion      `json:"userPromotion,omitempty"`
	AvailableQuantity  *int64              `json:"availableQuantity,omitempty"`
	CustomerUsageTotal *int64              `json:"customerUsageTotal,omitempty"`
	IsUnlimited        bool                `json:"isUnlimited,omitempty"`
}

// Customer ...
type Customer struct {
	CustomerID    int64      `json:"customerID"`
	AccountID     int64      `json:"accountID"`
	Name          string     `json:"name"`
	Username      string     `json:"username"`
	Scope         string     `json:"scope"`
	Level         string     `json:"level"`
	Point         float64    `json:"point"`
	ProvinceCode  string     `json:"provinceCode"`
	DistrictCode  string     `json:"districtCode"`
	WardCode      string     `json:"wardCode"`
	IsActive      int        `json:"isActive"`
	Status        string     `json:"status"`
	OrderCount    int        `json:"ordersCount"`
	LastOrderTime *time.Time `json:"lastOrderAt"`
	ConfirmedTime *time.Time `json:"confirmedTime"`
	CreatedTime   *time.Time `json:"createdTime,omitempty"`
	Phone         string     `json:"phone,omitempty"`
	Tags          []string   `json:"tags,omitempty"`
}

// BaseAPIResponse ...
type BaseAPIResponse struct {
	Status    string      `json:"status"`
	Message   string      `json:"message"`
	ErrorCode string      `json:"errorCode,omitempty"`
	Data      interface{} `json:"data"`
}

type SummationOrderInfo struct {
	CustomerID int64  `json:"customerId"`
	LastOrder  *Order `json:"lastOrder"`  // Đơn hàng gần nhất đã được confirm
	TotalOrder int64  `json:"totalOrder"` // Tổng đơn hàng đã được confirm
}

type CountOrderPointResponse struct {
	BaseAPIResponse
	Data []*OrderCountPoint `json:"data"`
}

type Sku struct {
	Code                string               `json:"code,omitempty" bson:"code,omitempty"`
	ProductCode         string               `json:"productCode,omitempty" bson:"product_code,omitempty"`
	ProductID           int64                `json:"productID,omitempty" bson:"product_id,omitempty"`
	SellerCode          string               `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	CampaignCode        string               `json:"campaignCode,omitempty" bson:"campaign_code,omitempty"`
	MaxQuantityPerOrder int64                `json:"maxQuantityPerOrder,omitempty" bson:"max_quantity_per_order,omitempty"`
	RetailPriceValue    float64              `json:"retailPriceValue,omitempty" bson:"retail_price_value,omitempty"`
	Status              *enum.SkuStatusValue `json:"status,omitempty" bson:"status,omitempty"`
	IsActive            *bool                `json:"isActive,omitempty" bson:"is_active,omitempty"`
	LocationCodes       []string             `json:"locationCodes,omitempty" bson:"location_codes,omitempty"`
	Fulfill             *float64             `json:"fulfill,omitempty" bson:"fulfill,omitempty"`
}

type Product struct {
	Code          string   `json:"code,omitempty"`
	CategoryCodes []string `json:"categoryCodes,omitempty"`
	// B2B-397

	ProductID             int64   `json:"productID,omitempty" bson:"product_id,omitempty"`
	SellerCategoryCode    string  `json:"sellerCategoryCode,omitempty" bson:"seller_category_code,omitempty"`
	SellerSubCategoryCode *string `json:"sellerSubCategoryCode,omitempty" bson:"seller_sub_category_code,omitempty"`
}

type SkuResponse struct {
	BaseAPIResponse
	Data []*Sku `json:"data"`
}

type ProductResponse struct {
	BaseAPIResponse
	Data []*Product `json:"data"`
}

// CampaignPrdResponse ...
type CampaignPrdResponse struct {
	Sku     string `json:"sku"`
	Message string `json:"message"`
}

// Notification ...
type Notification struct {
	Code         string  `json:"code,omitempty" bson:"code,omitempty" `
	UserID       int64   `json:"userId,omitempty" bson:"user_id,omitempty" `
	Username     string  `json:"username,omitempty" bson:"username,omitempty"`
	ReceiverType *string `json:"receiverType,omitempty" bson:"receiver_type,omitempty"`
	IsRead       *bool   `json:"isRead,omitempty" bson:"is_read,omitempty" `
	Topic        string  `json:"topic,omitempty" bson:"topic,omitempty"`
	Title        string  `json:"title,omitempty" bson:"title,omitempty"`
	Description  string  `json:"description,omitempty" bson:"description,omitempty" `
	Link         string  `json:"link,omitempty" bson:"link,omitempty" `
	Onwer        string  `json:"owner,omitempty" bson:"owner,omitempty"`
}

type SkuMainResponse struct {
	BaseAPIResponse
	Data []*SkuMain `json:"data"`
}
