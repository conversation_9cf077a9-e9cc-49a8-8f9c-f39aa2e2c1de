package api

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/buymed.th/marketplace/promotion/action"
	"gitlab.com/buymed.th/marketplace/promotion/model"
)

// CheckSyncCampaignTask ...
func CheckSyncCampaignTask(req sdk.APIRequest, resp sdk.APIResponder) error {
	action.CheckSyncCampaign()
	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "",
	})
}

func MigrateVoucherData(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input = struct {
		Type string `json:"type"`
	}{}
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.<PERSON>(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}
	switch input.Type {
	case "WarmUpAllVoucher":
		return resp.Respond(action.WarmUpAllVoucher())
	case "WarmUpAllVoucherActive":
		return resp.Respond(action.WarmUpAllVoucher())
	case "WarmUpAllUserPromotion":
		return resp.Respond(action.WarmUpAllUserPromotion())
	case "MigrateOldPromotion":
		return resp.Respond(action.MigrateOldPromotion())
	case "MigrateOldVoucher":
		return resp.Respond(action.MigrateOldVoucher())
	case "MigrateOldUserPromotion":
		return resp.Respond(action.MigrateOldUserPromotion())
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Invalid,
		Message:   "",
		ErrorCode: "",
	})
}
