package model

import (
	"gitlab.com/buymed.th/marketplace/promotion/model/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

type Code struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	CreatedBy       int64              `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	UpdatedBy       int64              `json:"updatedBy,omitempty" bson:"updated_by,omitempty"`

	Code            string                `json:"code" bson:"code"`
	PromotionID     int64                 `json:"promotionId" bson:"promotion_id"`
	Status          *enum.CodeStatusValue `json:"status,omitempty" bson:"status,omitempty"`
	CodeType        *enum.CodeTypeValue   `json:"code_type,omitempty" bson:"code_type,omitempty"`
	Number          int64                 `json:"number,omitempty" bson:"number,omitempty"`
	CollectedNumber int64                 `json:"collected_number,omitempty" bson:"collected_number,omitempty"`
	UsedNumber      int64                 `json:"used_number,omitempty" bson:"used_number,omitempty"`
}
