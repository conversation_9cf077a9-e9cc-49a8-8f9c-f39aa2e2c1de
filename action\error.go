package action

var VoucherError = map[string]string{
	"NOT_FOUND":                    "Voucher not found",
	"INACTIVE":                     "Voucher inactive",
	"NOT_START_YET":                "Voucher has not start yet",
	"EXPIRED":                      "Voucher expired",
	"NOT_ENOUGH_CONDITION":         "You are not enough condition to use this voucher",
	"OUT_OF_STOCK":                 "Voucher is out of stock",
	"CAN_NOT_USE":                  "You can not use this voucher",
	"OUT_OF_USE":                   "Voucher is out of use",
	"NOT_SUPPORT_REGION":           "Your region is unsupported",
	"NOT_SUPPORT_LEVEL":            "Your level is unsupported",
	"NOT_SUPPORT_SCOPE":            "Your scope is unsupported",
	"NOT_ENOUGH_ORDER_VALUE":       "Order value is not enough",
	"NUMBER_OF_ORDER_EXCEED_LIMIT": "The number of orders exceeds the limit",
	"NOT_ENOUGH_NUMBER_OF_ORDER":   "The number of orders is not enough",
	"INVALID_NUMBER_OF_ORDER":      "The number of orders is invalid",
	"NOT_ENOUGH_NO_ORDER_TIME":     "No-order time is not enough",
	"INVALID_PRODUCT":              "Voucher is only applicable for some products",
	"NOT_ENOUGH_PRODUCT_QUANTITY":  "Product quantity is not enough",
	"NOT_ENOUGH_PRODUCT_COUNT":     "Number of products is not enough",
	"NOT_ENOUGH_PRODUCT_AMOUNT":    "Total product amount is not enough",
	"INVALID_APP_VERSION":          "Applicable to other devices according to conditions",
}
