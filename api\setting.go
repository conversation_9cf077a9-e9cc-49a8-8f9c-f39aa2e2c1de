package api

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/buymed.th/marketplace/promotion/action"
	"gitlab.com/buymed.th/marketplace/promotion/model"
)

// SettingGet is func get setting
func SettingGet(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.GetSetting())
}

// SettingUpdate is func update setting
func SettingUpdate(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.Setting
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không thể đọc dữ liệu",
		})
	}
	return resp.Respond(action.UpdateSetting(&input))
}
