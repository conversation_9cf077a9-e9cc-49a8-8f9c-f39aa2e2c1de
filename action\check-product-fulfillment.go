package action

import (
	"fmt"
	"math"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/buymed.th/marketplace/promotion/client"
	"gitlab.com/buymed.th/marketplace/promotion/conf"
	"gitlab.com/buymed.th/marketplace/promotion/model"
	"gitlab.com/buymed.th/marketplace/promotion/model/enum"
	"gitlab.com/buymed.th/marketplace/promotion/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func GetCheckProductFulfillmentLogList(query *model.CheckProductFulfillmentLog, offset, limit int64, getTotal bool) *common.APIResponse {
	result := model.CheckProductFulfillmentLogDB.Query(query, offset, limit, &primitive.M{"created_time": -1})
	if result.Status != common.APIStatus.Ok {
		return result
	}
	if getTotal {
		result.Total = model.CheckProductFulfillmentLogDB.Count(query).Total
	}
	return result
}

func GetCheckProductFulfillmentDetailList(query *model.CheckProductFulfillmentDetail, offset, limit int64, getTotal bool) *common.APIResponse {
	result := model.CheckProductFulfillmentDetailDB.Query(query, offset, limit, &primitive.M{"created_time": -1})
	if result.Status != common.APIStatus.Ok {
		return result
	}
	if getTotal {
		result.Total = model.CheckProductFulfillmentDetailDB.Count(query).Total
	}
	return result
}

func CheckCampaignProductFulfill(acc *model.Account) *common.APIResponse {
	go func() {
		checkProductFulfillment(acc, enum.CheckProductFulfillment.MANUAL)
	}()
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "In progress",
	}
}

// CheckProductFulfillmentTask ...
func CheckProductFulfillmentTask(item *job.JobItem) (returnErr error) {
	if conf.Config.Env == "uat" {
		return nil
	}
	now := time.Now()
	checkProductFulfillment(nil, enum.CheckProductFulfillment.AUTO)
	lastDay := time.Date(now.Year(), now.Month(), now.Day()+1, 17, 0, 0, 0, now.Location())
	model.CheckProductFulfillmentJobExecutor.Push(nil, &job.JobItemMetadata{
		Topic:     "default",
		ReadyTime: &lastDay,
	})
	return nil
}

func checkProductFulfillment(acc *model.Account, checkCampaignProductFulfillType enum.CheckProductFulfillmentType) *common.APIResponse {
	now := time.Now()
	campaignList := getListCampaignToCheckFulfill()
	logCodes := make([]string, 0)
	listLog := make(map[string]*model.CheckProductFulfillmentLog)

	for _, cam := range campaignList {
		log := &model.CheckProductFulfillmentLog{
			StartTime:       &now,
			CampaignID:      cam.CampaignID,
			CampaignCode:    cam.CampaignCode,
			CampaignFulfill: cam.Fulfill,
			Status:          enum.CampaignLogStatus.IN_PROCESS,
			Type:            checkCampaignProductFulfillType,
		}
		if acc != nil {
			log.AccountID = acc.AccountID
		}
		log.Code = model.GenCodeWithTime()
		listLog[cam.CampaignCode] = log
		logCodes = append(logCodes, log.Code)

		_ = model.CheckProductFulfillmentLogDB.Create(log)
	}

	for _, cam := range campaignList {
		log := listLog[cam.CampaignCode]
		if log != nil {
			checkProductFulfillmentInCampaign(cam, log, acc, logCodes)
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Check fulfillment successfully",
	}
}

func checkProductFulfillmentInCampaign(cam *model.Campaign, log *model.CheckProductFulfillmentLog, acc *model.Account, logCodes []string) {
	go func() {
		campaignProductList := getListCampaignProductToCheckFulfill(cam.CampaignCode)

		validItem := 0
		invalidItem := 0
		listDetail := make([]*model.CheckProductFulfillmentDetail, 0)

		for _, item := range campaignProductList {
			detailCode := model.GenCodeWithTime()
			checkResultDetail := &model.CheckProductFulfillmentDetail{
				Code:                 detailCode,
				CheckFulfillmentCode: log.Code,
				CampaignProductCode:  item.CampaignProductCode,
				ProductCode:          item.ProductCode,
				Sku:                  item.Sku,
				Status:               "SUCCESS",
			}

			skuRes, errSku := client.Services.Product.GetSku(item.Sku)
			if errSku != nil {
				checkResultDetail.Status = "FAIL"
				checkResultDetail.FailReason = errSku.Error()
			} else if skuRes.Fulfill == nil || cam.Fulfill == nil || (skuRes.Fulfill != nil && cam.Fulfill != nil && *skuRes.Fulfill >= *cam.Fulfill) {
				checkResultDetail.Fulfill = skuRes.Fulfill
				checkResultDetail.IsValid = utils.ParseBoolToPointer(true)
				validItem++
			} else {
				FALSE := false
				checkResultDetail.Fulfill = skuRes.Fulfill
				checkResultDetail.IsValid = &FALSE

				failReason := fmt.Sprintf("Sản phẩm có tỉ lệ fulfillment là %v%% không đạt tỉ lệ fulfill tối thiểu của chương trình", math.Round(*skuRes.Fulfill*100)/100)
				checkResultDetail.FailReason = failReason

				dt := time.Now().Add(7 * time.Hour)
				note := fmt.Sprintf("Sản phẩm có tỉ lệ fulfillment (%v%%) không đạt vào thời điểm duyệt chương trình %s", math.Round(*skuRes.Fulfill*100)/100, dt.Format("02-01-2006 15:04:05"))

				res := model.CampaignProductDB.UpdateOne(&model.CampaignProduct{
					CampaignProductCode: item.CampaignProductCode,
				}, &model.CampaignProduct{IsActive: &FALSE, PrivateNote: note})
				if res.Status == common.APIStatus.Ok {
					WarmUpCampaignProduct(item.Sku, cam.CampaignCode, nil)
					WarmupSkuSaleInfo(&model.SkuSaleInfo{
						SkuCode:             item.Sku,
						CampaignCode:        &item.CampaignCode,
						CampaignProductCode: &item.CampaignProductCode,
						IsActive:            &FALSE,
					})
				}
				invalidItem++
			}
			listDetail = append(listDetail, checkResultDetail)

			if len(listDetail) == 500 {
				_ = model.CheckProductFulfillmentDetailDB.CreateMany(listDetail)
				listDetail = make([]*model.CheckProductFulfillmentDetail, 0)
			}
		}

		if len(listDetail) > 0 {
			_ = model.CheckProductFulfillmentDetailDB.CreateMany(listDetail)
		}

		end := time.Now()
		_ = model.CheckProductFulfillmentLogDB.UpdateOne(model.CheckProductFulfillmentLog{Code: log.Code},
			model.CheckProductFulfillmentLog{
				EndTime:                         &end,
				Status:                          enum.CampaignLogStatus.DONE,
				Total:                           len(campaignProductList),
				NumberOfInactiveCampaignProduct: invalidItem,
				NumberOfActiveCampaignProduct:   validItem,
			},
		)
		if acc != nil {
			go func() {
				isCheckProductFulfillmentDone(logCodes, acc)
			}()
		}
	}()
}

func getListCampaignToCheckFulfill() []*model.Campaign {
	campaignList := make([]*model.Campaign, 0)
	now := time.Now()

	query := model.Campaign{
		IsActive: utils.ParseBoolToPointer(true),
		ComplexQuery: []*bson.M{
			{
				"start_time": bson.M{"$lte": now},
				"end_time":   bson.M{"$gte": now},
			},
		}}

	if qCampaign := model.CampaignDB.Query(query, 0, 0, nil); qCampaign.Status == common.APIStatus.Ok {
		campaignList = qCampaign.Data.([]*model.Campaign)
	}
	return campaignList
}

func getListCampaignProductToCheckFulfill(campaignCode string) []*model.CampaignProduct {
	campaignProductList := make([]*model.CampaignProduct, 0)
	internalSellers := []string{"BUYMED", "MARKETING"}
	query := model.CampaignProduct{
		CampaignCode: campaignCode,
		IsActive:     utils.ParseBoolToPointer(true),
		Status:       enum.CampaignProductStatus.NORMAL,
		ComplexQuery: []*bson.M{
			{
				"seller_code": &bson.M{
					"$nin": internalSellers,
				},
			},
		},
	}

	if qCampaignProduct := model.CampaignProductDB.Query(query, 0, 0, nil); qCampaignProduct.Status == common.APIStatus.Ok {
		campaignProductList = qCampaignProduct.Data.([]*model.CampaignProduct)
	}
	return campaignProductList
}

func logCheckProductFulfillmentFail(log *model.CheckProductFulfillmentLog, err *common.APIResponse) {
	now := time.Now()
	model.CheckProductFulfillmentLogDB.UpdateOne(model.CheckProductFulfillmentLog{Code: log.Code},
		model.CheckProductFulfillmentLog{Status: enum.CampaignLogStatus.FAIL, FailReason: fmt.Sprintf("%s-%s", err.ErrorCode, err.Message), EndTime: &now})
}

func isCheckProductFulfillmentDone(logCodes []string, acc *model.Account) {
	logHasDone := make([]string, 0)

	var query = model.CheckProductFulfillmentLog{}
	query.ComplexQuery = make([]*bson.M, 0)

	if len(logCodes) > 0 {
		query.ComplexQuery = []*bson.M{
			{
				"code": &bson.M{
					"$in": logCodes,
				},
			},
		}
	}

	resp := GetCheckProductFulfillmentLogList(&query, 0, 100, true)
	if resp.Status == common.APIStatus.Ok && resp.Data != nil {
		listLog := resp.Data.([]*model.CheckProductFulfillmentLog)
		for _, log := range listLog {
			if log.Status == enum.CampaignLogStatus.DONE {
				logHasDone = append(logHasDone, log.Code)
			}
		}
	}

	if len(logCodes) == len(logHasDone) {
		_ = client.Services.Notification.CreateNotification(&model.Notification{
			Username:     acc.Username,
			UserID:       acc.AccountID,
			ReceiverType: utils.ParseStringToPointer("EMPLOYEE"),
			Topic:        "ANNOUNCEMENT",
			Title:        fmt.Sprintln("Fulfillment rate checking for the products has been completed."),
			Link:         fmt.Sprintf("/marketing/check-product-fulfillment"),
		})
	}
}
