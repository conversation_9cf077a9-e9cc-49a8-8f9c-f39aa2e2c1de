package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/buymed.th/marketplace/promotion/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DataRaw ...
type DataRaw struct {
	Type string `json:"-" bson:"type,omitempty"`
	Val  string `json:"-" bson:"val,omitempty"`
} // @name DataRaw

type CampaignProduct struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	CreatedBy       int64              `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	UpdatedBy       int64              `json:"updatedBy,omitempty" bson:"updated_by,omitempty"`

	CampaignID            int64                          `json:"campaignID,omitempty" bson:"campaign_id,omitempty"`
	CampaignCode          string                         `json:"campaignCode,omitempty" bson:"campaign_code,omitempty"`
	CampaignType          enum.CampaignValueType         `json:"campaignType,omitempty" bson:"campaign_type,omitempty"`
	CampaignProductID     int64                          `json:"campaignProductID,omitempty" bson:"campaign_product_id,omitempty"`
	CampaignProductIDs    []int64                        `json:"campaignProductIDs,omitempty" bson:"-"`
	CampaignProductCode   string                         `json:"campaignProductCode,omitempty" bson:"campaign_product_code,omitempty"`
	ProductID             int64                          `json:"productID,omitempty" bson:"product_id,omitempty"`
	ProductCode           string                         `json:"productCode,omitempty" bson:"product_code,omitempty"`
	Sku                   string                         `json:"sku,omitempty" bson:"sku,omitempty"`
	SellerCode            string                         `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	Price                 float64                        `json:"price,omitempty" bson:"price,omitempty"`
	SalePrice             float64                        `json:"salePrice,omitempty" bson:"sale_price,omitempty"`
	CampaignPrice         *float64                       `json:"campaignPrice,omitempty" bson:"campaign_price,omitempty"`
	PercentageDiscount    *float64                       `json:"percentageDiscount,omitempty" bson:"percentage_discount,omitempty"`
	AbsoluteDiscount      *float64                       `json:"absoluteDiscount,omitempty" bson:"absolute_discount,omitempty"`
	MaxDiscount           *float64                       `json:"maxDiscount,omitempty" bson:"max_discount,omitempty"`
	Quantity              *int64                         `json:"quantity,omitempty" bson:"quantity,omitempty"`
	SoldQuantity          *int64                         `json:"soldQuantity,omitempty" bson:"sold_quantity,omitempty"`
	MaxQuantityPerOrder   *int64                         `json:"maxQuantityPerOrder,omitempty" bson:"max_quantity_per_order,omitempty"`
	IsActive              *bool                          `json:"isActive,omitempty" bson:"is_active,omitempty"`
	SaleType              enum.CampaignSaleValueType     `json:"saleType,omitempty" bson:"sale_type,omitempty"`
	StartTime             time.Time                      `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime               time.Time                      `json:"endTime,omitempty" bson:"end_time,omitempty"`
	FlashSaleTime         []string                       `json:"flashSaleTimeRefs,omitempty" bson:"flash_sale_time_refs,omitempty"` // ref code to campaign
	Status                enum.CampaignProductStatusType `json:"status,omitempty" bson:"status,omitempty"`
	CancelReason          string                         `json:"cancelReason,omitempty" bson:"cancel_reason,omitempty"`
	ChargeFee             string                         `json:"chargeFee,omitempty" bson:"charge_fee,omitempty"` // MARKETING . SELLER
	Version               string                         `json:"version,omitempty" bson:"version,omitempty"`
	PrivateNote           string                         `json:"privateNote,omitempty" bson:"private_note,omitempty"`
	LocationCodeMap       map[string]int                 `json:"-" bson:"location_code_map,omitempty"`
	StatusPriority        int                            `json:"statusPriority,omitempty" bson:"status_priority,omitempty"` // cache only
	Note                  string                         `json:"-" bson:"note,omitempty"`
	FlashSaleTimes        []*CampaignFlashSaleTimeItem   `json:"-" bson:"flash_sale_time,omitempty"` // to check flash sale time for cart
	HashTag               string                         `json:"-" bson:"hash_tag,omitempty"`
	UniqueSkuActive       *string                        `json:"-" bson:"unique_sku_active,omitempty"`
	ComplexQuery          []*bson.M                      `json:"-" bson:"$and,omitempty"`
	Campaign              *Campaign                      `json:"-" bson:"campaign,omitempty"`
	FilterRaw             []*DataRaw                     `json:"-" bson:"filter_raw,omitempty"`
	Priority              *int64                         `json:"priority,omitempty" bson:"priority,omitempty"`
	SellerCategoryCode    string                         `json:"sellerCategoryCode,omitempty" bson:"seller_category_code,omitempty"`
	SellerSubCategoryCode *string                        `json:"sellerSubCategoryCode,omitempty" bson:"seller_sub_category_code,omitempty"`
	PriceRange            string                         `json:"priceRange,omitempty" bson:"price_range,omitempty"`
}

var CampaignProductDB = &db.Instance{
	ColName:        "campaign_product",
	TemplateObject: &CampaignProduct{},
}

var CampaignProductReaderDB = &db.Instance{
	ColName:        "campaign_product",
	TemplateObject: &CampaignProduct{},
}

var CampaignProductCacheDB = &db.Instance{
	ColName:        "campaign_product_v2",
	TemplateObject: &CampaignProduct{},
}

func InitCampaignProductReaderModel(s *mongo.Database) {
	CampaignProductReaderDB.ApplyDatabase(s)
}

// InitCampaignProductModel is func init model
func InitCampaignProductModel(dbInst *db.Instance, s *mongo.Database, skipIndex bool) {
	dbInst.ApplyDatabase(s)

	if skipIndex {
		return
	}

	t := true
	dbInst.CreateIndex(bson.D{
		primitive.E{Key: "status", Value: 1},
		primitive.E{Key: "campaign_id", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	// t := true
	// dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "campaign_product_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	//dbInst.CreateIndex(bson.D{
	//	primitive.E{Key: "unique_sku_active", Value: 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//	Unique:     &t,
	//	Sparse:     &t,
	//})

	// dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "campaign_product_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "campaign_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "campaign_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	dbInst.CreateIndex(bson.D{
		primitive.E{Key: "sku", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	dbInst.CreateIndex(bson.D{
		primitive.E{Key: "product_id", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	dbInst.CreateIndex(bson.D{
		primitive.E{Key: "seller_code", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	// err := dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "filter_raw.val", Value: 1},
	// 	primitive.E{Key: "filter_raw.type", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// if err != nil {
	// 	fmt.Println(err)
	// }

	// err = dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "flash_sale_time.ref", Value: 1},
	// 	primitive.E{Key: "status", Value: 1},
	// 	primitive.E{Key: "is_active", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// if err != nil {
	// 	fmt.Println(err)
	// }

	// err = dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "campaign_code", Value: 1},
	// 	primitive.E{Key: "status", Value: 1},
	// 	primitive.E{Key: "is_active", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// if err != nil {
	// 	fmt.Println(err)
	// }

	// dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "is_active", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "status", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
}

// InitCampaignProductCacheModel is func init model
func InitCampaignProductCacheModel(dbInst *db.Instance, s *mongo.Database, skipIndex bool) {
	dbInst.ApplyDatabase(s)

	if skipIndex {
		return
	}

	t := true
	dbInst.CreateIndex(bson.D{
		primitive.E{Key: "campaign_code", Value: 1},
		primitive.E{Key: "status", Value: 1},
		primitive.E{Key: "is_active", Value: 1},
		primitive.E{Key: "campaign.is_active", Value: 1},
		primitive.E{Key: "campaign.status", Value: 1},
		primitive.E{Key: "priority", Value: -1},
		primitive.E{Key: "status_priority", Value: -1},
		primitive.E{Key: "filter_raw.val", Value: 1},
		primitive.E{Key: "filter_raw.type", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})
	dbInst.CreateIndex(bson.D{
		primitive.E{Key: "filter_raw.val", Value: 1},
		primitive.E{Key: "filter_raw.type", Value: 1},
		primitive.E{Key: "campaign_code", Value: 1},
		primitive.E{Key: "status", Value: 1},
		primitive.E{Key: "is_active", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	// dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "campaign_product_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "unique_sku_active", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// 	Sparse:     &t,
	// })

	// dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "campaign_product_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "campaign_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "campaign_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "sku", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "product_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "seller_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// err := dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "filter_raw.val", Value: 1},
	// 	primitive.E{Key: "filter_raw.type", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// if err != nil {
	// 	fmt.Println(err)
	// }

	// err = dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "flash_sale_time.ref", Value: 1},
	// 	primitive.E{Key: "status", Value: 1},
	// 	primitive.E{Key: "is_active", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// if err != nil {
	// 	fmt.Println(err)
	// }

	// err = dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "campaign_code", Value: 1},
	// 	primitive.E{Key: "status", Value: 1},
	// 	primitive.E{Key: "is_active", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// if err != nil {
	// 	fmt.Println(err)
	// }

	// dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "is_active", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "status", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
}
