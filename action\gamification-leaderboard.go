package action

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/buymed.th/marketplace/promotion/model"
	"gitlab.com/buymed.th/marketplace/promotion/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

var GetGamificationLeaderboardList = func(
	query *model.GamificationLeaderBoard,
	offset int64,
	limit int64,
	getTotal bool,
) *common.APIResponse {

	if query.LeaderboardStatus != "" {
		totalResp := model.GamificationDetailDB.Count(&model.GamificationDetail{
			LuckyWheelCode: query.LuckyWheelCode,
		})

		if query.LeaderboardStatus == "IN_PROGRESS" {
			// query or number_of_mission_completed < total or number_of_mission_completed = 0
			query.ComplexQuery = append(query.ComplexQuery, &bson.M{
				"$or": []bson.M{
					{"number_of_mission_completed": bson.M{
						"$lt": totalResp.Total,
					}},
					{"number_of_mission_completed": bson.M{
						"$exists": false,
					}},
				},
			})
		} else if query.LeaderboardStatus == "COMPLETED" {
			query.ComplexQuery = append(query.ComplexQuery, &bson.M{
				"number_of_mission_completed": bson.M{
					"$gte": totalResp.Total,
				},
			})
		}
	}

	var result *common.APIResponse
	result = model.GamificationLeaderBoardDB.Query(query, offset, limit, &primitive.M{"point": -1})
	if getTotal {
		countResult := model.GamificationLeaderBoardDB.Count(query)
		result.Total = countResult.Total
	}

	return result
}

var GetGamificationResultListWithFilter = func(
	query *model.GamificationLeaderBoard,
	gamiResultsQuery *model.GamificationResult,
	offset int64,
	limit int64,
	getTotal bool,
) *common.APIResponse {

	gamiResultsQuery.Status = query.MissionStatus

	if len(query.GamificationDetailIDs) > 0 {
		// if specific mission ids, get completed customers, else get incompleted customers
		gamiResultsQuery.Status = enum.GamificationResultStatus.COMPLETED

		gamiResultsQuery.ComplexQuery = append(gamiResultsQuery.ComplexQuery, &bson.M{
			"gamification_detail_id": bson.M{
				"$in": query.GamificationDetailIDs,
			},
		})
	}

	resp := model.GamificationResultDB.Query(gamiResultsQuery, 0, 0, nil)

	if resp.Status != common.APIStatus.Ok && resp.Status != common.APIStatus.NotFound {
		return resp
	}

	gamiResults := make([]*model.GamificationResult, 0)
	if resp.Status == common.APIStatus.Ok {
		gamiResults = resp.Data.([]*model.GamificationResult)
	}

	customerIds := make([]int64, 0)
	for _, gamiResult := range gamiResults {
		customerIds = append(customerIds, gamiResult.CustomerID)
	}

	if len(query.GamificationDetailIDs) > 0 {
		if query.MissionStatus == enum.GamificationResultStatus.IN_PROGRESS {
			// get incompleted customers
			query.ComplexQuery = []*bson.M{{
				"customer_id": bson.M{
					"$nin": customerIds,
				},
			}}
		} else {
			// get completed customers
			query.ComplexQuery = []*bson.M{{
				"customer_id": bson.M{
					"$in": customerIds,
				},
			}}
		}
	} else {
		query.ComplexQuery = []*bson.M{{
			"customer_id": bson.M{
				"$in": customerIds,
			},
		}}
	}

	resp = model.GamificationLeaderBoardDB.Query(
		query, offset, limit, &primitive.M{"point": -1})

	if resp.Status != common.APIStatus.Ok {
		return resp
	}

	if getTotal {
		countResult := model.GamificationLeaderBoardDB.Count(query)
		resp.Total = countResult.Total
	}

	return resp
}

func GetListGamificationResultLog(query *model.GamificationResultLog, offset, limit int64, getTotal bool) *common.APIResponse {
	results := model.GamificationResultLogDB.Query(query, offset, limit, &primitive.M{"created_time": -1})

	if getTotal {
		countResult := model.GamificationResultLogDB.Count(query)
		results.Total = countResult.Total
	}

	for _, res := range results.Data.([]*model.GamificationResultLog) {
		bsonData, _ := bson.Marshal(res.RequestData)
		var bsonMap map[string]interface{}
		err := bson.Unmarshal(bsonData, &bsonMap)
		if err != nil {
			continue
		}

		// Marshal the map to JSON
		jsonData, err := json.Marshal(bsonMap)
		var jsonMap map[string]interface{}
		err = json.Unmarshal(jsonData, &jsonMap)
		if err != nil {
			continue
		}
		res.RequestData = jsonMap
	}
	return results
}

func DeleteKeyGamificationResultLog(key string) *common.APIResponse {
	if key == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Key is required.",
		}
	}
	return model.GamificationResultLogDB.Delete(model.GamificationResultLog{Key: key})
}
