package client

import (
	"fmt"
	"os"
	"runtime"

	"gitlab.com/buymed.th/marketplace/promotion/client/customer"
	"gitlab.com/buymed.th/marketplace/promotion/client/order"
	"gitlab.com/buymed.th/marketplace/promotion/conf"
	"go.mongodb.org/mongo-driver/mongo"
)

// Promo ..
type gatewayClient struct {
	Customer     *customer.Client
	Order        *order.Client
	Product      *productClient
	Notification *notificationClient
}

// Services ...
// ErrUnvaliableService ...
var (
	Services             *gatewayClient
	ErrUnvaliableService = fmt.Errorf("%s", "Service customer unavailable")
)

func init() {
	Services = &gatewayClient{
		Customer: customer.NewServiceClient(conf.Config.APIHost, conf.Config.APIKey, conf.Config.LogDBName),
		// Product:  NewProductClient(conf.Config.APIHost, conf.Config.APIKey, conf.Config.LogDBName),
	}
}

func InitClientWithLog(s *mongo.Database) {
	Services.Order = order.NewServiceClient(conf.Config.APIHost, conf.Config.APIKey, s)
	Services.Product = NewProductClient(conf.Config.APIHost, conf.Config.APIKey, conf.Config.LogDBName, s)
	Services.Notification = NewNotificationServiceClient(conf.Config.APIHost, conf.Config.APIKey, s)
	NewLocationServiceClient(s)
}

// HealthCheck is func health check all services
func (cli *gatewayClient) HealthCheck() error {
	hostname, _ := os.Hostname()
	fmt.Printf("hostname = %s, runtime go routine = %d\n", hostname, runtime.NumGoroutine())
	return nil
}
