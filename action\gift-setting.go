package action

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/buymed.th/marketplace/promotion/model"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// CreateGiftSetting is func to create gift setting
func CreateGiftSetting(username string, in *model.GiftSetting) *common.APIResponse {
	qResult := model.GiftSettingDB.QueryOne(bson.M{})
	var giftSetting *model.GiftSetting
	t := time.Now()
	if qResult.Status != common.APIStatus.Ok {
		giftSetting = &model.GiftSetting{
			CreatedTime:     &t,
			LastUpdatedTime: &t,
			CreatedBy:       username,
		}
	} else {
		giftSetting = &model.GiftSetting{
			LastUpdatedTime: &t,
			CreatedBy:       username,
		}
	}

	if in.NewbieGift != nil {
		giftSetting.NewbieGift = in.NewbieGift
	}
	if in.FriendGift != nil {
		giftSetting.FriendGift = in.FriendGift
	}

	isTrue := true
	after := options.After
	opt := options.FindOneAndUpdateOptions{
		Upsert:         &isTrue,
		ReturnDocument: &after,
	}

	return model.GiftSettingDB.UpdateOne(bson.M{}, giftSetting, &opt)
}

// GetGiftSetting is func to get gift setting
func GetGiftSetting() *common.APIResponse {
	return model.GiftSettingDB.QueryOne(bson.M{})
}
