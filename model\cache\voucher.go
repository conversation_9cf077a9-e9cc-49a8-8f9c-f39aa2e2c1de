package cache

import (
	"gitlab.com/buymed.th/marketplace/promotion/model/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

type Voucher struct {
	ID                  primitive.ObjectID       `json:"-" bson:"_id,omitempty" `
	CreatedTime         *time.Time               `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	CreatedBy           int64                    `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	CreatedByUserName   *string                  `json:"createdByUserName,omitempty" bson:"created_by_user_name,omitempty"`
	LastUpdatedTime     *time.Time               `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	UpdatedBy           int64                    `json:"updatedBy,omitempty" bson:"updated_by,omitempty"`
	VersionNo           string                   `json:"versionNo,omitempty" bson:"version_no,omitempty"`
	VoucherID           int64                    `json:"voucherId,omitempty" bson:"voucher_id,omitempty"`
	Code                string                   `json:"code,omitempty" bson:"code,omitempty"`
	PromotionID         int64                    `json:"promotionId,omitempty" bson:"promotion_id,omitempty"`
	PromotionName       string                   `json:"promotionName,omitempty" bson:"promotion_name,omitempty"`
	StartTime           *time.Time               `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime             *time.Time               `json:"endTime,omitempty" bson:"end_time,omitempty"`
	PublicTime          *time.Time               `json:"publicTime,omitempty" bson:"public_time,omitempty"`
	MaxUsage            int64                    `json:"maxUsage" bson:"max_usage,omitempty"`
	MaxUsagePerCustomer int64                    `json:"maxUsagePerCustomer" bson:"max_usage_per_customer,omitempty"`
	UsageTotal          *int64                   `json:"usageTotal" bson:"usage_total,omitempty"`
	VoucherType         *enum.VoucherTypeValue   `json:"type,omitempty" bson:"type,omitempty"`
	UsageType           *enum.UsageTypeValue     `json:"usageType,omitempty" bson:"usage_type,omitempty"`
	AppliedCustomers    *[]int64                 `json:"appliedCustomers" bson:"applied_customers,omitempty"`
	AppliedCustomerMap  map[int64]bool           `json:"-" bson:"applied_customer_map,omitempty"`
	Status              *enum.VoucherStatusValue `json:"status,omitempty" bson:"status,omitempty"`
	HashTag             string                   `json:"-" bson:"hash_tag,omitempty"`
	NeedCheck           *bool                    `json:"_" bson:"need_check,omitempty"`
}
