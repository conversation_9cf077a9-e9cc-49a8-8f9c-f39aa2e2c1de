package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type ShareLog struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	Code       string      `json:"code,omitempty" bson:"code,omitempty"`
	CustomerID int         `json:"customerID,omitempty" bson:"customer_id,omitempty"`
	AccountID  int         `json:"accountID,omitempty" bson:"account_id,omitempty"`
	ShareURL   string      `json:"shareUrl,omitempty" bson:"share_url,omitempty"`
	Source     string      `json:"source,omitempty" bson:"source,omitempty"`
	Page       string      `json:"page,omitempty" bson:"page,omitempty"`
	Screen     string      `json:"screen,omitempty" bson:"screen,omitempty"`
	Platform   string      `json:"platform,omitempty" bson:"platform,omitempty"`
	UaMetadata *UaMetadata `json:"uaMetadata,omitempty" bson:"ua_metadata,omitempty"`
	Data       interface{} `json:"data,omitempty" bson:"data,omitempty"`
}

type UaMetadata struct {
	Platform      string `json:"platform,omitempty" bson:"platform,omitempty"`
	OSName        string `json:"oSName,omitempty" bson:"os_name,omitempty"`
	OSVersion     string `json:"oSVersion,omitempty" bson:"os_version,omitempty"`
	ClientName    string `json:"clientName,omitempty" bson:"client_name,omitempty"`
	ClientVersion string `json:"clientVersion,omitempty" bson:"client_version,omitempty"`
}

// ShareLogDB ...
var ShareLogDB = &db.Instance{
	ColName:        "share_log",
	TemplateObject: &ShareLog{},
}

// InitShareLogModel is func init model share log
func InitShareLogModel(s *mongo.Database) {
	ShareLogDB.ApplyDatabase(s)
}
