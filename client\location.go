package client

import (
	"encoding/json"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/buymed.th/marketplace/promotion/conf"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	pathGetRegion    = "/core/master-data/v1/region/list"
	pathProvinceList = "/core/master-data/v1/province/list"
)

var LocationClient = &locationClient{}

type locationClient struct {
	svc     *client.RestClient
	headers map[string]string
}

// NewLocationServiceClient ...
func NewLocationServiceClient(session *mongo.Database) {
	LocationClient = &locationClient{
		svc: client.NewRESTClient(conf.Config.APIHost, "location", 3*time.Second, 1, 3*time.Second),
		headers: map[string]string{
			"Authorization": conf.Config.APIKey,
		},
	}
}

type Province struct {
	CreatedTime     *time.Time `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	Code           string   `json:"code" bson:"code,omitempty"`
	Name           string   `json:"name" bson:"name,omitempty"`
	Level          string   `json:"level" bson:"level,omitempty"`
	IsSupport      bool     `json:"isSupport" bson:"is_support,omitempty"`
	FeeValue       *float64 `json:"feeValue,omitempty" bson:"fee_value,omitempty"`
	FeeRegionValue *float64 `json:"feeRegionValue,omitempty" bson:"fee_region_value,omitempty"`
	RegionCode     *string  `json:"regionCode,omitempty" bson:"region_code,omitempty"`
	RegionName     *string  `json:"regionName,omitempty" bson:"region_name,omitempty"`
	ErpCode        *string  `json:"erpCode,omitempty" bson:"erp_code,omitempty"`
}

type ProvinceRes struct {
	Status    string      `json:"status"`
	Data      []*Province `json:"data,omitempty"`
	Message   string      `json:"message"`
	ErrorCode string      `json:"errorCode,omitempty"`
	Total     int64       `json:"total,omitempty"`
}

func (cli *locationClient) GetProvinceList() *ProvinceRes {
	params := make(map[string]string)
	params["offset"] = "0"
	params["limit"] = "1000"

	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathProvinceList, nil)

	if err != nil {
		return &ProvinceRes{
			Status:  common.APIStatus.Error,
			Message: err.Error(),
		}
	}

	var result *ProvinceRes
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &ProvinceRes{
			Status:  common.APIStatus.Error,
			Message: err.Error(),
		}
	}

	return result
}

// GetListSku ...
func (cli *locationClient) GetRegionList(codes []string) *RegionResponse {
	var params = map[string]string{
		"provinceCodes": strings.Join(codes, ","),
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetRegion, nil)
	if err != nil {
		return &RegionResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_GetRegion",
		}
	}

	var result *RegionResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &RegionResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_GetRegion",
		}
	}

	return result
}

func (cli *locationClient) GetRegionListByRegionCodes() *RegionResponse {
	var params = map[string]string{}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetRegion, nil)
	if err != nil {
		return &RegionResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_GetRegion",
		}
	}

	var result *RegionResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &RegionResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_GetRegion",
		}
	}

	return result
}

type Region struct {
	RegionID      int64    `json:"regionID,omitempty" bson:"region_id,omitempty"`
	Code          string   `json:"code,omitempty" bson:"code,omitempty"`
	Name          string   `json:"name" bson:"name,omitempty"`
	Scope         string   `json:"scope"`
	ProvinceCodes []string `json:"provinceCodes"`
}

// RegionResponse
type RegionResponse struct {
	Status    string    `json:"status"`
	Message   string    `json:"message"`
	ErrorCode string    `json:"errorCode,omitempty"`
	Data      []*Region `json:"data"`
}
