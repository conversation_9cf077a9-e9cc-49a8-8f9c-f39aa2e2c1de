package action

import (
	"fmt"
	"math/rand"
	"sort"
	"strconv"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/mongo/options"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/buymed.th/marketplace/promotion/client"
	"gitlab.com/buymed.th/marketplace/promotion/model"
	"gitlab.com/buymed.th/marketplace/promotion/model/enum"
	"gitlab.com/buymed.th/marketplace/promotion/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func GetLuckyWheelById(GamificationId int64) *common.APIResponse {
	if GamificationId == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Parameter gamificationId not allow empty",
			ErrorCode: "ID_REQUIRED",
		}
	}
	return model.LuckyWheelDB.QueryOne(bson.M{"gamification_id": GamificationId})
}

func GetLuckyWheelList(query *model.LuckyWheel, offset int64, limit int64, getTotal bool) *common.APIResponse {
	result := model.LuckyWheelDB.Query(query, offset, limit, &primitive.M{"_id": -1})

	if result.Status != common.APIStatus.Ok {
		return result
	}

	if getTotal {
		countResult := model.LuckyWheelDB.Count(query)
		result.Total = countResult.Total
	}

	return result
}

func LuckyWheelCreate(acc *model.Account, luckyWheel *model.LuckyWheel) *common.APIResponse {
	luckyWheel.CreatedBy = acc.AccountID
	luckyWheel.Code = model.GenCodeWithTime()
	luckyWheel.VersionUpdate = model.GenCodeWithTime()
	normalCode := strings.Replace(utils.NormalizeString(luckyWheel.Code), " ", "-", -1)
	normalName := strings.Replace(utils.NormalizeString(luckyWheel.Name), " ", "-", -1)
	luckyWheel.HashTag = fmt.Sprintf("%s-%s", normalCode, normalName)
	return model.LuckyWheelDB.Create(luckyWheel)
}

func UpdateLuckyWheel(acc *model.Account, luckyWheel *model.LuckyWheel) *common.APIResponse {
	query := model.LuckyWheel{
		Code: luckyWheel.Code,
	}
	qLuckyWheel := model.LuckyWheelDB.QueryOne(query)

	if qLuckyWheel.Status != common.APIStatus.Ok {
		return qLuckyWheel
	}
	query.VersionUpdate = luckyWheel.VersionUpdate
	luckyWheel.VersionUpdate = model.GenCodeWithTime()
	name := qLuckyWheel.Data.([]*model.LuckyWheel)[0].Name
	if luckyWheel.Name != "" {
		name = luckyWheel.Name
	}
	normalCode := strings.Replace(utils.NormalizeString(luckyWheel.Code), " ", "-", -1)
	normalName := strings.Replace(utils.NormalizeString(name), " ", "-", -1)
	luckyWheel.HashTag = fmt.Sprintf("%s-%s", normalCode, normalName)
	return model.LuckyWheelDB.UpdateOne(query, luckyWheel)
}

func LuckyWheelItemCreate(acc *model.Account, luckyWheel *model.LuckyWheelItem) *common.APIResponse {
	luckyWheel.CreatedBy = acc.AccountID
	return model.LuckyWheelItemDB.Create(luckyWheel)
}

func GetLuckyWheelItemList(query *model.LuckyWheelItem) *common.APIResponse {
	return model.LuckyWheelItemDB.Query(query, 0, 0, &primitive.M{"_id": -1})
}

func UpdateLuckyWheelItem(acc *model.Account, luckyWheelItem *model.LuckyWheelItem) *common.APIResponse {
	if luckyWheelItem.ItemCode == "" || luckyWheelItem.LuckyWheelCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Parameter luckyWheelCode not allow empty",
			ErrorCode: "CODE_REQUIRED",
		}
	}
	qItem := model.LuckyWheelItemDB.QueryOne(model.LuckyWheelItem{
		ItemCode:       luckyWheelItem.ItemCode,
		LuckyWheelCode: luckyWheelItem.LuckyWheelCode,
	})
	if qItem.Status != common.APIStatus.Ok {
		return qItem
	}
	isUpdateTime := false
	updateTime := bson.M{}
	if luckyWheelItem.StartTime == nil {
		isUpdateTime = true
		updateTime["start_time"] = nil
	}
	if luckyWheelItem.EndTime == nil {
		isUpdateTime = true
		updateTime["end_time"] = nil
	}
	if isUpdateTime {
		model.LuckyWheelItemDB.UpdateOneWithOption(model.LuckyWheelItem{
			LuckyWheelCode: luckyWheelItem.LuckyWheelCode,
			ItemCode:       luckyWheelItem.ItemCode,
		}, bson.M{"$set": updateTime})
	}
	return model.LuckyWheelItemDB.UpdateOne(model.LuckyWheelItem{
		LuckyWheelCode: luckyWheelItem.LuckyWheelCode,
		ItemCode:       luckyWheelItem.ItemCode,
	}, luckyWheelItem)
}

func PostSpinLuckyWheel(acc *model.Account, code, turnType, systemDisplay, phone, provinceCode string, questionCode string, answerID int64) *common.APIResponse {
	var customer *model.Customer
	var err error

	customer, err = client.Services.Customer.GetCustomerByAccountID(acc.AccountID)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "CUSTOMER_NOT_FOUND",
			Message:   "Customer is not found",
		}
	}
	queryLK := bson.M{
		"code":                 code,
		"is_active":            true,
		"start_time":           bson.M{"$lte": time.Now()},
		"end_time":             bson.M{"$gte": time.Now()},
		"system_display":       systemDisplay,
		"scope.province_codes": bson.M{"$in": []string{customer.ProvinceCode, "all"}},
		//"scope.levels": []string{customer.Level, "all"},
		//"scope.scopes": []string{customer.Scope, "all"},
	}
	qLK := model.LuckyWheelDB.QueryOne(queryLK)
	if qLK.Status != common.APIStatus.Ok {
		return qLK
	}
	luckyWheel := qLK.Data.([]*model.LuckyWheel)[0]

	// validate game question
	var question *model.GameQuestion
	var userAnswer *model.GameAnswer
	var correctAnswer *model.GameAnswer
	if luckyWheel.IsQuestionRequired != nil && *luckyWheel.IsQuestionRequired {
		if questionCode == "" || answerID == 0 {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				ErrorCode: "QUESTION_ANSWER_REQUIRED",
				Message:   "Please answer the question",
			}
		}

		questionResult := model.GameQuestionDB.QueryOne(bson.M{"code": questionCode})
		if questionResult.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				ErrorCode: "QUESTION_NOT_FOUND",
				Message:   "Question not found",
			}
		}
		question = questionResult.Data.([]*model.GameQuestion)[0]

		for _, answer := range question.Answers {
			if answer.AnswerID == answerID {
				userAnswer = &model.GameAnswer{
					AnswerID: answer.AnswerID,
					Content:  answer.Content,
				}
			}

			if answer.IsCorrect != nil && *answer.IsCorrect {
				correctAnswer = &model.GameAnswer{
					AnswerID: answer.AnswerID,
					Content:  answer.Content,
				}
			}
		}

		if userAnswer == nil {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				ErrorCode: "INVALID_ANSWER",
				Message:   fmt.Sprintf("Answer %d is not found", answerID),
			}
		}
		if correctAnswer == nil {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				ErrorCode: "INVALID_CORRECT_ANSWER",
				Message:   "This question does not have the correct answer",
			}
		}
	}

	isMatchLevel := false
	isMatchScope := false
	isMatchCustomer := luckyWheel.Scope == nil || luckyWheel.Scope.CustomerApplyType == "" || luckyWheel.Scope.CustomerApplyType == "ALL"
	if !isMatchCustomer {
		qCustomerJoin := model.GamificationCustomerDB.QueryOne(bson.M{
			"lucky_wheel_code": code,
			"customer_id":      customer.CustomerID,
			"status":           "ACTIVE",
		})
		isMatchCustomer = qCustomerJoin.Status == common.APIStatus.Ok
	}
	for _, level := range luckyWheel.Scope.CustomerLevels {
		if level == "all" || level == customer.Level {
			isMatchLevel = true
		}
	}
	for _, scope := range luckyWheel.Scope.CustomerScopes {
		if scope == "all" || scope == customer.Scope {
			isMatchScope = true
		}
	}
	if !(isMatchLevel && isMatchScope && isMatchCustomer) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Customer not match scope",
			ErrorCode: "NOT_FOUND",
		}
	}
	query := model.LuckyWheelItem{
		IsActive:       utils.ParseBoolToPointer(true),
		LuckyWheelCode: code,
	}
	qItem := model.LuckyWheelItemDB.Query(query, 0, 0, &primitive.M{"_id": -1})
	if qItem.Status != common.APIStatus.Ok {
		return qItem
	}
	items := qItem.Data.([]*model.LuckyWheelItem)
	mapItem := make(map[string]*model.LuckyWheelItem)
	itemCodes := make([]string, 0)
	now := time.Now()
	nowVN := now.Add(7 * time.Hour)
	for _, item := range items {
		mapItem[item.ItemCode] = item
		itemCodes = append(itemCodes, item.ItemCode)
	}
	var qDecrQuantity *common.APIResponse
	if turnType == "FREE" {
		returnOpt := options.After
		if luckyWheel.IsAcceptFreeTurn != nil && *luckyWheel.IsAcceptFreeTurn == true {
			timeToFree := now.Add(-time.Duration(luckyWheel.TimeToFreeTurn) * time.Minute)
			queryToDecre := bson.M{"$or": []*bson.M{
				{
					"last_free_time": bson.M{"$lte": timeToFree},
				},
				{
					"last_free_time": nil,
				},
				{
					"used_free_quantity": 0,
				},
				{
					"used_free_quantity": nil,
				},
			}, "lucky_wheel_code": code}
			if systemDisplay == "CIRCA" {
				queryToDecre["customer_phone"] = phone
			} else {
				queryToDecre["account_id"] = acc.AccountID
			}
			qDecrQuantity = model.CustomerLuckyWheelDB.UpdateOneWithOption(queryToDecre, bson.M{
				"$inc": bson.M{"used_free_quantity": 1},
				"$set": bson.M{"last_free_time": now},
			}, &options.FindOneAndUpdateOptions{
				Upsert:         utils.ParseBoolToPointer(true),
				ReturnDocument: &returnOpt,
			})
			//if qDecrQuantity.Status != common.APIStatus.Ok {
			//	qDecrQuantity = model.CustomerLuckyWheelDB.QueryOne(bson.M{"account_id": acc.AccountID, "lucky_wheel_code": code})
			//}
		}
	} else {
		queryDecrQuantity := bson.M{"quantity": bson.M{"$gt": 0}, "lucky_wheel_code": code}
		if systemDisplay == "CIRCA" {
			queryDecrQuantity["customer_phone"] = phone
		} else {
			queryDecrQuantity["account_id"] = acc.AccountID
		}
		qDecrQuantity = model.CustomerLuckyWheelDB.UpdateOneWithOption(queryDecrQuantity, bson.M{
			"$inc": bson.M{"quantity": -1},
		})
	}
	if qDecrQuantity.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "NOT_ENOUGH_SPIN",
			Message:   "You don't have any spin",
		}
	}
	customerLuckyWheel := qDecrQuantity.Data.([]*model.CustomerLuckyWheel)[0]
	mapQuantity := make(map[string]int64)
	if customerLuckyWheel.UsedItemQuantity != nil {
		mapQuantity = *customerLuckyWheel.UsedItemQuantity
	}
	var itemIncreasePercent *model.LuckyWheelItem
	type slot struct {
		from int64
		to   int64
	}
	slots := make(map[string]slot, 0) // 100 * 1000 = 100000
	lastTo := int64(0)
	freeSlot := int64(0)

	mapItemSkip := make(map[string]bool)
	if qSetting := model.SettingDB.QueryOne(model.Setting{}); qSetting.Status == common.APIStatus.Ok {
		setting := qSetting.Data.([]*model.Setting)[0]
		if setting.SkipLuckyWheelItemSpecial != nil {
			mapItemSkip = *setting.SkipLuckyWheelItemSpecial
		}
	}
	for _, item := range items {
		if item.IsIncreasePercentage != nil && *item.IsIncreasePercentage == true {
			itemIncreasePercent = item
		}
		if item.MaxQuantityPerCustomer != nil && *item.MaxQuantityPerCustomer != 0 && mapQuantity[item.ItemCode] >= *item.MaxQuantityPerCustomer {
			freeSlot += int64(*item.Percentage * 1000)
			continue
		}
		if item.MaxQuantity != nil && *item.MaxQuantity != 0 && item.UsedQuantity >= *item.MaxQuantity {
			freeSlot += int64(*item.Percentage * 1000)
			continue
		}
		keyItemPerDay := fmt.Sprintf("%d_%d_%s", nowVN.Day(), nowVN.Month(), item.ItemCode)
		if item.MaxQuantityPerDay != nil && *item.MaxQuantityPerDay != 0 {
			if item.UsedItemQuantity != nil {
				mapItemQuantity := *item.UsedItemQuantity
				if mapItemQuantity[keyItemPerDay] >= *item.MaxQuantityPerDay {
					freeSlot += int64(*item.Percentage * 1000)
					continue
				}
			}
		}
		if item.Percentage == nil || *item.Percentage == 0 {
			continue
		}
		if item.StartTime != nil && now.Before(*item.StartTime) {
			freeSlot += int64(*item.Percentage * 1000)
			continue
		}
		if item.EndTime != nil && now.After(*item.EndTime) {
			freeSlot += int64(*item.Percentage * 1000)
			continue
		}

		if mapItemSkip != nil && len(mapItemSkip) > 0 {
			if isSkipSpecialItem(customer, item, mapItemSkip) {
				freeSlot += int64(*item.Percentage * 1000)
				continue
			}
		}

		slots[item.ItemCode] = slot{
			from: lastTo + 1,
			to:   lastTo + int64(*item.Percentage*1000),
		}
		lastTo += int64(*item.Percentage * 1000)
		mapItem[item.ItemCode] = item
	}
	if freeSlot != 0 && itemIncreasePercent != nil {
		slots["FREE_SLOT"] = slot{
			from: lastTo,
			to:   lastTo + freeSlot,
		}
		lastTo += freeSlot
	}
	rand.Seed(time.Now().UnixNano()) // Seed the random number generator with the current time
	queryUpdateCustomerLuckyWheel := bson.M{"lucky_wheel_code": code}
	if systemDisplay == "CIRCA" {
		queryUpdateCustomerLuckyWheel["customer_phone"] = phone
	} else {
		queryUpdateCustomerLuckyWheel["account_id"] = acc.AccountID
	}
	if lastTo == 0 {
		if turnType == "FREE" {
			timeToFree := now.Add(-time.Duration(luckyWheel.TimeToFreeTurn) * time.Minute)

			model.CustomerLuckyWheelDB.UpdateOneWithOption(queryUpdateCustomerLuckyWheel, bson.M{
				"$inc": bson.M{"used_free_quantity": -1},
				"$set": bson.M{"last_free_time": timeToFree},
			})
		} else {
			model.CustomerLuckyWheelDB.UpdateOneWithOption(queryUpdateCustomerLuckyWheel, bson.M{
				"$inc": bson.M{"quantity": 1},
			})
		}
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "NOT_ENOUGH_ITEM",
			Message:   "Please try again",
		}
	}
	randomNumber := rand.Intn(int(lastTo))
	var itemResult *model.LuckyWheelItem

	isQuestionPassed := true
	if luckyWheel.IsQuestionRequired != nil && *luckyWheel.IsQuestionRequired {
		isQuestionPassed = userAnswer.AnswerID == correctAnswer.AnswerID
	}

	if isQuestionPassed {
		for itemCode, sl := range slots {
			if randomNumber >= int(sl.from) && randomNumber <= int(sl.to) {
				if itemCode == "FREE_SLOT" {
					itemCode = itemIncreasePercent.ItemCode
				}
				itemResult = mapItem[itemCode]
				itemResult.UsedQuantity += 1
			}
		}
	} else {
		itemResult = &model.LuckyWheelItem{
			ItemCode: "WRONG_ANSWER",
		}
	}
	rewardStrs := make([]string, 0)
	rewardHiddenStrs := make([]string, 0)
	if itemResult.Reward != nil {
		for _, reward := range *itemResult.Reward {
			switch reward.TypeReward {
			case enum.LuckyWheelItemReward.TICKET_PATTERN:
				ticket := strings.ReplaceAll(reward.TicketPattern, "#", model.GenShortCodeWithTime())
				reward.TicketPattern = ticket
				rewardStrs = append(rewardStrs, "1 TICKET_PATTERN "+ticket)
				rewardHiddenStrs = append(rewardHiddenStrs, "1 TICKET_PATTERN")
			case enum.LuckyWheelItemReward.VOUCHER:
				patternVoucher := "LUCKY_WHEEL_#"
				if reward.VoucherPattern != nil {
					patternVoucher = *reward.VoucherPattern
				}
				voucherID, voucherCode := model.GenVoucherID()
				voucherCode = strings.ReplaceAll(patternVoucher, "#", voucherCode)
				reward.VoucherCode = voucherCode
				reward.VoucherID = voucherID
				rewardStrs = append(rewardStrs, "1 "+itemResult.ItemName+" - "+voucherCode)
				rewardHiddenStrs = append(rewardHiddenStrs, "1 "+itemResult.ItemName)
			case enum.LuckyWheelItemReward.POINTS:
				rewardStrs = append(rewardStrs, fmt.Sprintf("%d REWARD_POINT", reward.Points))
				rewardHiddenStrs = append(rewardHiddenStrs, fmt.Sprintf("%d REWARD_POINT", reward.Points))
			case enum.LuckyWheelItemReward.TURNS:
				rewardStrs = append(rewardStrs, fmt.Sprintf("%d TURN", reward.TurnsRotation))
				rewardHiddenStrs = append(rewardHiddenStrs, fmt.Sprintf("%d TURN", reward.TurnsRotation))
			case enum.LuckyWheelItemReward.OTHER:
				// turn off circa
				// if systemDisplay == "CIRCA" {
				// 	circaVoucher := ""
				// 	voucherRes, errCreateVoucher := client.Services.Circa.CreateCircaVoucher(customer.Phone, itemResult.ItemCode, reward.RewardDescription)
				// 	if errCreateVoucher == nil {
				// 		circaVoucher = voucherRes.VoucherCode
				// 	}
				// 	model.CustomerLuckyWheelDB.UpdateOneWithOption(queryUpdateCustomerLuckyWheel, bson.M{
				// 		"$push": bson.M{"reward": model.CustomerLuckyWheelReward{
				// 			ItemCode:          itemResult.ItemCode,
				// 			VoucherCode:       circaVoucher,
				// 			RewardDescription: reward.RewardDescription,
				// 		}},
				// 	})
				// 	msg := "1 " + itemResult.ItemName
				// 	if circaVoucher != "" {
				// 		msg += " - " + circaVoucher
				// 	}
				// 	rewardStrs = append(rewardStrs, msg)
				// 	rewardHiddenStrs = append(rewardHiddenStrs, "1 "+itemResult.ItemName)
				// } else {
				rewardStrs = append(rewardStrs, reward.RewardDescription)
				rewardHiddenStrs = append(rewardHiddenStrs, reward.RewardDescription)
				// }
			}
		}
	}
	go func() {
		// note: update customer lucky wheel history
		if itemResult.ItemCode != "" {
			keyPerDay := fmt.Sprintf("used_item_quantity.%d_%d_%s", nowVN.Day(), nowVN.Month(), itemResult.ItemCode)
			model.CustomerLuckyWheelDB.UpdateOneWithOption(queryUpdateCustomerLuckyWheel, bson.M{
				"$inc": bson.M{"used_item_quantity." + itemResult.ItemCode: 1, keyPerDay: 1},
			})
			model.LuckyWheelItemDB.UpdateOneWithOption(bson.M{"item_code": itemResult.ItemCode, "lucky_wheel_code": code}, bson.M{
				"$inc": bson.M{"used_quantity": 1, keyPerDay: 1},
			})
		}

		// note: handle reward
		if itemResult.Reward != nil {
			for _, reward := range *itemResult.Reward {
				switch reward.TypeReward {
				case enum.LuckyWheelItemReward.VOUCHER:
					voucherRes := CreateVoucherByPromotionID(nil, customer.CustomerID, reward.PromotionID, reward.VoucherCode, reward.VoucherID)
					if voucherRes.Status == common.APIStatus.Ok {
						voucher := voucherRes.Data.([]*model.Voucher)[0]
						model.CustomerLuckyWheelDB.UpdateOneWithOption(queryUpdateCustomerLuckyWheel, bson.M{
							"$push": bson.M{"reward": model.CustomerLuckyWheelReward{
								ItemCode:    itemResult.ItemCode,
								VoucherCode: voucher.Code,
							},
							}})
					}
				case enum.LuckyWheelItemReward.TURNS:
					model.CustomerLuckyWheelDB.UpdateOneWithOption(queryUpdateCustomerLuckyWheel, bson.M{
						"$inc": bson.M{"quantity": reward.TurnsRotation},
						"$push": bson.M{"reward": model.CustomerLuckyWheelReward{
							ItemCode:      itemResult.ItemCode,
							TurnsRotation: reward.TurnsRotation,
						}},
					})
				case enum.LuckyWheelItemReward.TICKET_PATTERN:
					model.CustomerLuckyWheelDB.UpdateOneWithOption(queryUpdateCustomerLuckyWheel, bson.M{
						"$push": bson.M{"reward": model.CustomerLuckyWheelReward{
							ItemCode: itemResult.ItemCode,
							Ticket:   reward.TicketPattern,
						}},
					})
				case enum.LuckyWheelItemReward.POINTS:
					errIncrPoint := client.Services.Customer.UpdatePoint(acc.AccountID, reward.Points, "LUCKY_WHEEL")
					if errIncrPoint == nil {
						model.CustomerLuckyWheelDB.UpdateOneWithOption(queryUpdateCustomerLuckyWheel, bson.M{
							"$push": bson.M{"reward": model.CustomerLuckyWheelReward{
								ItemCode: itemResult.ItemCode,
								Points:   reward.Points,
							}},
						})
					}
				case enum.LuckyWheelItemReward.OTHER:
					if systemDisplay != "CIRCA" {
						model.CustomerLuckyWheelDB.UpdateOneWithOption(queryUpdateCustomerLuckyWheel, bson.M{
							"$push": bson.M{"reward": model.CustomerLuckyWheelReward{
								ItemCode:          itemResult.ItemCode,
								RewardDescription: reward.RewardDescription,
							}},
						})
					}
				}
			}
		}

		model.LuckyWheelLogDB.Create(model.LuckyWheelLog{
			AccountID:      acc.AccountID,
			LuckyWheelCode: code,
			CustomerPhone:  phone,
			ItemCode:       itemResult.ItemCode,
			ItemImage:      itemResult.ImageUrl,
			IsHasGift:      len(rewardStrs) > 0,
			Message: func() string {
				if len(rewardStrs) > 0 {
					return strings.Join(rewardStrs, ", ")
				}
				if !isQuestionPassed {
					return "WRONG_ANSWER"
				}
				return "LUCKY_NEXT_TIME"
			}(),
			MessageHidden: func() string {
				if len(rewardStrs) > 0 {
					if systemDisplay == "CIRCA" && len(customer.Name) > 8 {
						// replace phone from *********** to 8493****689 (customer.Name)
						phoneHidden := customer.Name
						phoneHidden = phoneHidden[:4] + "****" + phoneHidden[len(phoneHidden)-3:]
						customer.Name = phoneHidden
					}
					return "CONGRATULATIONS_TO " + customer.Name + "  FOR_SUCCESSFULLY_SPINNING_AND_WINNING  " + strings.Join(rewardHiddenStrs, ", ")
				}
				if !isQuestionPassed {
					return "WRONG_ANSWER"
				}
				return ""
			}(),
			Question: func() *model.LuckyWheelQuestionLog {
				if luckyWheel.IsQuestionRequired == nil || !*luckyWheel.IsQuestionRequired {
					return nil
				}

				return &model.LuckyWheelQuestionLog{
					QuestionCode:  question.Code,
					Question:      question.Question,
					CorrectAnswer: *correctAnswer,
					UserAnswer:    *userAnswer,
				}
			}(),
		})
	}()

	if !isQuestionPassed {
		type WrongAnswerResp struct {
			QuestionCode    string `json:"questionCode"`
			AnswerID        int64  `json:"answerId"`
			CorrectAnswerID int64  `json:"correctAnswerId"`
		}
		return &common.APIResponse{
			Status: common.APIStatus.Invalid,
			Data: []*WrongAnswerResp{{
				QuestionCode:    questionCode,
				AnswerID:        answerID,
				CorrectAnswerID: correctAnswer.AnswerID,
			}},
			Message:   "WRONG_ANSWER",
			ErrorCode: "WRONG_ANSWER",
		}
	}

	return &common.APIResponse{
		Status: common.APIStatus.Ok,
		Data:   []*model.LuckyWheelItem{itemResult},
		Message: func() string {
			if len(rewardStrs) > 0 {
				return "YOU_SPUN_AND_HIT " + "<strong>" + strings.Join(rewardStrs, ", ") + "</strong>"
			}
			return "LUCKY_NEXT_TIME"
		}(),
	}
}

func GetSelfLuckyWheel(acc *model.Account, luckyWheelCode, systemDisplay, phone, provinceCode string) *common.APIResponse {
	var customer *model.Customer
	var err error
	switch systemDisplay {
	case "CIRCA":
		if phone == "" {
			phone = "empty"
		}
		customer = &model.Customer{
			Phone:        phone,
			ProvinceCode: provinceCode,
			Scope:        "CIRCA",
			Name:         phone,
		}
		acc.AccountID = 0
	case "BUYMED":
		customer, err = client.Services.Customer.GetCustomerByAccountID(acc.AccountID)
		if err != nil {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				ErrorCode: "CUSTOMER_NOT_FOUND",
				Message:   "Customer is not found",
			}
		}
	}

	query := bson.M{
		"is_active":            true,
		"start_time":           bson.M{"$lte": time.Now()},
		"end_time":             bson.M{"$gte": time.Now()},
		"system_display":       systemDisplay,
		"scope.province_codes": bson.M{"$in": []string{customer.ProvinceCode, "all"}},
		//"scope.levels": []string{customer.Level, "all"},
		//"scope.scopes": []string{customer.Scope, "all"},
	}

	if luckyWheelCode != "" {
		query["code"] = luckyWheelCode
	}

	qLuckyWheel := model.LuckyWheelDB.Query(query, 0, 100, &primitive.M{"created_time": -1})
	if qLuckyWheel.Status != common.APIStatus.Ok {
		return qLuckyWheel
	}
	luckyWheels := qLuckyWheel.Data.([]*model.LuckyWheel)
	luckyWheelCodeNeedSubmits := make([]string, 0)
	customerMap := make(map[string]*model.GamificationCustomer)
	for _, lk := range luckyWheels {
		if lk.Scope == nil || lk.Scope.CustomerApplyType == "ALL" || lk.Scope.CustomerApplyType == "" {
			continue
		}
		luckyWheelCodeNeedSubmits = append(luckyWheelCodeNeedSubmits, lk.Code)
	}
	qCustomerJoin := model.GamificationCustomerDB.Query(bson.M{
		"status":           "ACTIVE",
		"customer_id":      customer.CustomerID,
		"lucky_wheel_code": bson.M{"$in": luckyWheelCodeNeedSubmits},
	}, 0, 0, &primitive.M{"created_time": -1})
	if qCustomerJoin.Status == common.APIStatus.Ok {
		for _, c := range qCustomerJoin.Data.([]*model.GamificationCustomer) {
			customerMap[c.LuckyWheelCode] = c
		}
	}

	lkSatisfyConditions := make([]*model.LuckyWheel, 0)

	for _, lk := range luckyWheels {
		isMatchLevel := false
		isMatchScope := false
		isMatchCustomer := false
		if lk.Scope == nil || lk.Scope.CustomerApplyType == "ALL" || lk.Scope.CustomerApplyType == "" {
			isMatchCustomer = true
		} else {
			isMatchCustomer = customerMap[lk.Code] != nil
		}
		for _, level := range lk.Scope.CustomerLevels {
			if level == "all" || level == customer.Level {
				isMatchLevel = true
			}
		}
		for _, scope := range lk.Scope.CustomerScopes {
			if scope == "all" || scope == customer.Scope {
				isMatchScope = true
			}
		}
		if isMatchLevel && isMatchScope && isMatchCustomer {
			lkSatisfyConditions = append(lkSatisfyConditions, lk)
		}
	}

	if len(lkSatisfyConditions) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			ErrorCode: "LUCKY_WHEEL_NOT_FOUND",
			Message:   "Lucky wheel is not found",
		}
	}

	if len(lkSatisfyConditions) == 1 {
		luckyWheel := lkSatisfyConditions[0]

		queryItem := model.LuckyWheelItem{
			LuckyWheelCode: luckyWheel.Code,
			IsActive:       utils.ParseBoolToPointer(true),
		}
		qItem := model.LuckyWheelItemDB.Query(queryItem, 0, 0, &primitive.M{"index": 1})
		if qItem.Status != common.APIStatus.Ok {
			return qItem
		}
		qCustomer := model.CustomerLuckyWheelDB.QueryOne(model.CustomerLuckyWheel{AccountID: acc.AccountID, LuckyWheelCode: luckyWheel.Code, CustomerPhone: phone})
		if qCustomer.Status == common.APIStatus.Ok {
			customerLuckyWheel := qCustomer.Data.([]*model.CustomerLuckyWheel)[0]
			luckyWheel.Turns = customerLuckyWheel.Quantity
			if luckyWheel.IsAcceptFreeTurn != nil && *luckyWheel.IsAcceptFreeTurn {
				if customerLuckyWheel.LastFreeTime != nil {
					nextFreeTime := customerLuckyWheel.LastFreeTime.Add(time.Duration(luckyWheel.TimeToFreeTurn) * time.Minute)
					luckyWheel.NextFreeTime = &nextFreeTime
				} else {
					nextFeeTime := time.Now()
					luckyWheel.NextFreeTime = &nextFeeTime
				}
			}
		} else {
			luckyWheel.Turns = 0
			if luckyWheel.IsAcceptFreeTurn != nil && *luckyWheel.IsAcceptFreeTurn {
				nextFeeTime := time.Now()
				luckyWheel.NextFreeTime = &nextFeeTime
			}
		}
		items := qItem.Data.([]*model.LuckyWheelItem)
		now := time.Now()
		finalItems := make([]*model.LuckyWheelItem, 0)
		for _, item := range items {
			if item.StartTime != nil && item.StartTime.After(now) {
				continue
			}
			if item.EndTime != nil && item.EndTime.Before(now) {
				continue
			}
			finalItems = append(finalItems, item)
		}
		luckyWheel.Items = finalItems
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Data:    []*model.LuckyWheel{luckyWheel},
			Message: "Get lucky wheel success",
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Data:    lkSatisfyConditions,
		Message: "Get lucky wheel success",
	}
}

func GetSelfLuckyWheelLog(acc *model.Account, code, typeLog, phone, source string, offset, limit int64, getTotal bool) *common.APIResponse {
	if phone != "" {
		acc.AccountID = 0
	}
	if phone == "" && source == "CIRCA" {
		phone = "empty"
	}
	query := model.LuckyWheelLog{
		AccountID:      acc.AccountID,
		LuckyWheelCode: code,
		CustomerPhone:  phone,
	}
	if typeLog == "OTHER" {
		query = model.LuckyWheelLog{
			LuckyWheelCode: code,
			IsHasGift:      true,
		}
	}
	res := model.LuckyWheelLogDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if res.Status != common.APIStatus.Ok {
		return res
	}
	if getTotal {
		total := model.LuckyWheelLogDB.Count(model.LuckyWheelLog{
			AccountID:      acc.AccountID,
			LuckyWheelCode: code,
			CustomerPhone:  phone,
		}).Total
		res.Total = total
	}
	return res
}

func GetLuckyWheelLog(acc *model.Account, query *model.LuckyWheelLog, offset, limit int64, getTotal bool) *common.APIResponse {
	res := model.LuckyWheelLogDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if res.Status != common.APIStatus.Ok {
		return res
	}
	if getTotal {
		total := model.LuckyWheelLogDB.Count(query).Total
		res.Total = total
	}
	return res
}

func CreateCustomerLuckyWheel(acc *model.Account, luckyWheel *model.CustomerLuckyWheel) *common.APIResponse {
	after := options.After
	return model.CustomerLuckyWheelDB.UpdateOneWithOption(
		bson.M{"account_id": luckyWheel.AccountID, "lucky_wheel_code": luckyWheel.LuckyWheelCode},
		bson.M{"$inc": bson.M{"quantity": luckyWheel.Quantity}},
		&options.FindOneAndUpdateOptions{Upsert: utils.ParseBoolToPointer(true), ReturnDocument: &after})
}

func GetMissionLuckyWheel(acc *model.Account, luckyWheelCode string, phone string, systemDisplay string) *common.APIResponse {
	qMission := model.GamificationDetailDB.Query(model.GamificationDetail{
		LuckyWheelCode: luckyWheelCode,
	}, 0, 0, &primitive.M{"index_mission": 1})
	if qMission.Status != common.APIStatus.Ok {
		return qMission
	}
	missions := qMission.Data.([]*model.GamificationDetail)
	missionIds := make([]int64, 0)
	missionMap := make(map[int64]*model.GamificationDetail)
	for _, mission := range missions {
		missionIds = append(missionIds, mission.GamificationDetailID)
		missionMap[mission.GamificationDetailID] = mission
	}
	mapResult := make(map[int64]*model.GamificationResult)
	queryResult := bson.M{"account_id": acc.AccountID, "gamification_detail_id": bson.M{"$in": missionIds}, "lucky_wheel_code": luckyWheelCode}
	if systemDisplay == "CIRCA" {
		if phone == "" {
			phone = "empty"
		}
		queryResult = bson.M{"customer_phone": phone, "gamification_detail_id": bson.M{"$in": missionIds}, "lucky_wheel_code": luckyWheelCode}
	}
	qResult := model.GamificationResultDB.Query(queryResult, 0, 0, &primitive.M{"index": 1})
	if qResult.Status == common.APIStatus.Ok {
		results := qResult.Data.([]*model.GamificationResult)
		for _, missionValue := range results {
			mapResult[missionValue.GamificationDetailID] = missionValue
		}
	}
	sortedMissions := make([]*model.GamificationDetail, 0)
	unCompletedMission := make([]*model.GamificationDetail, 0)
	completedMission := make([]*model.GamificationDetail, 0)
	for _, mission := range missions {
		cond := mission.Condition
		var detailValue *model.DetailValue
		isCompleted := false
		if cond == nil {
			continue
		}
		if cond.StartTime != nil && cond.StartTime.After(time.Now()) {
			continue
		}
		if cond.EndTime != nil && cond.EndTime.Before(time.Now()) {
			continue
		}
		if data, ok := mapResult[mission.GamificationDetailID]; ok && data != nil {
			if data.DetailValue != nil {
				detailValue = data.DetailValue
			}
			isCompleted = data.Status == enum.GamificationResultStatus.COMPLETED
		} else {
			detailValue = &model.DetailValue{}
		}
		mission.ProcessInfos = handleMissionProcessInfo(mission.ProcessInfos, detailValue, cond, isCompleted)
		if !isNilOrDefaultValue(mission.ParentGamificationDetailID) {
			if mapResult[*mission.ParentGamificationDetailID] == nil {
				mission.IsBlocked = true
			} else {
				mission.IsBlocked = mapResult[*mission.ParentGamificationDetailID].Status != enum.GamificationResultStatus.COMPLETED
			}
			if mission.IsBlocked {
				if missionMap[*mission.ParentGamificationDetailID] != nil && missionMap[*mission.ParentGamificationDetailID].MissionName != nil {
					mission.ParentMissionName = *missionMap[*mission.ParentGamificationDetailID].MissionName
				}
			}
		}
		if isCompleted {
			completedMission = append(completedMission, mission)
		} else {
			unCompletedMission = append(unCompletedMission, mission)
		}
	}
	// sort slice unCompletedMission by isBlocked & index
	sort.Slice(unCompletedMission, func(i, j int) bool {
		if unCompletedMission[i].IsBlocked != unCompletedMission[j].IsBlocked {
			return !unCompletedMission[i].IsBlocked
		}
		if unCompletedMission[i].IndexMission != nil && unCompletedMission[i].IndexMission != nil {
			return *unCompletedMission[i].IndexMission < *unCompletedMission[j].IndexMission
		}
		return false
	})

	sortedMissions = append(unCompletedMission, completedMission...)
	qMission.Data = sortedMissions
	return qMission
}

func IncreaseCustomerLuckyWheel(acc *model.Account, luckyWheel *model.CustomerLuckyWheelLog) *common.APIResponse {
	if luckyWheel.Key != "" {
		qCheckDuplicate := model.CustomerLuckyWheelLogDB.QueryOne(model.CustomerLuckyWheelLog{
			Key: luckyWheel.Key,
		})
		if qCheckDuplicate.Status == common.APIStatus.Ok {
			return &common.APIResponse{
				Status:    common.APIStatus.Existed,
				Message:   "Key existed",
				ErrorCode: "KEY_EXISTED",
			}
		}
	}
	after := options.After
	if logRes := model.CustomerLuckyWheelLogDB.Create(luckyWheel); logRes.Status != common.APIStatus.Ok {
		return logRes
	}
	query := bson.M{"account_id": luckyWheel.AccountID, "lucky_wheel_code": luckyWheel.LuckyWheelCode}
	if luckyWheel.SystemDisplay == "CIRCA" {
		query = bson.M{"customer_phone": luckyWheel.CustomerPhone, "lucky_wheel_code": luckyWheel.LuckyWheelCode}
	}
	return model.CustomerLuckyWheelDB.UpdateOneWithOption(
		query,
		bson.M{"$inc": bson.M{"quantity": luckyWheel.Quantity}},
		&options.FindOneAndUpdateOptions{Upsert: utils.ParseBoolToPointer(true), ReturnDocument: &after})
}

func handleMissionProcessInfo(processInfos []*model.MissionProcessInfo, data *model.DetailValue, cond *model.GamificationDetailCondition, isCompleted bool) []*model.MissionProcessInfo {
	if !isNilOrDefaultValue(cond.MinSkuCount) {
		processInfos = append(processInfos, &model.MissionProcessInfo{
			UnitName:    "QUANTITY_OF_PRODUCTS",
			Value:       data.MinSkuCount,
			Target:      *cond.MinSkuCount,
			IsCompleted: data.MinSkuCount >= *cond.MinSkuCount,
		})
	}
	if !isNilOrDefaultValue(cond.MinOrderCount) {
		processInfos = append(processInfos, &model.MissionProcessInfo{
			UnitName:    "NUMBER_OF_ORDERS",
			Value:       data.MinOrderCount,
			Target:      *cond.MinOrderCount,
			IsCompleted: data.MinOrderCount >= *cond.MinOrderCount,
		})
	}
	if !isNilOrDefaultValue(cond.MinTotalValue) {
		processInfos = append(processInfos, &model.MissionProcessInfo{
			UnitName:    "TOTAL_VALUE_ORDERED",
			Value:       data.MinTotalValue,
			Target:      *cond.MinTotalValue,
			IsCompleted: data.MinTotalValue >= *cond.MinTotalValue,
		})
	}
	if cond.Type == enum.GamificationConditionType.SHARE {
		processInfos = append(processInfos, &model.MissionProcessInfo{
			UnitName:    "NUMBER_OF_SHARES",
			Target:      1,
			IsCompleted: isCompleted,
		})
	}
	if cond.Type == enum.GamificationConditionType.DISCOVER {
		processInfos = append(processInfos, &model.MissionProcessInfo{
			UnitName:    "NUMBER_OF_EXPLORATIONS",
			Target:      1,
			IsCompleted: isCompleted,
		})
	}
	return processInfos
}

func isSkipSpecialItem(customer *model.Customer, item *model.LuckyWheelItem, mapItemSkip map[string]bool) bool {
	keyProvince := "PROVINCE_" + customer.ProvinceCode + "_" + item.LuckyWheelCode + "_" + item.ItemCode
	keyCustomerID := "CUSTOMER_" + strconv.Itoa(int(customer.CustomerID)) + "_" + item.LuckyWheelCode + "_" + item.ItemCode
	keyCustomerLevel := "CUSTOMER_LEVEL_" + customer.Level + "_" + item.LuckyWheelCode + "_" + item.ItemCode
	for _, tag := range customer.Tags {
		keyTag := "TAG_" + tag + "_" + item.LuckyWheelCode + "_" + item.ItemCode
		if data, ok := mapItemSkip[keyTag]; ok && data {
			return true
		}
	}
	if data, ok := mapItemSkip[keyProvince]; ok && data {
		return true
	}
	if data, ok := mapItemSkip[keyCustomerID]; ok && data {
		return true
	}
	if data, ok := mapItemSkip[keyCustomerLevel]; ok && data {
		return true
	}
	return false
}
