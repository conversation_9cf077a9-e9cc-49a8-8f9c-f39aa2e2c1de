package api

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/buymed.th/marketplace/promotion/action"
	"gitlab.com/buymed.th/marketplace/promotion/model"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// GetSingleGamification is func get gamification by gamification code
func GetSingleGamification(req sdk.APIRequest, resp sdk.APIResponder) error {
	var q = req.GetParam("q")
	var code = req.GetParam("code")
	var id = sdk.ParseInt64(req.GetParam("id"), 0)
	// fill query
	query := model.Gamification{
		GamificationCode: code,
		GamificationID:   id,
	}
	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not parse input data.",
			})

		}
	}
	return resp.Respond(action.GetGamification(&query))
}

// GetSelfSingleGamification is func get gamification by gamification code
func GetSelfSingleGamification(req sdk.APIRequest, resp sdk.APIResponder) error {
	var q = req.GetParam("q")
	var code = req.GetParam("code")
	var id = sdk.ParseInt64(req.GetParam("id"), 0)
	// fill query
	query := model.Gamification{
		GamificationCode: code,
		GamificationID:   id,
	}
	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not parse input data.",
			})

		}
	}
	return resp.Respond(action.GetGamificationSelfSingle(getActionSource(req), &query))
}

// GetGamificationList iss func get list Gamifications
func GetGamificationList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		q        = req.GetParam("q")
		search   = req.GetParam("search")
		ids      = req.GetParam("ids")
		codes    = req.GetParam("codes")
	)

	if limit < 0 {
		limit = 20
	}

	if limit > 100 {
		limit = 100
	}

	if offset < 0 {
		offset = 0
	}

	// fill query
	query := model.Gamification{}
	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not parse input data.",
			})

		}
	}

	// if want to search
	if len(search) > 0 {
		search := parserQ(search)
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"hash_tag": primitive.Regex{Pattern: fmt.Sprintf(".*%s.*", search), Options: ""},
		})
	}

	if len(query.Status) > 0 {
		now := time.Now()
		switch query.Status {
		case "UPCOMING":
			query.ComplexQuery = append(query.ComplexQuery, &bson.M{
				"start_time": bson.M{"$gt": now},
			})
		case "PROCESSING":
			query.ComplexQuery = append(query.ComplexQuery, &bson.M{
				"start_time": bson.M{"$lte": now},
				"end_time":   bson.M{"$gte": now},
			})
		case "SCORING":
			query.ComplexQuery = append(query.ComplexQuery, &bson.M{
				"start_time":          bson.M{"$lte": now},
				"end_cal_result_time": bson.M{"$gte": now},
			})
		case "EXPIRED":
			query.ComplexQuery = append(query.ComplexQuery, &bson.M{
				"end_time": bson.M{"$lt": now},
			})
		}
	}

	if len(ids) > 0 {
		idsArr := strings.Split(ids, ",")
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"gamification_id": &bson.M{
				"$in": idsArr,
			},
		})
	}

	if len(codes) > 0 {
		codesArr := strings.Split(codes, ",")
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"gamification_code": &bson.M{
				"$in": codesArr,
			},
		})
	}

	if query.CreatedTimeFrom != nil || query.CreatedTimeTo != nil {
		t := time.Now()
		if query.CreatedTimeFrom == nil {
			t = t.Add(-30 * 24 * time.Hour) // default from is last 30 days
			query.CreatedTimeFrom = &t
		}

		if query.CreatedTimeTo == nil {
			query.CreatedTimeTo = &t
		}
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"created_time": bson.M{
				"$gte": query.CreatedTimeFrom,
				"$lte": query.CreatedTimeTo,
			},
		})
	}

	if query.ProcessingTimeFrom != nil || query.ProcessingTimeTo != nil {
		if query.ProcessingTimeFrom != nil {
			query.ComplexQuery = append(query.ComplexQuery, &bson.M{
				"start_time": bson.M{"$gte": query.ProcessingTimeFrom},
			})
		}

		if query.ProcessingTimeTo != nil {
			query.ComplexQuery = append(query.ComplexQuery, &bson.M{
				"start_time": bson.M{"$lte": query.ProcessingTimeTo},
			})
		}
	}

	if query.SellerCode == "Thuocsi" {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"$or": []bson.M{
				{
					"seller_code": "",
				},
				{
					"seller_code": nil,
				},
			},
		})
		query.SellerCode = ""
	}

	return resp.Respond(action.GetGamificationList(&query, offset, limit, getTotal))
}

// GetGamificationSelfList iss func get list Gamifications
func GetGamificationSelfList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		q        = req.GetParam("q")
		search   = req.GetParam("search")
		ids      = req.GetParam("ids")
		codes    = req.GetParam("codes")
		status   = req.GetParam("status")
	)

	if limit < 0 {
		limit = 20
	}

	if limit > 100 {
		limit = 100
	}

	if offset < 0 {
		offset = 0
	}

	// fill query
	query := model.Gamification{}
	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not parse input data.",
			})

		}
	}

	// if want to search
	if len(search) > 0 {
		search := parserQ(search)
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"hash_tag": primitive.Regex{Pattern: fmt.Sprintf(".*%s.*", search), Options: ""},
		})
	}
	now := time.Now()
	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
		"public_time": bson.M{"$lt": now},
	})

	if len(status) > 0 {
		query.Status = status
	}

	if len(query.Status) > 0 {
		switch query.Status {
		case "UPCOMING":
			query.ComplexQuery = append(query.ComplexQuery, &bson.M{
				"start_time": bson.M{"$gt": now},
			})
		case "PROCESSING":
			query.ComplexQuery = append(query.ComplexQuery, &bson.M{
				"start_time": bson.M{"$lte": now},
				"end_time":   bson.M{"$gte": now},
			})
		case "EXPIRED":
			query.ComplexQuery = append(query.ComplexQuery, &bson.M{
				"end_time": bson.M{"$lt": now},
			})
		}
	}

	if len(ids) > 0 {
		idsArr := strings.Split(ids, ",")
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"gamification_id": &bson.M{
				"$in": idsArr,
			},
		})
	}

	if len(codes) > 0 {
		codesArr := strings.Split(codes, ",")
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"gamification_code": &bson.M{
				"$in": codesArr,
			},
		})
	}

	if query.CreatedTimeFrom != nil || query.CreatedTimeTo != nil {
		t := time.Now()
		if query.CreatedTimeFrom == nil {
			t = t.Add(-30 * 24 * time.Hour) // default from is last 30 days
			query.CreatedTimeFrom = &t
		}

		if query.CreatedTimeTo == nil {
			query.CreatedTimeTo = &t
		}
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"created_time": bson.M{
				"$gte": query.CreatedTimeFrom,
				"$lte": query.CreatedTimeTo,
			},
		})
	}

	return resp.Respond(action.GetGamificationSelfList(getActionSource(req), &query, offset, limit, getTotal))
}

// GetGamificationSelfResultList iss func get list result
func GetGamificationSelfResultList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		q        = req.GetParam("q")
		ids      = req.GetParam("ids")
		codes    = req.GetParam("codes")
	)

	if limit < 0 {
		limit = 20
	}

	if limit > 100 {
		limit = 100
	}

	if offset < 0 {
		offset = 0
	}

	// fill query
	query := model.GamificationResult{}
	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not parse input data.",
			})

		}
	}

	if len(ids) > 0 {
		idsArr := strings.Split(ids, ",")
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"gamification_result_id": &bson.M{
				"$in": idsArr,
			},
		})
	}

	if len(codes) > 0 {
		codesArr := strings.Split(codes, ",")
		query.ComplexQuery = []*bson.M{
			{
				"gamification_result_code": &bson.M{
					"$in": codesArr,
				},
			},
		}
	}

	if query.CreatedTimeFrom != nil || query.CreatedTimeTo != nil {
		t := time.Now()
		if query.CreatedTimeFrom == nil {
			t = t.Add(-30 * 24 * time.Hour) // default from is last 30 days
			query.CreatedTimeFrom = &t
		}

		if query.CreatedTimeTo == nil {
			query.CreatedTimeTo = &t
		}
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"created_time": bson.M{
				"$gte": query.CreatedTimeFrom,
				"$lte": query.CreatedTimeTo,
			},
		})
	}

	if query.OrderID != 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"order_ids": query.OrderID,
		})
	}

	return resp.Respond(action.GetGamificationSelfResultList(getActionSource(req), &query, offset, limit, getTotal))
}

// GetGamificationResultList iss func get list GamificationResultList
func GetGamificationResultList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset         = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit          = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal       = req.GetParam("getTotal") == "true"
		q              = req.GetParam("q")
		ids            = req.GetParam("ids")
		codes          = req.GetParam("codes")
		customerIds    = req.GetParam("customerIds")
		customerPhones = req.GetParam("customerPhones")
	)

	if limit < 0 {
		limit = 20
	}

	if limit > 1000 {
		limit = 1000
	}

	if offset < 0 {
		offset = 0
	}

	// fill query
	query := model.GamificationResult{}
	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not parse input data.",
			})

		}
	}

	if len(ids) > 0 {
		idsArr := strings.Split(ids, ",")
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"gamification_result_id": &bson.M{
				"$in": idsArr,
			},
		})
	}

	if len(codes) > 0 {
		codesArr := strings.Split(codes, ",")
		query.ComplexQuery = []*bson.M{
			{
				"gamification_result_code": &bson.M{
					"$in": codesArr,
				},
			},
		}
	}

	if len(customerIds) > 0 {
		idsArr := strings.Split(customerIds, ",")
		idsArrInt := make([]int64, 0)
		for _, id := range idsArr {
			idsArrInt = append(idsArrInt, sdk.ParseInt64(id, 0))
		}
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"customer_id": &bson.M{
				"$in": idsArrInt,
			},
		})
	}

	if len(customerPhones) > 0 {
		phonesArr := strings.Split(customerPhones, ",")
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"customer_phone": &bson.M{
				"$in": phonesArr,
			},
		})
	}

	if query.CreatedTimeFrom != nil || query.CreatedTimeTo != nil {
		t := time.Now()
		if query.CreatedTimeFrom == nil {
			t = t.Add(-30 * 24 * time.Hour) // default from is last 30 days
			query.CreatedTimeFrom = &t
		}

		if query.CreatedTimeTo == nil {
			query.CreatedTimeTo = &t
		}
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"created_time": bson.M{
				"$gte": query.CreatedTimeFrom,
				"$lte": query.CreatedTimeTo,
			},
		})
	}

	if query.OrderID != 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"order_ids": query.OrderID,
		})
	}

	return resp.Respond(action.GetGamificationResultList(&query, offset, limit, getTotal))
}

// CreateGamification is func create a Gamifications with payload
func CreateGamification(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.Gamification
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}

	if err = validateGamification(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	actionSource := getActionSource(req)

	return resp.Respond(action.CreateGamification(actionSource, &input))
}

func validateGamification(input *model.Gamification) error {
	if err := validateGamificationDetails(input.Details); err != nil {
		return err
	}
	return nil
}

func validateGamificationDetails(input []*model.GamificationDetail) error {
	for _, detail := range input {
		if detail == nil || detail.Reward == nil {
			continue
		}
		if err := model.Checker.Validate(*detail.Reward); err != nil {
			return err
		}
		maxVoucherDiscount := detail.Reward.MaxVoucherDiscount
		if maxVoucherDiscount != nil && *maxVoucherDiscount%100 > 0 {
			return fmt.Errorf("maxVoucherDiscount must be a multiple of 100")
		}
	}
	return nil
}

// UpdateGamification is func update a Gamifications
func UpdateGamification(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.Gamification
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không thể đọc dữ liệu",
		})
	}

	if err = validateGamification(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	return resp.Respond(action.UpdateGamification(getActionSource(req), &input))
}

// ToolUpdateGamification
func ToolUpdateGamification(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.Gamification
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không thể đọc dữ liệu",
		})
	}

	return resp.Respond(action.ToolUpdateGamification(&input))
}

// SyncGamificationResultFromOrder is func sync order with gamification
func SyncGamificationResultFromOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input = struct {
		OrderID int64 `json:"orderID,omitempty"`
	}{}
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không thể đọc dữ liệu",
		})
	}
	return resp.Respond(action.SyncGamificationResultFromOrder(getActionSource(req), input.OrderID))
}

func GamificationSync(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input struct {
		CreatedBySystem string `json:"createdBySystem,omitempty"`
	}
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.SyncGamificationResult(acc, input.CreatedBySystem))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func SyncGamificationFromOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var payload struct {
		Values                  []*model.OrderCountPoint `json:"values"`
		LogGamificationSyncCode string                   `json:"logSyncGamificationCode"`
	}
	if err := req.GetContent(&payload); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if err := model.Checker.Validate(&payload); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}
	return resp.Respond(action.SyncGamificationFromOrder(payload.Values, payload.LogGamificationSyncCode))
}

// GamificationLogGetList is func get list level
func GamificationLogGetList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q        = req.GetParam("q")
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 1000)
		getTotal = req.GetParam("getTotal") == "true"
	)
	var query = model.SyncGamificationLog{}
	if len(q) > 0 {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Không thể đọc dữ liệu",
				ErrorCode: "PAYLOAD_INVALID",
			})
		}
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.GetGamificationLogList(&query, offset, limit, getTotal))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

// GetGamificationListBySellerCode is func get list gamification
func GetGamificationListBySellerCode(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		q        = req.GetParam("q")
		search   = req.GetParam("search")
		ids      = req.GetParam("ids")
		codes    = req.GetParam("codes")
	)

	if limit < 0 {
		limit = 20
	}

	if limit > 100 {
		limit = 100
	}

	if offset < 0 {
		offset = 0
	}

	// fill query
	query := model.Gamification{}
	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not parse input data.",
			})

		}
	}

	if len(query.SellerCode) <= 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing sellerCode",
		})
	}

	// if want to search
	if len(search) > 0 {
		search := parserQ(search)
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"hash_tag": primitive.Regex{Pattern: fmt.Sprintf(".*%s.*", search), Options: ""},
		})
	}
	now := time.Now()
	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
		"public_time": bson.M{"$lt": now},
		"end_time":    bson.M{"$gte": now},
	})

	if len(ids) > 0 {
		idsArr := strings.Split(ids, ",")
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"gamification_id": &bson.M{
				"$in": idsArr,
			},
		})
	}

	if len(codes) > 0 {
		codesArr := strings.Split(codes, ",")
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"gamification_code": &bson.M{
				"$in": codesArr,
			},
		})
	}

	if query.CreatedTimeFrom != nil || query.CreatedTimeTo != nil {
		t := time.Now()
		if query.CreatedTimeFrom == nil {
			t = t.Add(-30 * 24 * time.Hour) // default from is last 30 days
			query.CreatedTimeFrom = &t
		}

		if query.CreatedTimeTo == nil {
			query.CreatedTimeTo = &t
		}
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"created_time": bson.M{
				"$gte": query.CreatedTimeFrom,
				"$lte": query.CreatedTimeTo,
			},
		})
	}

	return resp.Respond(action.GetGamificationListBySellerCode(&query, offset, limit, getTotal))
}

func GenerateGamificationReward(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.Gamification
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.AddRewardsGenerationJob(*acc, input))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

// SubmitJoinGamification is func to submit join gamification
func SubmitJoinGamification(req sdk.APIRequest, resp sdk.APIResponder) error {
	var payload struct {
		GamificationID int64 `json:"gamificationId"`
	}
	if err := req.GetContent(&payload); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if err := model.Checker.Validate(&payload); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	actionSource := getActionSource(req)

	return resp.Respond(action.SubmitJoinGamification(actionSource, payload.GamificationID))
}

// GamificationCustomerGetList is func get list gamification customer
func GamificationCustomerGetList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q        = req.GetParam("q")
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 1000)
		getTotal = req.GetParam("getTotal") == "true"
	)
	var query = model.GamificationCustomer{}
	if len(q) > 0 {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Không thể đọc dữ liệu",
				ErrorCode: "PAYLOAD_INVALID",
			})
		}
	}
	if query.CustomerStatus == "BLACKLIST" {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"status": "BLACKLIST",
		})
	}
	if query.CustomerStatus == "NORMAL" {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"status": bson.M{"$ne": "BLACKLIST"},
		})
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.GamificationCustomerList(&query, offset, limit, getTotal))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func AddGamificationCustomer(req sdk.APIRequest, resp sdk.APIResponder) error {
	var payload struct {
		GamificationId int64   `json:"gamificationId"`
		CustomerIds    []int64 `json:"customerIds"`
		LuckyWheelCode string  `json:"luckyWheelCode"`
		Status         string  `json:"status"`
	}
	if err := req.GetContent(&payload); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if err := model.Checker.Validate(&payload); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	return resp.Respond(action.AddListCustomerIntoGamification(payload.GamificationId, payload.CustomerIds, payload.LuckyWheelCode, payload.Status))
}

func UpdateGamificationCustomerStatus(req sdk.APIRequest, resp sdk.APIResponder) error {
	var payload model.GamificationCustomer
	if err := req.GetContent(&payload); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if err := model.Checker.Validate(&payload); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	return resp.Respond(action.UpdateGamificationCustomerStatus(payload))
}

func DeleteGamificationCustomer(req sdk.APIRequest, resp sdk.APIResponder) error {
	code := req.GetParam("code")
	return resp.Respond(action.DeleteGamificationCustomer(code))
}

func PushGamificationScore(req sdk.APIRequest, resp sdk.APIResponder) error {
	var payload action.Mission
	if err := req.GetContent(&payload); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	return resp.Respond(action.PushGamificationScore(&payload))
}

func PushGamificationReScore(req sdk.APIRequest, resp sdk.APIResponder) error {
	var payload action.ReScoreMission
	if err := req.GetContent(&payload); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	return resp.Respond(action.PushGamificationReScore(&payload))
}

func SyncCustomerJoinGamification(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.SyncCustomerJoinGamification(req.GetParam("gamificationCode")))
}
