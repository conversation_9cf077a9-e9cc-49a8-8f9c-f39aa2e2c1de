package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/buymed.th/marketplace/promotion/model/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type Ticket struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	ApprovedTime    *time.Time         `json:"approvedTime,omitempty" bson:"approved_time,omitempty"`
	RejectedTime    *time.Time         `json:"rejectedTime,omitempty" bson:"rejected_time,omitempty"`
	CreatedBy       int64              `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	UpdatedBy       int64              `json:"updatedBy,omitempty" bson:"updated_by,omitempty"`

	TicketType          string                     `json:"ticketType,omitempty" bson:"ticket_type,omitempty"`
	TicketID            int64                      `json:"ticketID,omitempty" bson:"ticket_id,omitempty"`
	TicketCode          string                     `json:"ticketCode,omitempty" bson:"ticket_code,omitempty"`
	CampaignID          int64                      `json:"campaignID,omitempty" bson:"campaign_id,omitempty"`
	ProductID           int64                      `json:"productID,omitempty" bson:"product_id,omitempty"`
	ProductCode         string                     `json:"productCode,omitempty" bson:"product_code,omitempty"`
	Sku                 string                     `json:"sku" bson:"sku,omitempty"`
	SellerCode          string                     `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	Price               float64                    `json:"price,omitempty" bson:"price,omitempty"`
	SalePrice           float64                    `json:"salePrice,omitempty" bson:"sale_price,omitempty"`
	PercentageDiscount  *float64                   `json:"percentageDiscount,omitempty" bson:"percentage_discount,omitempty"`
	AbsoluteDiscount    *float64                   `json:"absoluteDiscount,omitempty" bson:"absolute_discount,omitempty"`
	Quantity            int64                      `json:"quantity,omitempty" bson:"quantity,omitempty"`
	MaxQuantityPerOrder *int64                     `json:"maxQuantityPerOrder,omitempty" bson:"max_quantity_per_order,omitempty"`
	MaxDiscount         float64                    `json:"maxDiscount,omitempty" bson:"max_discount,omitempty"`
	Status              enum.TicketStateValue      `json:"status,omitempty" bson:"status,omitempty"`
	SaleType            enum.CampaignSaleValueType `json:"saleType,omitempty" bson:"sale_type,omitempty"`
	Source              string                     `json:"source,omitempty" bson:"source,omitempty"`
	FlashSaleTimes      *[]string                  `json:"flashSaleTimes,omitempty" bson:"flash_sale_times,omitempty"`
	Note                string                     `json:"note,omitempty" bson:"note,omitempty"`
}

var TicketDB = &db.Instance{
	ColName:        "ticket",
	TemplateObject: &Ticket{},
}

// InitTicketModel is func init model
func InitTicketModel(s *mongo.Database) {
	TicketDB.ApplyDatabase(s)

	// t := true
	// TicketDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "ticket_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })

	// TicketDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "ticket_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })

	// TicketDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "status", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// TicketDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "seller_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// TicketDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "seller_code", Value: 1},
	// 	primitive.E{Key: "product_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
}
