package enum

type LuckyWheelItemRewardType string

type luckyWheelItemReward struct {
	VOUCHER        LuckyWheelItemRewardType
	POINTS         LuckyWheelItemRewardType
	TURNS          LuckyWheelItemRewardType
	TICKET_PATTERN LuckyWheelItemRewardType
	OTHER          LuckyWheelItemRewardType
}

var LuckyWheelItemReward = &luckyWheelItemReward{
	VOUCHER:        "VOUCHER",
	POINTS:         "POINTS",
	TURNS:          "TURNS",
	TICKET_PATTERN: "TICKET_PATTERN",
	OTHER:          "OTHER",
}
