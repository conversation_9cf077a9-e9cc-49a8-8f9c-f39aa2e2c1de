package enum

type CustomerScopeType string

type customerScope struct {
	PHARMACY           CustomerScopeType
	CLINIC             CustomerScopeType
	DRUGSTORE          CustomerScopeType
	HOSPITAL           CustomerScopeType
	PHARMA_COMPANY     CustomerScopeType
	DENTISTRY          CustomerScopeType
	BEAUTY_SALON       CustomerScopeType
	HEALTH_CENTER      CustomerScopeType
	ANIMAL_PHARMACY    CustomerScopeType
	WHOLESALE_PHARMACY CustomerScopeType
	ANIMAL_CLINIC      CustomerScopeType
	NURSE_CLINIC       CustomerScopeType
}

var CustomerScope = &customerScope{
	PHARMACY:           "PHARMACY",
	CLINIC:             "<PERSON><PERSON><PERSON><PERSON>",
	DRUGSTORE:          "DRUGSTORE",
	HOSPITAL:           "HOSPITAL",
	PHARMA_COMPANY:     "PHARMA_COMPANY",
	DENTISTRY:          "DENTISTRY",
	BEAUTY_SALON:       "BEAUTY_SALON",
	HEALTH_CENTER:      "HEALTH_CENTER",
	ANIMAL_PHARMACY:    "ANIMAL_PHARMACY",
	WHOLE<PERSON>LE_PHARMACY: "WHOLESALE_PHARMACY",
	ANIMAL_CLINIC:      "ANIMAL_CLINIC",
	NURSE_CLINIC:       "NURSE_CLINIC",
}
