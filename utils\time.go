package utils

import (
	"math"
	"time"
)

func CalculateRealDayBetween(t1, t2 *time.Time) int {
	tz := time.FixedZone("UTC+7", +7*60*60)

	t1Real := t1.In(tz)
	t2Real := t2.In(tz)

	t1Truncate := time.Date(t1Real.Year(), t1Real.Month(), t1Real.Day(), 0, 0, 0, 0, tz)
	t2Truncate := time.Date(t2Real.Year(), t2Real.Month(), t2Real.Day(), 0, 0, 0, 0, tz)

	return int(math.Ceil(t2Truncate.Sub(t1Truncate).Hours() / 24))
}

// offset is the number of hours to add to GMT+0
func GetCurrentTimeByGMT(offset int) time.Time {
	gmtTimeLoc := time.FixedZone("GMT", 0)
	return time.Now().In(gmtTimeLoc).Add(time.Hour * time.Duration(offset))
}
