package utils

import (
	"regexp"
	"strconv"
	"strings"
	"unicode"

	"golang.org/x/text/transform"
	"golang.org/x/text/unicode/norm"
)

var mapVNICode = map[string]string{
	"a": "[à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ]",
	"ê": "[è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ]",
	"i": "[ì|í|ị|ỉ|ĩ]",
	"o": "[ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ]",
	"u": "[ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ]",
	"y": "[ỳ|ý|ỵ|ỷ|ỹ]",
	"d": "[đ]",
	"-": "[\\_|\\-|\\t|\\s]+",
}

var emailRegex = regexp.MustCompile("^[a-zA-Z0-9.!#$%&'*+\\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$")

// NormalizeString ...
func NormalizeString(val string) string {
	//nolint
	tran := transform.Chain(norm.NFD, transform.RemoveFunc(isMn), norm.NFC)
	normStr, _, _ := transform.String(tran, strings.ToLower(val))
	normStr = formmatVNCode(normStr)
	normStr = strings.Replace(normStr, " ", "-", -1)
	normStr = strings.Replace(normStr, ",", "", -1)
	normStr = strings.Replace(normStr, ";", "", -1)
	normStr = strings.Replace(normStr, "(", "", -1)
	normStr = strings.Replace(normStr, ")", "", -1)
	normStr = strings.Replace(normStr, "/", "", -1)
	normStr = strings.Replace(normStr, "&", "", -1)
	normStr = strings.Replace(normStr, "%", "", -1)

	// Remove - at the end and start of string
	m := regexp.MustCompile("^-+|-+$")
	normStr = m.ReplaceAllString(normStr, "")

	r, _ := regexp.Compile(`(\\W)`)
	qCheck := make(map[string]int)
	for i, v := range r.FindAllString(normStr, -1) {
		if v == "-" || qCheck[v] > 0 {
			continue
		}
		qCheck[v] = i + 1
		normStr = strings.ReplaceAll(normStr, v, ``)
	}

	return strings.Replace(normStr, "--", "-", -1)
}

func isMn(r rune) bool {
	return unicode.Is(unicode.Mn, r)
}

func formmatVNCode(str string) string {
	for key, val := range mapVNICode {
		m := regexp.MustCompile(val)
		str = m.ReplaceAllString(str, key)
	}
	return str
}

func IsContainsString(arr []string, key string) bool {

	if len(arr) == 0 {
		return false
	}

	for _, s := range arr {
		if key == s {
			return true
		}
	}

	return false
}

func IsEmailValid(e string) bool {
	if len(e) < 3 && len(e) > 254 {
		return false
	}
	return emailRegex.MatchString(e)
}

func CompareVersion(v1, v2 string) int {
	arr1 := strings.Split(v1, ".")
	arr2 := strings.Split(v2, ".")

	len1 := len(arr1)
	len2 := len(arr2)

	for i := 0; i < len1; i++ {
		a1 := arr1[i]

		if strings.Contains(a1, "-") {
			a1 = strings.Split(a1, "-")[0]
		}

		val1, err := strconv.Atoi(a1)

		if err != nil {
			return 0
		}

		var a2 string

		if len2 > i {
			a2 = arr2[i]

		} else {
			a2 = "0"
		}

		if strings.Contains(a2, "-") {
			a2 = strings.Split(a2, "-")[0]
		}

		val2, err := strconv.Atoi(a2)

		if err != nil {
			return 0
		}

		if val1 > val2 {
			return 1
		} else if val1 < val2 {
			return -1
		} else if i == len1-1 && len2 > len1 {
			return -1
		} else {
			continue
		}
	}

	return 0
}
