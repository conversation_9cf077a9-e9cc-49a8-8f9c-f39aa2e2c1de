package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/buymed.th/marketplace/promotion/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type GamificationLeaderBoard struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	CreatedBy       int64              `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	UpdatedBy       int64              `json:"updatedBy,omitempty" bson:"updated_by,omitempty"`

	GamificationCode string `json:"gamificationCode,omitempty" bson:"gamification_code,omitempty"`
	GamificationID   int64  `json:"gamificationID,omitempty" bson:"gamification_id,omitempty"`
	LuckyWheelCode   string `json:"luckyWheelCode,omitempty" bson:"lucky_wheel_code,omitempty"`

	CustomerID    int64  `json:"customerId,omitempty" bson:"customer_id,omitempty"`
	AccountID     int64  `json:"accountId,omitempty" bson:"account_id,omitempty"`
	CustomerPhone string `json:"customerPhone,omitempty" bson:"customer_phone,omitempty"`

	Point                    int `json:"point,omitempty" bson:"point,omitempty"`
	NumberOfMissionCompleted int `json:"numberOfMissionCompleted,omitempty" bson:"number_of_mission_completed,omitempty"`

	// for query
	GamificationDetailIDs []int64                            `json:"-" bson:"-"`
	ComplexQuery          []*bson.M                          `json:"-" bson:"$and,omitempty"`
	MissionStatus         enum.GamificationResultStatusValue `json:"missionStatus" bson:"-"`
	LeaderboardStatus     string                             `json:"leaderboardStatus" bson:"-"` // COMPLETED, INCOMPLETED
}

var GamificationLeaderBoardDB = &db.Instance{
	ColName:        "gamification_leaderboard",
	TemplateObject: &GamificationLeaderBoard{},
}

func InitGamificationLeaderBoardModel(s *mongo.Database) {
	GamificationLeaderBoardDB.ApplyDatabase(s)
}
