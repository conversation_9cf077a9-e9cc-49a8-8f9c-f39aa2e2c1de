package api

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/buymed.th/marketplace/promotion/action"
	"gitlab.com/buymed.th/marketplace/promotion/model"
)

func GetVoucherGroupTypeList(req sdk.APIRequest, res sdk.APIResponder) error {
	query, _ := parseParamQ(req.GetParam("q"))

	query.Offset = int64(sdk.ParseInt(req.GetParam("offset"), 0))
	query.Limit = int64(sdk.ParseInt(req.GetParam("limit"), 20))
	query.GetTotal = req.GetParam("getTotal") == "true"
	query.SearchText = req.GetParam("search")

	return res.Respond(action.GetVoucherGroupTypeList(query))
}

func GetVoucherGroupType(req sdk.APIRequest, res sdk.APIResponder) error {
	query, err := parseParamQ(req.GetParam("q"))
	if err != nil {
		return err
	}
	return res.Respond(action.GetVoucherGroupTypeByID(query.TypeID))
}

func CreateVoucherGroupType(req sdk.APIRequest, res sdk.APIResponder) error {
	var body model.VoucherGroupType
	err := req.GetContent(&body)

	if err != nil {
		return err
	}

	return res.Respond(action.CreateVoucherGroupType(&body))
}

func UpdateVoucherGroupType(req sdk.APIRequest, res sdk.APIResponder) error {
	content := model.VoucherGroupType{}

	err := req.GetContent(&content)

	if err != nil {
		return err
	}

	return res.Respond(action.UpdateVoucherGroupType(&content))
}

// parseParamQ will parse json string format q to model.
// Return query modal and error. If can not parse, query will be empty struct.
func parseParamQ(q string) (*model.VoucherGroupTypeQuery, error) {
	query := model.VoucherGroupTypeQuery{}

	if q == "" {
		return &query, &common.Error{
			Message: "Param q should not empty !",
		}
	}

	err := json.Unmarshal([]byte(q), &query)

	return &query, err
}
