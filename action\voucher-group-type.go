package action

import (
	"fmt"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/buymed.th/marketplace/promotion/helper"
	"gitlab.com/buymed.th/marketplace/promotion/model"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// GetVoucherGroupTypeByID get voucher group type by id
func GetVoucherGroupTypeByID(typeID int64) *common.APIResponse {
	if typeID == 0 {
		return lackTypeIDResponse()
	}
	query := model.VoucherGroupTypeQuery{
		TypeID: typeID,
	}
	return model.VoucherGroupTypeDB.QueryOne(&query)
}

func lackTypeIDResponse() *common.APIResponse {
	return &common.APIResponse{
		Status:    common.APIStatus.Invalid,
		Message:   "Id của nhóm khuyến mãi không được trống và cần đúng định dạng !.",
		ErrorCode: "VOUCHER_ID_REQUIRED",
	}
}

// GetVoucherGroupTypeList get voucher group type list
func GetVoucherGroupTypeList(
	query *model.VoucherGroupTypeQuery,
) *common.APIResponse {
	query.AddStartTimeQueryIfHave()
	query.AddEndTimeQueryIfHave()
	query.AddTextSearchQueryIfHave()

	result := model.VoucherGroupTypeDB.Query(
		query,
		query.Offset,
		query.Limit,
		&primitive.M{"_id": -1})

	if query.GetTotal {
		countResult := model.VoucherGroupTypeDB.Count(query)
		result.Total = countResult.Total
	}
	return result
}

// UpdateVoucherGroupType do update a voucher type by its TypeID
func UpdateVoucherGroupType(
	content *model.VoucherGroupType,
) *common.APIResponse {
	if content.TypeID == 0 {
		return lackTypeIDResponse()
	}

	query := &model.VoucherGroupTypeQuery{
		TypeID: content.TypeID,
	}

	result := model.VoucherGroupTypeDB.QueryOne(query)

	if result.Status != common.APIStatus.Ok {
		return result
	}

	slugIsExisted, response, slug := isSlugExistedInDB(content)

	if slugIsExisted {
		return response
	} else if slug != nil {
		content.Slug = slug
	}

	userInputCodeIsExisted, response := isInputCodeExistedInDB(content)

	if userInputCodeIsExisted || response != nil {
		return response
	}

	return model.VoucherGroupTypeDB.UpdateOne(query, content)
}

// CreateVoucherGroupType create a new voucher type
func CreateVoucherGroupType(
	content *model.VoucherGroupType,
) *common.APIResponse {
	content.GenVoucherGroupTypeID()
	err := content.FormatContentString()

	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "INVALID_INPUT_DATA",
		}
	}

	slugIsExisted, response, slug := isSlugExistedInDB(content)

	if slugIsExisted {
		return response
	} else if slug != nil {
		content.Slug = slug
	}

	userInputCodeIsExisted, response := isInputCodeExistedInDB(content)

	if userInputCodeIsExisted || response != nil {
		return response
	}

	content.HashTag = generateHashTag(content)

	return model.VoucherGroupTypeDB.Create(content)
}

func generateHashTag(content *model.VoucherGroupType) string {
	return fmt.Sprintf(
		"%d-%s-%s", content.TypeID,
		helper.NormalizeString(*content.Name),
		helper.NormalizeString(*content.Code),
	)
}

// isSlugExistedInDB convert name to slug and check if slug is existed in db.
func isSlugExistedInDB(content *model.VoucherGroupType) (bool, *common.APIResponse, *string) {
	if content.Name == nil {
		return false, nil, nil
	}

	slug := helper.NormalizeString(*content.Name)

	// check if slug is existed in DB with other type_id.
	query := &model.VoucherGroupTypeQuery{
		Slug:              &slug,
		ComplexAndQueries: []*bson.M{{"type_id": bson.M{"$ne": content.TypeID}}},
	}

	response := model.VoucherGroupTypeDB.QueryOne(query)

	if response.Status == common.APIStatus.Ok {
		return true, &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Tên nhóm khuyến mãi đã tồn tại !",
			ErrorCode: "VOUCHER_GROUP_TYPE_NAME_EXISTED",
		}, nil
	}

	return false, nil, &slug
}

// isInputCodeExistedInDB check if user input code is existed in db.
func isInputCodeExistedInDB(content *model.VoucherGroupType) (bool, *common.APIResponse) {
	if content.Code == nil {
		return false, nil
	}

	if content.Code != nil && *content.Code == "" {
		return false, &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã của nhóm khuyến mãi không được để trống !",
			ErrorCode: "VOUCHER_GROUP_TYPE_CODE_REQUIRED",
		}
	}

	// check if user input code is existed in DB with other type_id.
	query := &model.VoucherGroupTypeQuery{
		Code:              content.Code,
		ComplexAndQueries: []*bson.M{{"type_id": bson.M{"$ne": content.TypeID}}},
	}

	response := model.VoucherGroupTypeDB.QueryOne(query)

	if response.Status == common.APIStatus.Ok {
		return true, &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã của nhóm khuyến mãi đã tồn tại !",
			ErrorCode: "VOUCHER_GROUP_TYPE_INPUT_CODE_EXISTED",
		}
	}

	return false, nil
}
