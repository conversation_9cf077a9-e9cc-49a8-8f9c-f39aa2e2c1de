package action

import (
	"gitlab.com/buymed.th/marketplace/promotion/model"
	"gitlab.com/buymed.th/marketplace/promotion/model/enum"
	"gitlab.com/buymed.th/marketplace/promotion/utils"
	"testing"
	"time"
)

func TestRun(t *testing.T) {
	type args struct {
		in result
	}
	tests := []struct {
		name  string
		args  args
		want  bool
		want1 string
	}{
		{
			name: "#1 ",
			args: args{
				in: result{
					cart:           &model.Cart{Price: 2000000},
					cartItemMap:    nil,
					orderSummation: nil,
					promoCondition: map[enum.ConditionTypeValue]model.PromotionType{
						enum.ConditionType.ORDER_VALUE: {
							OrConditions: []model.PromotionCondition{
								{
									OrderConditionField: model.OrderConditionField{
										MinTotalPrice: utils.ParseIntToPointer(1000000),
									},
								},
							},
						},
					},
					customer: &model.Customer{OrderCount: 10, LastOrderTime: utils.ParseTimeToPointer(time.Date(2022, 06, 01, 7, 0, 0, 0, time.Local))},
					message:  "",
					isValid:  false,
					operator: "AND",
				},
			},
			want:  false,
			want1: "Gi<PERSON> trị đơn hàng chưa đủ để áp dụng mã khuyến mãi!",
		},
		{
			name: "#2 ",
			args: args{
				in: result{
					cart: &model.Cart{Price: 1000000, CartItems: []model.CartItemInternal{
						{
							Quantity:    10,
							Total:       1000,
							SellerCode:  "medx",
							ProductID:   1,
							ProductCode: "code",
						},
					}},
					cartItemMap:    nil,
					orderSummation: nil,
					promoCondition: map[enum.ConditionTypeValue]model.PromotionType{
						enum.ConditionType.ORDER_VALUE: {
							OrConditions: []model.PromotionCondition{
								{
									OrderConditionField: model.OrderConditionField{
										MinTotalPrice: utils.ParseIntToPointer(30000000),
									},
								},
								{
									OrderConditionField: model.OrderConditionField{
										MinTotalPrice: utils.ParseIntToPointer(30300),
									},
								},
							},
							AndConditions: []model.PromotionCondition{
								//{
								//	OrderConditionField: model.OrderConditionField{
								//		MinTotalPrice: 20000000,
								//	},
								//},
								{
									OrderConditionField: model.OrderConditionField{
										MinTotalPrice: utils.ParseIntToPointer(300000),
									},
								},
							},
						},
						enum.ConditionType.CUSTOMER_HISTORY: {
							AndConditions: []model.PromotionCondition{
								{
									CustomerConditionField: model.CustomerConditionField{
										MinOrderCount: utils.ParseIntToPointer(5),
										MaxOrderCount: utils.ParseIntToPointer(20),
									},
								},
							},
						},
						enum.ConditionType.CUSTOMER_HISTORY: {
							AndConditions: []model.PromotionCondition{
								{
									CustomerConditionField: model.CustomerConditionField{
										MinOrderCount: utils.ParseIntToPointer(5),
										MaxOrderCount: utils.ParseIntToPointer(20),
									},
								},
							},
						},
						enum.ConditionType.PRODUCT: {
							AndConditions: []model.PromotionCondition{
								{
									ProductConditionField: model.ProductConditionField{
										ProductID:     1,
										ProductCode:   "code",
										SellerCode:    nil,
										MinQuantity:   utils.ParseIntToPointer(2),
										MinTotalPrice: nil,
									},
								},
							},
						},
					},
					customer: &model.Customer{OrderCount: 10, LastOrderTime: utils.ParseTimeToPointer(time.Date(2022, 06, 01, 7, 0, 0, 0, time.Local))},
					message:  "",
					isValid:  false,
					operator: "AND",
				},
			},
			want:  true,
			want1: "",
		},
		{
			name: "#3 ",
			args: args{
				in: result{
					cart: &model.Cart{Price: 1000000, CartItems: []model.CartItemInternal{
						{
							Quantity:    10,
							Total:       100000,
							SellerCode:  "medx",
							ProductID:   1,
							ProductCode: "code",
						},
					}},
					cartItemMap:    nil,
					orderSummation: nil,
					promoCondition: map[enum.ConditionTypeValue]model.PromotionType{
						enum.ConditionType.ORDER_VALUE: {
							OrConditions: []model.PromotionCondition{
								{
									OrderConditionField: model.OrderConditionField{
										MinTotalPrice: utils.ParseIntToPointer(30000000),
									},
								},
								{
									OrderConditionField: model.OrderConditionField{
										MinTotalPrice: utils.ParseIntToPointer(33330300),
									},
								},
							},
							AndConditions: []model.PromotionCondition{
								//{
								//	OrderConditionField: model.OrderConditionField{
								//		MinTotalPrice: 20000000,
								//	},
								//},
								{
									OrderConditionField: model.OrderConditionField{
										MinTotalPrice: utils.ParseIntToPointer(300000),
									},
								},
							},
						},
						enum.ConditionType.CUSTOMER_HISTORY: {
							AndConditions: []model.PromotionCondition{
								{
									CustomerConditionField: model.CustomerConditionField{
										MinOrderCount: utils.ParseIntToPointer(5),
										MaxOrderCount: utils.ParseIntToPointer(20),
									},
								},
							},
						},
						enum.ConditionType.CUSTOMER_HISTORY: {
							AndConditions: []model.PromotionCondition{
								{
									CustomerConditionField: model.CustomerConditionField{
										MinOrderCount: utils.ParseIntToPointer(5),
										MaxOrderCount: utils.ParseIntToPointer(20),
									},
								},
							},
						},
						enum.ConditionType.PRODUCT: {
							AndConditions: []model.PromotionCondition{
								{
									ProductConditionField: model.ProductConditionField{
										ProductID:     1,
										ProductCode:   "code",
										SellerCode:    nil,
										MinQuantity:   utils.ParseIntToPointer(2),
										MinTotalPrice: nil,
									},
								},
							},
						},
					},
					customer: &model.Customer{OrderCount: 10, LastOrderTime: utils.ParseTimeToPointer(time.Date(2022, 06, 01, 7, 0, 0, 0, time.Local))},
					message:  "",
					isValid:  false,
					operator: "OR",
				},
			},
			want:  true,
			want1: "",
		},
		{
			name: "#4 product tag",
			args: args{
				in: result{
					cart: &model.Cart{Price: 1000000, CartItems: []model.CartItemInternal{
						{
							Quantity:    10,
							Total:       100000,
							SellerCode:  "medx-a",
							ProductID:   1,
							ProductCode: "code",
							ProductTags: []string{"HDN"},
						},
					}},
					cartItemMap:    nil,
					orderSummation: nil,
					promoCondition: map[enum.ConditionTypeValue]model.PromotionType{
						//enum.ConditionType.ORDER_VALUE: {
						//	OrConditions: []model.PromotionCondition{
						//		{
						//			OrderConditionField: model.OrderConditionField{
						//				MinTotalPrice: utils.ParseIntToPointer(30000),
						//			},
						//		},
						//		{
						//			OrderConditionField: model.OrderConditionField{
						//				MinTotalPrice: utils.ParseIntToPointer(33330300),
						//			},
						//		},
						//	},
						//	AndConditions: []model.PromotionCondition{
						//		//{
						//		//	OrderConditionField: model.OrderConditionField{
						//		//		MinTotalPrice: 20000000,
						//		//	},
						//		//},
						//		{
						//			OrderConditionField: model.OrderConditionField{
						//				MinTotalPrice: utils.ParseIntToPointer(30000),
						//			},
						//		},
						//	},
						//},
						//enum.ConditionType.CUSTOMER_HISTORY: {
						//	AndConditions: []model.PromotionCondition{
						//		{
						//			CustomerConditionField: model.CustomerConditionField{
						//				MinOrderCount: utils.ParseIntToPointer(5),
						//				MaxOrderCount: utils.ParseIntToPointer(20),
						//			},
						//		},
						//	},
						//},
						//enum.ConditionType.CUSTOMER_HISTORY: {
						//	AndConditions: []model.PromotionCondition{
						//		{
						//			CustomerConditionField: model.CustomerConditionField{
						//				MinOrderCount: utils.ParseIntToPointer(5),
						//				MaxOrderCount: utils.ParseIntToPointer(20),
						//			},
						//		},
						//	},
						//},
						//enum.ConditionType.PRODUCT: {
						//	AndConditions: []model.PromotionCondition{
						//		{
						//			ProductConditionField: model.ProductConditionField{
						//				ProductID:     1,
						//				ProductCode:   "code",
						//				SellerCode:    nil,
						//				MinQuantity:   utils.ParseIntToPointer(2),
						//				MinTotalPrice: nil,
						//			},
						//		},
						//	},
						//},
						enum.ConditionType.PRODUCT_TAG: {
							AndConditions: []model.PromotionCondition{
								{
									ProductTagConditionField: model.ProductTagConditionField{
										TagCode: "HDN",
										//SellerCode:    utils.ParseStringToPointer("medx-a"),
										MinQuantity:   nil,
										MinTotalPrice: nil,
									},
								},
							},
						},
					},
					customer: &model.Customer{OrderCount: 10, LastOrderTime: utils.ParseTimeToPointer(time.Date(2022, 06, 01, 7, 0, 0, 0, time.Local))},
					message:  "",
					isValid:  false,
					operator: "AND",
				},
			},
			want:  true,
			want1: "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := Run(tt.args.in)
			if got != tt.want {
				t.Errorf("Run() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("Run() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}
