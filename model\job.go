package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

var SyncGamificationResultJobExecutor = &job.Executor{ColName: "sync_gamification_result"}

func InitSyncGamificationJob(database *mongo.Database, consumer job.ExecutionFn) {
	SyncGamificationResultJobExecutor.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		WaitForReadyTime: true,
		FailThreshold:    10,
	})

	SyncGamificationResultJobExecutor.SetConsumer(consumer)
	if SyncGamificationResultJobExecutor.GetJobDB().Count(bson.M{}).Total > 0 {

	} else {
		now := time.Now()
		lastDay := time.Date(now.Year(), now.Month(), now.Day(), 15, 0, 0, 0, now.Location())
		SyncGamificationResultJobExecutor.Push(nil, &job.JobItemMetadata{
			Topic:     "default",
			ReadyTime: &lastDay,
		})
	}
}

var CheckProductFulfillmentJobExecutor = &job.Executor{ColName: "check_campaign_product_fulfillment"}

func InitCheckProductFulfillmentJob(database *mongo.Database, consumer job.ExecutionFn) {
	CheckProductFulfillmentJobExecutor.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		WaitForReadyTime: true,
		FailThreshold:    10,
	})

	CheckProductFulfillmentJobExecutor.SetConsumer(consumer)
	if CheckProductFulfillmentJobExecutor.GetJobDB().Count(bson.M{}).Total > 0 {

	} else {
		now := time.Now()
		lastDay := time.Date(now.Year(), now.Month(), now.Day(), 17, 0, 0, 0, now.Location())
		CheckProductFulfillmentJobExecutor.Push(nil, &job.JobItemMetadata{
			Topic:     "default",
			ReadyTime: &lastDay,
		})
	}
}

var CampaignProductImportJob = &job.Executor{ColName: "campaign_product_import_job"}

func InitImportCampaignProductJob(database *mongo.Database, consumer job.ExecutionFn) {
	CampaignProductImportJob.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		SortedItem:       false,
		WaitForReadyTime: false,
		FailThreshold:    1,
		ChannelCount:     1,
	})
	CampaignProductImportJob.SetConsumer(consumer)
}

var SyncGamificationJob = &job.Executor{ColName: "sync_single_gamification_job"}

func InitSyncGamificationSingleJob(database *mongo.Database, consumer job.ExecutionFn) {
	SyncGamificationJob.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		SortedItem:       false,
		WaitForReadyTime: true,
		FailThreshold:    1,
		ChannelCount:     1,
	})
	SyncGamificationJob.SetConsumer(consumer)
}

// new import campaign ticket job executor
var UpdateCampaignTicketeStatusJob = &job.Executor{
	ColName: "update_campaign_ticket_status_job",
}

// init import campaign ticket job
func InitUpdateCampaignTicketStatusJob(database *mongo.Database, consumer job.ExecutionFn) {
	UpdateCampaignTicketeStatusJob.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		SortedItem:          false,
		WaitForReadyTime:    false,
		FailThreshold:       10,
		MaximumWaitToRetryS: 10,
		ChannelCount:        1,
	})

	UpdateCampaignTicketeStatusJob.SetConsumer(consumer)
}

var ImportCampaignTicketJob = &job.Executor{
	ColName: "import_campaign_ticket_job",
}

func InitImportCampaignTicketJob(database *mongo.Database, consumer job.ExecutionFn) {
	ImportCampaignTicketJob.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		SortedItem:          false,
		WaitForReadyTime:    false,
		FailThreshold:       10,
		MaximumWaitToRetryS: 10,
		ChannelCount:        5,
	})

	ImportCampaignTicketJob.SetConsumer(consumer)
}

var RewardGenJob = &job.Executor{ColName: "gamification_reward_gen_job"}

func InitRewardGenJob(database *mongo.Database, consumer job.ExecutionFn) {
	RewardGenJob.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		SortedItem:       false,
		WaitForReadyTime: false,
		FailThreshold:    1,
		ChannelCount:     1,
	})
	RewardGenJob.SetConsumer(consumer)
}

var UserVoucherCreationJob = &job.Executor{ColName: "user_voucher_creation_job"}

func InitUserVoucherCreationJob(database *mongo.Database, consumer job.ExecutionFn) {
	UserVoucherCreationJob.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		SortedItem:       false,
		WaitForReadyTime: false,
		FailThreshold:    1,
		ChannelCount:     1,
	})
	UserVoucherCreationJob.SetConsumer(consumer)
}

var RewardVoucherUpdationJob = &job.Executor{ColName: "reward_voucher_updation_job"}

func InitRewardVoucherUpdationJob(database *mongo.Database, consumer job.ExecutionFn) {
	RewardVoucherUpdationJob.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		SortedItem:       false,
		WaitForReadyTime: false,
		FailThreshold:    1,
		ChannelCount:     1,
	})
	RewardVoucherUpdationJob.SetConsumer(consumer)
}

var ScoreGamificationJob = &job.Executor{ColName: "score_gamification_job"}

func InitScoreGamificationJob(database *mongo.Database) {
	ScoreGamificationJob.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		SortedItem:       false,
		WaitForReadyTime: true,
		FailThreshold:    1,
		ChannelCount:     1,
	})
}

var ReScoreGamificationJob = &job.Executor{ColName: "re_score_gamification_job"}

func InitReScoreGamificationJob(database *mongo.Database) {
	ReScoreGamificationJob.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		SortedItem:       false,
		WaitForReadyTime: false,
		ChannelCount:     1,
		UniqueItem:       true,
	})
}
