package client

import (
	"encoding/json"
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"go.mongodb.org/mongo-driver/mongo"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.com/buymed.th/marketplace/promotion/model"
)

type productClient struct {
	svc     *client.RestClient
	headers map[string]string
}

func NewProductClient(apiHost, apiKey, logName string, s *mongo.Database) *productClient {
	if apiHost == "" || apiKey == "" {
		return nil
	}

	client := &productClient{
		svc: client.NewRESTClient(
			apiHost,
			"product_client",
			time.Duration(10*time.Second),
			1,
			time.Duration(10*time.Second),
		),
		headers: map[string]string{
			"Authorization": apiKey,
		},
	}
	// client.svc.SetDBLog(s)

	return client
}

func (cli *productClient) UpdateSku(input *model.UpdateSkuCampaignRequest) error {
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, nil, input, "/marketplace/product/v2/sku/campaign", nil)
	if err != nil {
		return err
	}
	var result *model.BaseAPIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return err
	}
	return nil
}

func (cli *productClient) GetSku(sku string) (*model.Sku, error) {
	query := map[string]string{
		"code": sku,
	}
	queryString, _ := json.Marshal(query)

	params := map[string]string{
		"q": string(queryString),
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, "/marketplace/product/v2/sku", nil)
	if err != nil {
		return nil, err
	}
	var result *model.SkuResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}

	return result.Data[0], nil
}

func (cli *productClient) GetProduct(code string) (*model.Product, error) {
	query := map[string]string{
		"code": code,
	}
	queryString, _ := json.Marshal(query)

	params := map[string]string{
		"q": string(queryString),
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, "/marketplace/product/v2/product", nil)
	if err != nil {
		return nil, err
	}
	var result *model.ProductResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}

	return result.Data[0], nil
}

func (cli *productClient) GetSkuMain(sku string) (*model.SkuMain, error) {
	query := map[string]string{
		"code": sku,
	}
	queryString, _ := json.Marshal(query)

	params := map[string]string{
		"q": string(queryString),
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, "/marketplace/product/v2/sku-main/list", nil)
	if err != nil {
		return nil, err
	}
	var result *model.SkuMainResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}

	return result.Data[0], nil
}

func (cli *productClient) GetPriceRange() ([]*model.ProductSearchRangePriceConfig, error) {
	params := map[string]string{}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, "/search/config/range-price/list", nil)
	if err != nil {
		return nil, err
	}
	var result *model.ProductSearchRangePriceResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}

	return result.Data, nil
}

func (cli *productClient) WarmupProductCache() error {
	body := map[string]interface{}{
		"SyncCampaign": true,
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, nil, body, "/marketplace/product/v2/flag", nil)
	if err != nil {
		return err
	}
	var result *model.BaseAPIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return err
	}
	return nil
}
