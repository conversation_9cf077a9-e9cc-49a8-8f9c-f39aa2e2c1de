package utils

import (
	"os"
	"regexp"
	"strings"
	"time"
)

// ParseIntToPointer is func convert int to *int
func ParseIntToPointer(val int) *int {
	return &val
}

// ParseInt64ToPointer is func convert int to *int
func ParseInt64ToPointer(val int64) *int64 {
	return &val
}

// ParseFloat64ToPointer is func convert int to *int
func ParseFloat64ToPointer(val float64) *float64 {
	return &val
}

// ParseStringToPointer is func convert string to *string
func ParseStringToPointer(val string) *string {
	return &val
}

// ParseBoolToPointer is func convert bool to *bool
func ParseBoolToPointer(val bool) *bool {
	return &val
}

// ParseTimeToPointer is func convert time.Time to *time.Time
func ParseTimeToPointer(val time.Time) *time.Time {
	return &val
}

// PointerToInt ...
func PointerToInt(val *int) int {
	return *val
}

// GetHostName ...
func GetHostName() string {
	name, err := os.Hostname()
	if err != nil {
		return "undefined"
	}
	return name
}

// ParserQ ...
func ParserQ(q string) string {
	q = strings.Replace(NormalizeString(q), " ", "-", -1)
	r, _ := regexp.Compile(`(\\W)`)
	qCheck := make(map[string]int)
	for i, v := range r.FindAllString(q, -1) {
		if v == "-" || qCheck[v] > 0 {
			continue
		}
		qCheck[v] = i + 1
		q = strings.ReplaceAll(q, v, `\`+v)
	}
	return q
}
