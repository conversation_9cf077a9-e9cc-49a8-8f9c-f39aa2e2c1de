package action

import (
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/buymed.th/marketplace/promotion/client"
	"gitlab.com/buymed.th/marketplace/promotion/model"
	_ "gitlab.com/buymed.th/marketplace/promotion/model"
	"gitlab.com/buymed.th/marketplace/promotion/model/enum"
	"gitlab.com/buymed.th/marketplace/promotion/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type errRes struct {
	ErrorMessage  string                        `json:"errorMessage,omitempty"`
	ErrorCode     enum.VoucherErrorCodeType     `json:"errorCode,omitempty"`
	VoucherCode   string                        `json:"voucherCode,omitempty" bson:"-"`
	Discount      float64                       `json:"discountValue,omitempty"`
	Gifts         []model.Gift                  `json:"gifts,omitempty"`
	CanUse        bool                          `json:"canUse,omitempty"`
	AutoApply     bool                          `json:"autoApply,omitempty"`
	MatchSeller   string                        `json:"matchSeller,omitempty"`
	MatchProducts []model.ProductConditionField `json:"matchProducts,omitempty"`
	GroupCode     string                        `json:"groupCode,omitempty"`
	ApplySkus     []string                      `json:"applySkus,omitempty"`
	NotApplySkus  []string                      `json:"notApplySkus,omitempty"`
	Priority      *int                          `json:"-"`
	DiscountInfos []model.DiscountInfo          `json:"discountInfos,omitempty"`
	SellerCode    string                        `json:"sellerCode,omitempty"`
	SellerCodes   []string                      `json:"sellerCodes,omitempty"`
}

var blackListViewVoucherDiscountMap = map[int64]bool{}

func PromotionUpdate(acc *model.Account, update *model.Promotion) *common.APIResponse {
	if update.PromotionID == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing id",
			ErrorCode: "MISSING_ID",
		}
	}
	query := model.Promotion{
		PromotionID: update.PromotionID,
	}
	qPromotion := model.PromotionDB.QueryOne(query)
	if qPromotion.Status != common.APIStatus.Ok {
		return qPromotion
	}
	promotion := qPromotion.Data.([]*model.Promotion)[0]
	if update.PromotionName == "" {
		update.PromotionName = promotion.PromotionName
	}
	if update.Description == "" {
		update.Description = promotion.Description
	}
	update.UpdatedBy = acc.AccountID
	normNameStr := strings.Replace(utils.NormalizeString(update.PromotionName), " ", "-", -1)
	normDesStr := strings.Replace(utils.NormalizeString(update.Description), " ", "-", -1)
	update.HashTag = fmt.Sprintf("%d-%s-%s", update.PromotionID, normNameStr, normDesStr)
	res := model.PromotionDB.UpdateOne(query, update)
	return res
}

func PromotionCreate(acc *model.Account, promotion *model.Promotion) *common.APIResponse {
	promotion.CreatedBy = acc.AccountID
	normNameStr := strings.Replace(utils.NormalizeString(promotion.PromotionName), " ", "-", -1)
	normDescriptionStr := strings.Replace(utils.NormalizeString(promotion.Description), " ", "-", -1)
	promotion.HashTag = fmt.Sprintf("%d-%s-%s", promotion.PromotionID, normNameStr, normDescriptionStr)
	promotion.PromotionID = model.GenId("PROMOTION_ID")
	res := model.PromotionDB.Create(promotion)
	return res
}

func VoucherCheckWithCart(accountID int64, voucherCodes []string, cart *model.Cart, getVoucherAutoApply bool) *common.APIResponse {
	customer, err := client.Services.Customer.GetCustomerByAccountID(accountID) // get customer info
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "GET_CUSTOMER_ERROR",
		}
	}
	if cart != nil {
		cart.CustomerLevel = utils.ParseStringToPointer(customer.Level)
		if cart.ProvinceCode == "" { // set province code
			cart.ProvinceCode = customer.ProvinceCode
		}
		if cart.CustomerScope == "" {
			cart.CustomerScope = customer.Scope
		}
	}

	var setting *model.Setting
	if qSetting := model.SettingDB.QueryOne(model.Setting{}); qSetting.Status == common.APIStatus.Ok {
		setting = qSetting.Data.([]*model.Setting)[0]
	}

	mapVoucher := make(map[string]*model.Voucher)
	// voucherAutoApply := 1
	conditions := []*bson.M{
		{
			"code": bson.M{"$in": voucherCodes},
		},
	}
	if getVoucherAutoApply && !cart.ValidateOrder {
		conditions = append(conditions, &bson.M{
			"apply_type": enum.ApplyType.AUTO,
		})
		// if qSetting := model.SettingDB.QueryOne(model.Setting{}); qSetting.Status == common.APIStatus.Ok {
		// 	voucherAutoApply = qSetting.Data.([]*model.Setting)[0].NumberOfVoucherAuto
		// }
	} else if !cart.ValidateOrder {
		conditions = []*bson.M{
			{
				"$and": []bson.M{
					{
						"code": bson.M{"$in": voucherCodes},
					},
					{
						"apply_type": bson.M{"$ne": enum.ApplyType.AUTO},
					},
				},
			},
		}
	}
	qVoucher := model.VoucherDB.Query(model.Voucher{ComplexQuery: []*bson.M{{"$or": conditions}}}, 0, 0, &primitive.M{"priority": -1})
	if qVoucher.Status == common.APIStatus.Ok {
		voucherCodes = make([]string, 0)
		for _, voucher := range qVoucher.Data.([]*model.Voucher) {
			if mapVoucher[voucher.Code] == nil {
				voucherCodes = append(voucherCodes, voucher.Code)
			}
			mapVoucher[voucher.Code] = voucher
		}
	}

	queryUsed := model.UserPromotion{
		CustomerID: customer.CustomerID,
		ComplexQuery: []*bson.M{
			{
				"voucher_code": bson.M{"$in": voucherCodes},
			},
		},
	}
	qUsed := model.UserPromotionCacheDB.Query(queryUsed, 0, 0, nil)
	mapUsed := make(map[string]*model.UserPromotion)
	if qUsed.Status == common.APIStatus.Ok {
		for _, used := range qUsed.Data.([]*model.UserPromotion) {
			mapUsed[used.VoucherCode] = used
		}
	}
	dataResponse := make([]*errRes, 0)
	// countVoucherAutoApply := 0
	// isSkipNextVoucher := false
	mapGiftManual := make(map[string]*model.Gift, 0)
	mapProductManual := make(map[string]*bool, 0)
	for _, voucherCode := range voucherCodes {
		value := isValidVoucher(voucherCode, mapVoucher[voucherCode], mapUsed[voucherCode], cart, customer, setting)
		value.CanUse = true
		if value.ErrorMessage != "" {
			value.CanUse = false
		}
		if !value.AutoApply && value.CanUse {
			for _, gift := range value.Gifts {
				mapGiftManual[gift.Sku] = &gift
			}
			for _, product := range value.MatchProducts {
				if product.SellerCode != nil {
					mapProductManual[fmt.Sprintf("%s.%s", *product.SellerCode, product.ProductCode)] = utils.ParseBoolToPointer(true)
				}
			}
		}
		if value.AutoApply && !value.CanUse {
			continue
		}
		dataResponse = append(dataResponse, &value)
	}
	isSkipVoucherAutoConflictGift := func(gifts []model.Gift, mapGiftManual map[string]*model.Gift) bool {
		for _, gift := range gifts {
			if data, ok := mapGiftManual[gift.Sku]; ok && data != nil {
				return true
			}
		}
		return false
	}
	isSkipVoucherAutoConflictProduct := func(products []model.ProductConditionField, mapProductManual map[string]*bool) bool {
		for _, product := range products {
			if product.SellerCode != nil {
				if data, ok := mapProductManual[fmt.Sprintf("%s.%s", *product.SellerCode, product.ProductCode)]; ok && data != nil && *data {
					return true
				}
			}
		}
		return false
	}
	newDataResponse := make([]*errRes, 0)
	for _, data := range dataResponse {
		if data.AutoApply && (isSkipVoucherAutoConflictGift(data.Gifts, mapGiftManual) || isSkipVoucherAutoConflictProduct(data.MatchProducts, mapProductManual)) {
			continue
		}
		newDataResponse = append(newDataResponse, data)
	}
	newDataResponse = checkVoucherGroupMapInUse(newDataResponse, setting)
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Data:    newDataResponse,
		Message: "Voucher check success",
	}
}

func checkVoucherGroupMapInUse(inUses []*errRes, setting *model.Setting) []*errRes {
	if setting == nil {
		return inUses
	}

	voucherGroupConnectionCases := setting.VoucherGroupConnectionCases

	inUses = removeInvalidAutoApplyVouchersFromOrigin(inUses, voucherGroupConnectionCases, setting)

	updatedInUses, nonUses := checkVoucherGroupMapInUseFromSetting(
		inUses,
		voucherGroupConnectionCases,
	)
	//canNotUseMap := make(map[string]bool)
	//for _, nonUse := range nonUses {
	//	canNotUseMap[nonUse.VoucherCode] = true
	//}
	//inUseGroupMap := make(map[string]*errRes)
	//for _, inUse := range inUses {
	//	if _, ok := canNotUseMap[inUse.VoucherCode]; ok {
	//		continue
	//	}
	//	inUseGroupMap[inUse.GroupCode+"."+inUse.SellerCode] = inUse
	//	inUseGroupMap["."+inUse.SellerCode] = inUse
	//}
	for _, nonUse := range nonUses {
		nonUse.CanUse = false
		nonUse.ErrorMessage = "This voucher is not available at the moment"
		nonUse.ErrorCode = enum.VoucherErrorCode.VOUCHER_IS_NOT_AVAILABLE
		//if vConflict, ok := inUseGroupMap[nonUse.GroupCode+"."+nonUse.SellerCode]; ok {
		//	nonUse.ErrorMessage = "Không dùng chung được với mã " + vConflict.VoucherCode
		//} else {
		//	nonUse.ErrorMessage = "Mã giảm giá này hiện không hoạt động"
		//}
	}

	return updatedInUses
}

// checkVoucherGroupMapInUseFromSetting validates inUses of voucher by voucherGroupConnectionCases,
// return updated InUses and nonUses.
// A nonUse voucher in this list is pointed to the same at the one in inUses.
func checkVoucherGroupMapInUseFromSetting(inUses []*errRes, voucherGroupConnectionCases map[string]bool) (_ []*errRes, nonUses []*errRes) {
	if voucherGroupConnectionCases == nil {
		voucherGroupConnectionCases = make(map[string]bool)
	}

	mapGroupVoucherInUse := make(map[string]int)
	keyFormat := model.VGC_CASE_FORMAT
	for _, inUse := range inUses {
		mapGroupVoucherInUse[inUse.GroupCode+"."+inUse.SellerCode] = mapGroupVoucherInUse[inUse.GroupCode+"."+inUse.SellerCode] + 1
	}

	for k, v := range mapGroupVoucherInUse {
		sellerCode := strings.Split(k, ".")[1]
		k = strings.Split(k, ".")[0]
		for _, nonUse := range inUses {
			if !nonUse.CanUse {
				continue
			}
			if nonUse.SellerCode != sellerCode {
				continue
			}
			key := fmt.Sprintf(keyFormat, k, v, nonUse.GroupCode, 1)
			key2 := fmt.Sprintf(keyFormat, nonUse.GroupCode, 1, k, v)
			if nonUse.GroupCode == k {
				if v-1 == 0 {
					continue
				}
				key = fmt.Sprintf(keyFormat, k, v-1, nonUse.GroupCode, 1)
				key2 = fmt.Sprintf(keyFormat, nonUse.GroupCode, 1, k, v-1)
			}

			if !(voucherGroupConnectionCases[key] || voucherGroupConnectionCases[key2]) {
				nonUses = append(nonUses, nonUse)
			}
		}
	}

	return inUses, nonUses
}

func removeInvalidAutoApplyVouchersFromOrigin(inUses []*errRes, voucherGroupConnectionCases map[string]bool, setting *model.Setting) []*errRes {

	autoVoucherInUses := getAutoVoucherInUses(inUses)

	sortedAutoVouchers := sortAutoVouchersByPriorityDesc(autoVoucherInUses)

	checkedAutoVouchers := make([]*errRes, 0)
	numVouchersOfGroupMap := make(map[string]int)

	for _, inUse := range sortedAutoVouchers {
		if isVoucherFitInCheckedList(
			voucherGroupConnectionCases,
			inUse,
			checkedAutoVouchers,
			numVouchersOfGroupMap,
		) {
			checkedAutoVouchers = append(checkedAutoVouchers, inUse)
			numVouchersOfGroupMap[inUse.GroupCode+"."+inUse.SellerCode]++
		}
	}

	validAutoVoucherMap := makeAutoVoucherMap(checkedAutoVouchers, setting)

	updatedInUses := make([]*errRes, 0)
	for _, inUse := range inUses {
		if !inUse.AutoApply {
			updatedInUses = append(updatedInUses, inUse)
			continue
		}

		if validAutoVoucherMap[inUse.VoucherCode] {
			updatedInUses = append(updatedInUses, inUse)
		}
	}

	return updatedInUses
}

func getAutoVoucherInUses(inUses []*errRes) []*errRes {
	autoVoucherInUses := make([]*errRes, 0)
	for _, inUse := range inUses {
		if inUse.AutoApply {
			autoVoucherInUses = append(autoVoucherInUses, inUse)
		}
	}
	return autoVoucherInUses
}

func sortAutoVouchersByPriorityDesc(autoVoucherInUses []*errRes) []*errRes {
	sort.Slice(autoVoucherInUses, func(i, j int) bool {
		if autoVoucherInUses[i].Priority == nil {
			return false
		}
		if autoVoucherInUses[j].Priority == nil {
			return true
		}
		return *autoVoucherInUses[i].Priority > *autoVoucherInUses[j].Priority
	})
	return autoVoucherInUses
}

func isVoucherFitInCheckedList(
	connectionCases map[string]bool,
	checking *errRes,
	checkedAutoVouchers []*errRes,
	numVouchersOfGroupMap map[string]int,
) bool {
	for _, voucher := range checkedAutoVouchers {
		if checking.SellerCode != voucher.SellerCode {
			continue
		}
		// 2 different groups, num vouchers equal to existing vouchers + 1.
		checkingCount := numVouchersOfGroupMap[checking.GroupCode+"."+checking.SellerCode] + 1

		// Same group, checking 1 with ammount of existing vouchers.
		if checking.GroupCode+"."+checking.SellerCode == voucher.GroupCode+"."+checking.SellerCode {
			checkingCount = 1
		}
		key := fmt.Sprintf(
			model.VGC_CASE_FORMAT,
			checking.GroupCode, checkingCount,
			voucher.GroupCode, numVouchersOfGroupMap[voucher.GroupCode+"."+checking.SellerCode],
		)
		key2 := fmt.Sprintf(
			model.VGC_CASE_FORMAT,
			voucher.GroupCode, numVouchersOfGroupMap[voucher.GroupCode+"."+checking.SellerCode],
			checking.GroupCode, checkingCount,
		)

		if !(connectionCases[key] || connectionCases[key2]) {
			return false
		}

	}

	return true
}

func makeAutoVoucherMap(autoVouchers []*errRes, setting *model.Setting) map[string]bool {
	validAutoVoucherMap := make(map[string]bool)
	nAuto := 1
	nAutoSeller := 0
	if setting != nil {
		nAuto = setting.NumberOfVoucherAuto
		nAutoSeller = setting.NumberOfVoucherAutoPerSeller
	}
	cAuto := 0
	cAutoSeller := 0
	for _, voucher := range autoVouchers {
		if voucher.SellerCode == "" && cAuto >= nAuto {
			continue
		}
		if voucher.SellerCode != "" && cAutoSeller >= nAutoSeller {
			continue
		}
		if voucher.AutoApply && voucher.CanUse {
			if voucher.SellerCode == "" {
				cAuto++
			} else {
				cAutoSeller++
			}
		}
		validAutoVoucherMap[voucher.VoucherCode] = true
	}
	return validAutoVoucherMap
}

func checkVoucherGroupMap(inUses, nonUses []*model.VoucherViewWebOnly, systemDisplay string, cart *model.Cart, setting *model.Setting) ([]*model.VoucherViewWebOnly, []*model.VoucherViewWebOnly) {
	numberOfVoucherManual := 0
	voucherGroupConnectionCases := make(map[string]bool)
	if setting != nil {
		numberOfVoucherManual = setting.NumberOfVoucherManual
		voucherGroupConnectionCases = setting.VoucherGroupConnectionCases
	}
	updatedInUses, updatedNonUses := checkVoucherGroupMapFromSetting(
		inUses,
		nonUses,
		voucherGroupConnectionCases,
		numberOfVoucherManual,
		cart,
	)

	return updatedInUses, updatedNonUses
}

func checkVoucherGroupMapFromSetting(inUses, nonUses []*model.VoucherViewWebOnly, voucherGroupConnectionCases map[string]bool, numberOfVoucherManual int, cart *model.Cart) ([]*model.VoucherViewWebOnly, []*model.VoucherViewWebOnly) {
	if voucherGroupConnectionCases == nil {
		voucherGroupConnectionCases = make(map[string]bool)
	}
	inUseAuto := make([]*model.VoucherViewWebOnly, 0)
	inUseManual := make([]*model.VoucherViewWebOnly, 0)
	inUseGroupMap := make(map[string]*model.VoucherViewWebOnly)
	if numberOfVoucherManual <= 1 {
		for _, v := range inUses {
			inUseGroupMap[v.GroupCode+"."+v.SellerCode] = v
			if v.ApplyType == enum.ApplyType.AUTO {
				inUseAuto = append(inUseAuto, v)
			} else {
				inUseManual = append(inUseManual, v)
			}
		}
		inUses = inUseAuto
	}

	mapGroupVoucherInUse := make(map[string]int)
	keyFormat := model.VGC_CASE_FORMAT
	for _, inUse := range inUses {
		mapGroupVoucherInUse[inUse.GroupCode+"."+inUse.SellerCode] = mapGroupVoucherInUse[inUse.GroupCode+"."+inUse.SellerCode] + 1
		inUseGroupMap[inUse.GroupCode+"."+inUse.SellerCode] = inUse
	}

	voucherGroupQuantityPairsContainNonUse := make(map[string]int)
	for _, nonUse := range nonUses {
		voucherGroupQuantityPairsContainNonUse[nonUse.GroupCode+"."+nonUse.SellerCode] = mapGroupVoucherInUse[nonUse.GroupCode+"."+nonUse.SellerCode] + 1
	}

	for k, v := range mapGroupVoucherInUse {
		sellerCode := strings.Split(k, ".")[1]
		k = strings.Split(k, ".")[0]
		for _, nonUse := range inUses {
			if !nonUse.CanUse {
				continue
			}
			if nonUse.SellerCode != sellerCode {
				continue
			}
			key := fmt.Sprintf(keyFormat, k, v, nonUse.GroupCode, 1)
			key2 := fmt.Sprintf(keyFormat, nonUse.GroupCode, 1, k, v)
			if nonUse.GroupCode == k {
				if v-1 == 0 {
					continue
				}
				key = fmt.Sprintf(keyFormat, k, v-1, nonUse.GroupCode, 1)
				key2 = fmt.Sprintf(keyFormat, nonUse.GroupCode, 1, k, v-1)
			}

			if !(voucherGroupConnectionCases[key] || voucherGroupConnectionCases[key2]) {
				nonUse.CanUse = false
				if vConflict, ok := inUseGroupMap[k+"."+sellerCode]; ok {
					nonUse.ErrorMessage = "Cannot apply this voucher with " + vConflict.Code
					nonUse.ErrorCode = "CANNOT_APPLY_WITH_OTHERS"
				} else {
					nonUse.ErrorMessage = "This voucher is not available at the moment"
				}
				if cart.IsCartEmpty {
					nonUse.ErrorMessage = ""
				}
				nonUse.ActionStatus = "DISABLED"
			}
		}
		for _, nonUse := range nonUses {
			if !nonUse.CanUse {
				continue
			}
			if nonUse.SellerCode != sellerCode {
				continue
			}
			curNonUseQnt := 1
			//if k != nonUse.GroupCode && nonUse.GroupCode != "" {
			if k != nonUse.GroupCode {
				curNonUseQnt = voucherGroupQuantityPairsContainNonUse[nonUse.GroupCode+"."+nonUse.SellerCode]
			}

			key := fmt.Sprintf(keyFormat, k, v, nonUse.GroupCode, curNonUseQnt)
			key2 := fmt.Sprintf(keyFormat, nonUse.GroupCode, curNonUseQnt, k, v)

			if !(voucherGroupConnectionCases[key] || voucherGroupConnectionCases[key2]) {
				nonUse.CanUse = false
				if vConflict, ok := inUseGroupMap[k+"."+sellerCode]; ok {
					nonUse.ErrorMessage = "Cannot apply this voucher with " + vConflict.Code
					nonUse.ErrorCode = "CANNOT_APPLY_WITH_OTHERS"
				} else {
					nonUse.ErrorMessage = "This voucher is not available at the moment"
				}
				if cart.IsCartEmpty {
					nonUse.ErrorMessage = ""
				}
				nonUse.ActionStatus = "DISABLED"
			}
		}
	}
	if len(inUseManual) > 0 {
		inUses = append(inUses, inUseManual...)
	}
	return inUses, nonUses
}

func VoucherActiveList(accountID int64, query *model.Voucher, cart *model.Cart, offset, limit int64, getTotal, getValidate bool, scope string) *common.APIResponse {
	var setting *model.Setting
	if qSetting := model.SettingDB.QueryOne(model.Setting{}); qSetting.Status == common.APIStatus.Ok {
		setting = qSetting.Data.([]*model.Setting)[0]
	}

	customer, err := client.Services.Customer.GetCustomerByAccountID(accountID)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "GET_CUSTOMER_ERROR",
		}
	}
	if cart != nil {
		cart.CustomerLevel = utils.ParseStringToPointer(customer.Level)
		if cart.ProvinceCode == "" {
			cart.ProvinceCode = customer.ProvinceCode
		}
		if cart.CustomerScope == "" {
			cart.CustomerScope = customer.Scope
		}
	}
	queryUsed := model.UserPromotion{
		CustomerID: customer.CustomerID,
		ComplexQuery: []*bson.M{
			{
				//"status":         bson.M{"$nin": []string{string(enum.CodeStatus.INACTIVE)}},
				"voucher_status": enum.VoucherStatus.ACTIVE,
			},
			{
				"$or": []bson.M{
					{
						"customer_apply_type": enum.CustomerApplyType.ALL,
					},
					{
						"$and": []bson.M{
							{
								"customer_apply_type": enum.CustomerApplyType.MANY,
							},
							{
								"status": bson.M{"$ne": enum.CodeStatus.DELETED},
							},
						},
					},
				},
			},
		},
	}
	qUsed := model.UserPromotionCacheDB.Query(queryUsed, 0, 0, nil)
	mapUsed := make(map[string]*model.UserPromotion)
	voucherCodes := make([]string, 0)
	inactiveVoucherCodes := make([]string, 0)
	if qUsed.Status == common.APIStatus.Ok {
		for _, used := range qUsed.Data.([]*model.UserPromotion) {
			if used.Status != nil && *used.Status == enum.CodeStatus.INACTIVE {
				inactiveVoucherCodes = append(inactiveVoucherCodes, used.VoucherCode)
			} else {
				voucherCodes = append(voucherCodes, used.VoucherCode)
				mapUsed[used.VoucherCode] = used
			}
		}
	}
	now := time.Now()
	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
		"public_time": bson.M{"$lte": now},
		"end_time":    bson.M{"$gte": now},
		"status":      "ACTIVE",
		"apply_type":  bson.M{"$ne": enum.ApplyType.AUTO},
		"code":        bson.M{"$nin": inactiveVoucherCodes},
	})

	regions := make([]*bson.M, 0)
	regions = append(regions, &bson.M{
		"region_scope_map.ALL": true,
	})
	regions = append(regions, &bson.M{
		"region_scope_map": nil,
	})
	regions = append(regions, &bson.M{
		fmt.Sprintf("region_scope_map.%s", customer.ProvinceCode): true,
	})
	for _, region := range cart.RegionCodes {
		regions = append(regions, &bson.M{
			fmt.Sprintf("region_scope_map.%s", region): true,
		})
	}
	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
		"$or": regions,
	})
	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
		"$or": []*bson.M{
			{
				"level_scope_map.ALL": true,
			},
			{
				"level_scope_map": nil,
			},
			{
				fmt.Sprintf("level_scope_map.%s", customer.Level): true,
			},
		},
	})
	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
		"$or": []*bson.M{
			{
				"customer_scope_map.ALL": true,
			},
			{
				"customer_scope_map": nil,
			},
			{
				fmt.Sprintf("customer_scope_map.%s", customer.Scope): true,
			},
		},
	})
	switch scope {
	case "me":
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"$and": []*bson.M{
				{
					"customer_apply_type": enum.CustomerApplyType.MANY,
					"code":                bson.M{"$in": voucherCodes},
				},
			},
		})
	case "other":
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"$or": []*bson.M{
				{
					"customer_apply_type": enum.CustomerApplyType.ALL,
				},
			},
		})
	default:
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"$or": []*bson.M{
				{
					"customer_apply_type": enum.CustomerApplyType.MANY,
					"code":                bson.M{"$in": voucherCodes},
				},
				{
					"customer_apply_type": enum.CustomerApplyType.ALL,
				},
			},
		})
	}

	mapVoucher := make(map[string]*model.Voucher)
	queryMapVoucher := make(map[string]*model.Voucher)
	voucherDatas := make([]*model.Voucher, 0)
	qVoucher := model.VoucherCacheDB.Query(query, offset, limit, &primitive.M{"end_time": 1})
	if qVoucher.Status == common.APIStatus.Ok {
		for _, voucher := range qVoucher.Data.([]*model.Voucher) {
			mapVoucher[voucher.Code] = voucher
			queryMapVoucher[voucher.Code] = voucher
		}
		voucherDatas = qVoucher.Data.([]*model.Voucher)
	} else {
		return qVoucher
	}
	if qVoucherInUse := model.VoucherCacheDB.Query(bson.M{"code": bson.M{"$in": cart.RedeemCode}}, 0, 0, nil); qVoucherInUse.Status == common.APIStatus.Ok {
		for _, voucher := range qVoucherInUse.Data.([]*model.Voucher) {
			if _, ok := mapVoucher[voucher.Code]; !ok {
				mapVoucher[voucher.Code] = voucher
				voucherDatas = append(voucherDatas, voucher)
			}
		}
	}

	if getTotal {
		qVoucher.Total = model.VoucherCacheDB.Count(query).Total
	}
	dataResponse := make([]*model.VoucherViewWebOnly, 0)
	if getValidate {
		for voucherCode, voucher := range mapVoucher {
			value := isValidVoucher(voucherCode, voucher, mapUsed[voucherCode], cart, customer, setting)
			voucher.CanUse = true
			voucher.ErrorCode = string(value.ErrorCode)
			voucher.ErrorMessage = VoucherError[voucher.ErrorCode]
			if value.ErrorCode != "" {
				voucher.CanUse = false
			}
			voucher.Gifts = value.Gifts
			voucher.Discount = value.Discount
		}
	}

	voucherInUseMap := make(map[string]bool)
	voucherInUses := make([]*model.VoucherViewWebOnly, 0)
	for _, voucher := range cart.RedeemCode {
		voucherInUseMap[voucher] = true
	}

	for _, v := range voucherDatas {
		voucher := mapVoucher[v.Code]
		used := mapUsed[v.Code]
		if voucher == nil {
			continue
		}
		if used != nil && voucher.MaxUsagePerCustomer != nil && *voucher.MaxUsagePerCustomer != 0 && used.Amount != nil && *used.Amount >= *voucher.MaxUsagePerCustomer {
			continue
		}
		// if getVoucherToCollect {
		// 	isSkipVoucherCollect := false
		// 	if voucher.ApplyDiscount != nil && voucher.ApplyDiscount.NotInSkus != nil {
		// 		for _, notInSku := range *voucher.ApplyDiscount.NotInSkus {
		// 			if notInSku == sku {
		// 				isSkipVoucherCollect = true
		// 				break
		// 			}
		// 		}
		// 	}
		// 	if isSkipVoucherCollect {
		// 		continue
		// 	}
		// }
		viewVoucher := setViewData(voucher)
		if used == nil && blackListViewVoucherDiscountMap[customer.CustomerID] && len(viewVoucher.Gifts) == 0 {
			continue
		}
		// if used != nil {
		// 	viewVoucher.IsCollected = used.IsCollected
		// 	if !viewVoucher.IsOwner && viewVoucher.IsCollected != nil && *viewVoucher.IsCollected {
		// 		viewVoucher.CollectStatus = "COLLECTED"
		// 	}
		// }
		//if scope == "collected" {
		//	if viewVoucher.IsCollected == nil || !*viewVoucher.IsCollected {
		//		continue
		//	}
		//}
		//if skipVoucherCollected {
		//	if viewVoucher.IsCollected != nil && *viewVoucher.IsCollected {
		//		continue
		//	}
		//}
		//if skipVoucherOwner {
		//	if viewVoucher.IsOwner {
		//		continue
		//	}
		//}
		if data, ok := voucherInUseMap[v.Code]; data && ok {
			viewVoucher.ActionStatus = "INUSE"
			voucherInUses = append(voucherInUses, viewVoucher)
		} else {
			dataResponse = append(dataResponse, viewVoucher)
		}
		// update data for app old version
		if v.CustomerApplyType == enum.CustomerApplyType.MANY {
			viewVoucher.Voucher.AppliedCustomers = []int64{customer.CustomerID}
		}
		viewVoucher.Voucher.PromotionName = viewVoucher.Description
		viewVoucher.Voucher.StartTime = viewVoucher.StartTime
		viewVoucher.Voucher.EndTime = viewVoucher.EndTime
		viewVoucher.Voucher.Code = viewVoucher.Code
		viewVoucher.Voucher.Promotion.Description = viewVoucher.Description
	}

	voucherInUses, dataResponse = checkVoucherGroupMap(voucherInUses, dataResponse, "", cart, setting)
	sort.Slice(dataResponse, func(i, j int) bool {
		if dataResponse[i].CanUse == dataResponse[j].CanUse {
			if len(dataResponse[i].Gifts) == len(dataResponse[j].Gifts) {
				if dataResponse[i].Discount > dataResponse[j].Discount {
					return true
				} else {
					return false
				}
			} else {
				if len(dataResponse[i].Gifts) > 0 {
					return true
				} else {
					return false
				}
			}

			//if dataResponse[i].EndTime != nil && dataResponse[j].EndTime != nil{
			//	if dataResponse[i].EndTime.Before(*dataResponse[j].EndTime) {
			//		return true
			//	} else {
			//		return false
			//	}
			//}
		}
		if dataResponse[i].CanUse {
			return true
		}
		return false
	})
	dataResponse = append(voucherInUses, dataResponse...)

	if len(cart.RedeemCode) > 0 {
		dataResponse, qVoucher = removeInUsesVouchersThatNotMatchSearch(
			dataResponse, qVoucher, cart.RedeemCode, queryMapVoucher)
	}

	// Auto apply vouchers should not display in the active list because Users can not apply them manually.
	dataResponse, qVoucher = removeAutoApplyVouchers(dataResponse, qVoucher)

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Data:    dataResponse,
		Message: qVoucher.Message,
		Total:   qVoucher.Total,
	}
}

func removeAutoApplyVouchers(
	dataResponse []*model.VoucherViewWebOnly,
	qVoucher *common.APIResponse) ([]*model.VoucherViewWebOnly, *common.APIResponse) {
	updatedDataRes := make([]*model.VoucherViewWebOnly, 0)

	for _, voucher := range dataResponse {
		if voucher.ApplyType == enum.ApplyType.AUTO {
			qVoucher.Total--
			continue
		}

		updatedDataRes = append(updatedDataRes, voucher)
	}

	return updatedDataRes, qVoucher
}

func removeInUsesVouchersThatNotMatchSearch(dataResponse []*model.VoucherViewWebOnly, qVoucher *common.APIResponse, redeemCode []string, queryMapVoucher map[string]*model.Voucher) ([]*model.VoucherViewWebOnly, *common.APIResponse) {
	voucherCodeMaptoViewVoucher := make(map[string]*model.VoucherViewWebOnly)
	for _, viewVoucher := range dataResponse {
		voucherCodeMaptoViewVoucher[viewVoucher.Code] = viewVoucher
	}

	for _, voucherCode := range redeemCode {
		if queryMapVoucher[voucherCode] == nil {
			delete(voucherCodeMaptoViewVoucher, voucherCode)
		}
	}

	newDataResponse := make([]*model.VoucherViewWebOnly, 0)
	for _, voucher := range dataResponse {
		if voucherCodeMaptoViewVoucher[voucher.Code] == nil {
			continue
		}
		newDataResponse = append(newDataResponse, voucher)
	}
	qVoucher.Total = int64(len(newDataResponse))

	return newDataResponse, qVoucher
}

func SelfVoucherActiveList(accountID int64, query *model.Voucher, offset, limit int64, getTotal bool, scope string, sourceDetail *model.OrderSourceDetail) *common.APIResponse {
	customer, err := client.Services.Customer.GetCustomerByAccountID(accountID)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "GET_CUSTOMER_ERROR",
		}
	}
	queryUsed := model.UserPromotion{
		CustomerID: customer.CustomerID,
		ComplexQuery: []*bson.M{
			{
				//"status":         bson.M{"$nin": []string{string(enum.CodeStatus.INACTIVE)}},
				"voucher_status": enum.VoucherStatus.ACTIVE,
			},
			{
				"$or": []bson.M{
					{
						"customer_apply_type": enum.CustomerApplyType.ALL,
					},
					{
						"$and": []bson.M{
							{
								"customer_apply_type": enum.CustomerApplyType.MANY,
							},
							{
								"status": bson.M{"$ne": enum.CodeStatus.DELETED},
							},
						},
					},
				},
			},
		},
	}
	qUsed := model.UserPromotionCacheDB.Query(queryUsed, 0, 0, nil)
	mapUsed := make(map[string]*model.UserPromotion)
	voucherCodes := make([]string, 0)
	inactiveVoucherCodes := make([]string, 0)
	if qUsed.Status == common.APIStatus.Ok {
		for _, used := range qUsed.Data.([]*model.UserPromotion) {
			if used.Status != nil && *used.Status == enum.CodeStatus.INACTIVE {
				inactiveVoucherCodes = append(inactiveVoucherCodes, used.VoucherCode)
			} else {
				voucherCodes = append(voucherCodes, used.VoucherCode)
				mapUsed[used.VoucherCode] = used
			}
		}
	}
	now := time.Now()
	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
		"public_time": bson.M{"$lte": now},
		"end_time":    bson.M{"$gte": now},
		"status":      "ACTIVE",
		"apply_type":  bson.M{"$ne": enum.ApplyType.AUTO},
		"code":        bson.M{"$nin": inactiveVoucherCodes},
	})

	regions := make([]*bson.M, 0)
	regions = append(regions, &bson.M{
		"region_scope_map.ALL": true,
	})
	regions = append(regions, &bson.M{
		"region_scope_map": nil,
	})
	regions = append(regions, &bson.M{
		fmt.Sprintf("region_scope_map.%s", customer.ProvinceCode): true,
	})
	regionResp := client.LocationClient.GetRegionList([]string{customer.ProvinceCode})
	if regionResp.Status == common.APIStatus.Ok {
		for _, region := range regionResp.Data {
			regions = append(regions, &bson.M{
				fmt.Sprintf("region_scope_map.%s", region.Code): true,
			})
		}
	}
	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
		"$or": regions,
	})
	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
		"$or": []*bson.M{
			{
				"level_scope_map.ALL": true,
			},
			{
				"level_scope_map": nil,
			},
			{
				fmt.Sprintf("level_scope_map.%s", customer.Level): true,
			},
		},
	})
	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
		"$or": []*bson.M{
			{
				"customer_scope_map.ALL": true,
			},
			{
				"customer_scope_map": nil,
			},
			{
				fmt.Sprintf("customer_scope_map.%s", customer.Scope): true,
			},
		},
	})
	switch scope {
	case "me":
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"$and": []*bson.M{
				{
					"customer_apply_type": enum.CustomerApplyType.MANY,
					"code":                bson.M{"$in": voucherCodes},
				},
			},
		})
	case "other":
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"$or": []*bson.M{
				{
					"customer_apply_type": enum.CustomerApplyType.ALL,
				},
			},
		})
	default:
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"$or": []*bson.M{
				{
					"customer_apply_type": enum.CustomerApplyType.MANY,
					"code":                bson.M{"$in": voucherCodes},
				},
				{
					"customer_apply_type": enum.CustomerApplyType.ALL,
				},
			},
		})
	}

	mapVoucher := make(map[string]*model.Voucher)
	qVoucher := model.VoucherCacheDB.Query(query, offset, limit, &primitive.M{"end_time": 1})
	if qVoucher.Status == common.APIStatus.Ok {
		for _, voucher := range qVoucher.Data.([]*model.Voucher) {
			mapVoucher[voucher.Code] = voucher
		}
	} else {
		return qVoucher
	}

	if getTotal {
		qVoucher.Total = model.VoucherCacheDB.Count(query).Total
	}
	dataResponse := make([]*model.VoucherViewWebOnly, 0)

	for _, v := range qVoucher.Data.([]*model.Voucher) {
		voucher := mapVoucher[v.Code]
		used := mapUsed[v.Code]
		if used != nil && voucher.MaxUsagePerCustomer != nil && *voucher.MaxUsagePerCustomer != 0 && used.Amount != nil && *used.Amount >= *voucher.MaxUsagePerCustomer {
			continue
		}
		viewVoucher := setViewData(voucher)
		//if used == nil && blackListViewVoucherDiscountMap[customer.CustomerID] && len(viewVoucher.Gifts) == 0 {
		//	continue
		//}
		dataResponse = append(dataResponse, viewVoucher)
		// update data for app old version
		if v.CustomerApplyType == enum.CustomerApplyType.MANY {
			viewVoucher.Voucher.AppliedCustomers = []int64{customer.CustomerID}
		}
		viewVoucher.Voucher.PromotionName = viewVoucher.Description
		viewVoucher.Voucher.StartTime = viewVoucher.StartTime
		viewVoucher.Voucher.EndTime = viewVoucher.EndTime
		viewVoucher.Voucher.Code = viewVoucher.Code
		viewVoucher.Voucher.Promotion.Description = viewVoucher.Description
	}
	sort.Slice(dataResponse, func(i, j int) bool {
		if dataResponse[i].CanUse == dataResponse[j].CanUse {
			if len(dataResponse[i].Gifts) == len(dataResponse[j].Gifts) {
				if dataResponse[i].Discount > dataResponse[j].Discount {
					return true
				} else {
					return false
				}
			} else {
				if len(dataResponse[i].Gifts) > 0 {
					return true
				} else {
					return false
				}
			}
		}
		if dataResponse[i].CanUse {
			return true
		}
		return false
	})
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Data:    dataResponse,
		Message: qVoucher.Message,
		Total:   qVoucher.Total,
	}
}

// * PRIVATE FUNC //
func isValidVoucherScope(cart model.Cart, scopes []model.Scope) (isValid bool, errCode enum.VoucherErrorCodeType, msg string) {
	cart.RegionCodes = append(cart.RegionCodes, cart.ProvinceCode)
	isCheckArea := cart.RegionCodes != nil && len(cart.RegionCodes) > 0
	for _, scope := range scopes {
		isValid = false
		if scope.Type == nil || (scope.QuantityType != nil && *scope.QuantityType == enum.QuantityType.ALL) {
			isValid, errCode, msg = checkAppVersion(&cart, scope.MinClientVersion)
			if !isValid {
				return
			}
			isValid = true
			continue
		}
		switch *scope.Type {
		case enum.ScopeType.AREA:
			if isCheckArea {
				mapAreaCode := make(map[string]bool)
				for _, v := range scope.AreaCodes {
					mapAreaCode[v] = true
				}
				for _, v := range cart.RegionCodes {
					if data, ok := mapAreaCode[v]; ok && data {
						isValid = true
					}
				}
				if isValid {
					continue
				}
				isValid = false
				msg = "NOT_SUPPORT_REGION"
				return
			}
		case enum.ScopeType.CUSTOMER_LEVEL:
			for _, v := range scope.CustomerLevelCodes {
				if cart.CustomerLevel != nil && v == *cart.CustomerLevel {
					isValid = true
				}
			}
			if isValid {
				continue
			}
			isValid = false
			msg = "NOT_SUPPORT_LEVEL"
			return
		case enum.ScopeType.CUSTOMER_SCOPE:
			for _, v := range scope.CustomerScopes {
				if v == cart.CustomerScope {
					isValid = true
				}
			}
			if isValid {
				continue
			}
			isValid = false
			msg = "NOT_SUPPORT_SCOPE"
			return
			//  TODO: check chỗ min client version
		case enum.ScopeType.DISPLAY_PLATFORM:
			isValid, errCode, msg = validateVoucherByPlatforms(cart, scope.DisplayPlatforms, scope.MinClientVersion)
			return
		default:
			isValid = true
			continue
		}
	}
	return
}

func checkAppVersion(cart *model.Cart, minVersion *string) (bool, enum.VoucherErrorCodeType, string) {
	if cart.SourceDetail != nil && cart.SourceDetail.Platform == string(enum.Platform.MOBILE_APP) {
		if minVersion != nil && *minVersion != "" && cart.SourceDetail.BrowserVersion != "" &&
			utils.CompareVersion(cart.SourceDetail.BrowserVersion, *minVersion) < 0 {
			return false, enum.VoucherErrorCode.INVALID_APP_VERSION, "Please update your app to the latest version"
		}
	}
	return true, "", ""
}

func validateVoucherByPlatforms(cart model.Cart, displayPlatforms []enum.PlatformType, minVersion *string) (bool, enum.VoucherErrorCodeType, string) {
	if cart.SourceDetail == nil || cart.SourceDetail.Platform == "" {
		return true, "", ""
	}

	isValid, errCodeDefault, msg := false, enum.VoucherErrorCode.INVALID_APP_VERSION, "Applicable to other devices according to conditions"

	if len(displayPlatforms) == 0 {
		isMobileValid, errCode, mobileMsg := checkAppVersion(&cart, minVersion)

		if !isMobileValid {
			return isMobileValid, errCode, mobileMsg
		}

		return true, "", ""
	} else {
		for _, p := range displayPlatforms {
			if p == enum.Platform.MOBILE_APP {
				isMobileValid, errCode, mobileMsg := checkAppVersion(&cart, minVersion)
				if !isMobileValid {
					return isMobileValid, errCode, mobileMsg
				}
			}

			if p == enum.PlatformType(cart.SourceDetail.Platform) {
				return true, "", ""
			}
		}
	}

	return isValid, errCodeDefault, msg
}

func isValidCondition(in result) (int, bool, enum.VoucherErrorCodeType, string) {
	customerCondition := &customerCondition{}
	productCondition := &productCondition{}
	orderCondition := &orderCondition{}
	productTagCondition := &productTagCondition{}

	productTagCondition.setNext(customerCondition)
	productCondition.setNext(productTagCondition)
	orderCondition.setNext(productCondition)
	orderCondition.execute(&in)
	return in.rewardCount, in.isValid, in.errorCode, in.message
}

func isValidVoucherBase(voucher *model.Voucher, used *model.UserPromotion) (bool, enum.VoucherErrorCodeType, string) {
	now := time.Now()
	if voucher.Status != nil && *voucher.Status != enum.VoucherStatus.ACTIVE {
		return false, enum.VoucherErrorCode.VOUCHER_NOT_ACTIVE, "INACTIVE"
	}
	if voucher.StartTime != nil && voucher.StartTime.After(now) {
		return false, enum.VoucherErrorCode.VOUCHER_NOT_STARTED, "NOT_START_YET"
	}
	if voucher.EndTime != nil && voucher.EndTime.Before(now) {
		return false, enum.VoucherErrorCode.VOUCHER_EXPIRED, "EXPIRED"
	}

	if voucher.CustomerApplyType == enum.CustomerApplyType.MANY && (used == nil || *used.Status == enum.CodeStatus.DELETED) {
		return false, enum.VoucherErrorCode.VOUCHER_INVALID_USER, "NOT_ENOUGH_CONDITION"
	}
	if voucher.UsageTotal != nil && voucher.MaxUsage != nil && *voucher.MaxUsage != 0 && *voucher.UsageTotal >= *voucher.MaxUsage {
		return false, enum.VoucherErrorCode.VOUCHER_OUT_OF_STOCK, "OUT_OF_STOCK"
	}

	if used != nil && *used.Status == enum.CodeStatus.INACTIVE {
		return false, enum.VoucherErrorCode.VOUCHER_INVALID_USER, "CAN_NOT_USE"
	}

	if used != nil && voucher.MaxUsagePerCustomer != nil && *voucher.MaxUsagePerCustomer != 0 && used.Amount != nil && *used.Amount >= *voucher.MaxUsagePerCustomer {
		return false, enum.VoucherErrorCode.VOUCHER_USED, "OUT_OF_USE"
	}
	return true, "", ""
}

func setDiscountOnEachSku(discount float64, voucher *model.Voucher, cart *model.Cart) {
	totalDiscount := float64(0)
	mapNotApplySku := make(map[string]bool)
	if voucher.ApplyDiscount != nil && voucher.ApplyDiscount.NotInSkus != nil && len(*voucher.ApplyDiscount.NotInSkus) != 0 {
		for _, sku := range *voucher.ApplyDiscount.NotInSkus {
			mapNotApplySku[sku] = true
		}
	}
	if voucher.ApplyDiscount != nil && voucher.ApplyDiscount.Skus != nil && len(*voucher.ApplyDiscount.Skus) != 0 {
		if cart.SkuValue == nil {
			makeMapItem(cart, cart.CartItems)
		}
		skuMap := make(map[string]bool)
		for _, sku := range *voucher.ApplyDiscount.Skus {
			skuMap[sku] = true
		}

		for _, item := range cart.CartItems {
			if _, ok := skuMap[item.ProductSKU]; ok {
				discountInfo := model.DiscountInfo{
					Sku:      item.ProductSKU,
					IsApply:  true,
					Discount: float64(int(float64(item.Total) / float64(voucher.PriceToCalDiscount) * float64(discount))),
					SellerCodes: func() []string {
						if voucher.SellerCodes != nil {
							return *voucher.SellerCodes
						}
						return []string{}
					}(),
				}
				voucher.DiscountInfos = append(voucher.DiscountInfos, discountInfo)
				totalDiscount += float64(discountInfo.Discount)
			}
		}

	} else if voucher.PromotionOrganizer != nil && (*voucher.PromotionOrganizer == enum.PromotionOrganizer.INTERNAL_SELLER) {
		if voucher.SellerCodes != nil && len(*voucher.SellerCodes) != 0 {
			for _, seller := range *voucher.SellerCodes {
				for _, item := range cart.CartItems {
					if _, ok := mapNotApplySku[item.ProductSKU]; ok {
						continue
					}
					if item.SellerCode == seller {
						discountInfo := model.DiscountInfo{
							Sku:      item.ProductSKU,
							IsApply:  true,
							Discount: float64(int(float64(item.Total) / float64(voucher.PriceToCalDiscount) * float64(discount))),
							SellerCodes: func() []string {
								if voucher.SellerCodes != nil {
									return *voucher.SellerCodes
								}
								return []string{}
							}(),
						}
						voucher.DiscountInfos = append(voucher.DiscountInfos, discountInfo)
						totalDiscount += float64(discountInfo.Discount)
					}
				}
			}
		}
	} else {
		for _, item := range cart.CartItems {
			if _, ok := mapNotApplySku[item.ProductSKU]; ok {
				continue
			}
			discountInfo := model.DiscountInfo{
				Sku:      item.ProductSKU,
				IsApply:  true,
				Discount: float64(float64(item.Total) / float64(voucher.PriceToCalDiscount) * float64(discount)),
				SellerCodes: func() []string {
					if voucher.SellerCodes != nil {
						return *voucher.SellerCodes
					}
					return []string{}
				}(),
			}
			voucher.DiscountInfos = append(voucher.DiscountInfos, discountInfo)
			totalDiscount += float64(discountInfo.Discount)
		}
	}
	if len(voucher.DiscountInfos) > 0 {
		//if totalDiscount != discount {
		lastItemDiscount := float64(voucher.DiscountInfos[len(voucher.DiscountInfos)-1].Discount)
		voucher.DiscountInfos[len(voucher.DiscountInfos)-1].Discount = lastItemDiscount + (discount - totalDiscount)
		//}
	}
}

func isValidVoucher(voucherCode string, voucher *model.Voucher, used *model.UserPromotion, cart *model.Cart, customer *model.Customer, setting *model.Setting) errRes {
	rewardCount, _, errCode, message := validateVoucher(voucher, used, cart, customer, setting)
	discount, gifts, sellerCode := getReward(voucher, cart, rewardCount)
	if discount > 0 {
		setDiscountOnEachSku(discount, voucher, cart)
	} else if len(gifts) == 0 {
		errCode, message = enum.VoucherErrorCode.INVALID_APPLY_DISCOUNT, "Mua thêm sản phẩm theo điều kiện"
	}
	products := make([]model.ProductConditionField, 0)
	if len(gifts) > 0 {
		products = getProductConditions(voucher)
	}
	return errRes{
		ErrorMessage: message,
		ErrorCode:    errCode,
		VoucherCode:  voucherCode,
		Discount:     discount,
		Gifts:        gifts,
		GroupCode: func() string {
			if voucher != nil && voucher.VoucherGroupCode != nil {
				return *voucher.VoucherGroupCode
			}
			return ""
		}(),
		AutoApply:     voucher != nil && voucher.ApplyType != "" && voucher.ApplyType == enum.ApplyType.AUTO,
		MatchSeller:   sellerCode,
		MatchProducts: products,
		ApplySkus: func() []string {
			if voucher.ApplyDiscount != nil && voucher.ApplyDiscount.Skus != nil {
				return *voucher.ApplyDiscount.Skus
			}
			return []string{}
		}(),
		NotApplySkus: func() []string {
			if voucher.ApplyDiscount != nil && voucher.ApplyDiscount.NotInSkus != nil {
				return *voucher.ApplyDiscount.NotInSkus
			}
			return []string{}
		}(),
		Priority: voucher.Priority,
		// DiscountInfos: voucher.DiscountInfos,
		SellerCode: getVoucherSellerCode(voucher),
		SellerCodes: func() []string {
			if voucher.SellerCodes != nil && len(*voucher.SellerCodes) > 0 {
				return *voucher.SellerCodes
			}
			return []string{}
		}(),
	}
}

func getVoucherSellerCode(voucher *model.Voucher) string {
	return "BUYMED"
	// if voucher.PromotionOrganizer != nil && *voucher.PromotionOrganizer == enum.PromotionOrganizer.INTERNAL_SELLER {
	// 	return "INTERNAL_SELLER"
	// }
	// if voucher.SellerCodes != nil && len(*voucher.SellerCodes) > 0 {
	// 	sellerCode := (*voucher.SellerCodes)[0]
	// 	return sellerCode
	// }
	// if voucher.SellerCode != nil {
	// 	return *voucher.SellerCode
	// }
	// return ""
}

func isValidNumberVoucher(voucher *model.Voucher, setting *model.Setting, cart *model.Cart) (bool, enum.VoucherErrorCodeType, string) {
	if voucher.ApplyType == enum.ApplyType.AUTO {
		return true, "", ""
	}
	mapNumberVoucher := make(map[string]int)  // key = SELLER_CODE _ APPLY_TYPE
	mapVoucherInCart := make(map[string]bool) // key = voucherCode
	if cart.NumberOfVoucherMap != nil {
		mapNumberVoucher = cart.NumberOfVoucherMap
		mapVoucherInCart = cart.VoucherInCartMap
	} else {
		for _, item := range cart.RedeemApplyResult {
			if item.AutoApply {
				continue
			}
			applyType := string(enum.ApplyType.MANUAL)
			if item.AutoApply {
				applyType = string(enum.ApplyType.AUTO)
			}
			mapNumberVoucher[item.SellerCode+"_"+applyType] += 1
			mapVoucherInCart[item.Code] = true
		}
		cart.NumberOfVoucherMap = mapNumberVoucher
		cart.VoucherInCartMap = mapVoucherInCart
	}
	if voucher.ApplyType == "" {
		voucher.ApplyType = enum.ApplyType.MANUAL
	}
	sellerCode := getVoucherSellerCode(voucher)
	if n, ok := mapNumberVoucher[sellerCode+"_"+string(voucher.ApplyType)]; ok {
		numberToCheck := setting.NumberOfVoucherAuto
		if sellerCode != "" {
			numberToCheck = setting.NumberOfVoucherManualPerSeller
			if voucher.ApplyType == enum.ApplyType.AUTO {
				numberToCheck = setting.NumberOfVoucherAutoPerSeller
			}
		} else if voucher.ApplyType == enum.ApplyType.MANUAL {
			numberToCheck = setting.NumberOfVoucherManual
		}
		if _, ok := mapVoucherInCart[voucher.Code]; ok {
			n--
		}
		if n >= numberToCheck {
			messageError := "Only apply maximum " + strconv.Itoa(numberToCheck) + " vouchers from Buymed"
			// if sellerCode != "" && voucher.SellerName != nil {
			// 	messageError = "Chỉ áp dụng được tối đa " + strconv.Itoa(numberToCheck) + " mã giảm giá từ " + *voucher.SellerName
			// }
			return false, enum.VoucherErrorCode.INVALID_NUMBER_APPLY, messageError
		}
	}
	return true, "", ""
}

func validateVoucher(voucher *model.Voucher, used *model.UserPromotion, cart *model.Cart, customer *model.Customer, setting *model.Setting) (int, bool, enum.VoucherErrorCodeType, string) {
	if voucher == nil {
		return 0, false, enum.VoucherErrorCode.VOUCHER_NOT_FOUND, "NOT_FOUND"
	}

	//if used == nil {
	//	qUsedVoucher := model.UserPromotionDB.QueryOne(&model.UserPromotion{VoucherCode: voucher.Code, CustomerID: customer.CustomerID})
	//	if qUsedVoucher.Status == common.APIStatus.Ok {
	//		used = qUsedVoucher.Data.([]*model.UserPromotion)[0]
	//	}
	//}
	if isValid, errorCode, message := isValidVoucherBase(voucher, used); !isValid && !cart.ValidateOrder {
		return 0, isValid, errorCode, message
	}

	if isValid, errorCode, message := isValidVoucherScope(*cart, voucher.Scopes); !isValid && !cart.ValidateOrder {
		return 0, isValid, errorCode, message
	}
	if setting != nil {
		if isValid, errorCode, message := isValidNumberVoucher(voucher, setting, cart); !isValid && !cart.ValidateOrder {
			return 0, isValid, errorCode, message
		}
	}
	rewardCountRes := 0
	if setting != nil && setting.ApplyDiscountVoucher != nil {
		if voucher.ApplyDiscount != nil && voucher.ApplyDiscount.SkuName == nil {
			voucher.ApplyDiscount.SkuName = setting.ApplyDiscountVoucher.SkuName
		}
	}
	// valid condition
	if voucher.AndConditions != nil {
		rewardCount, isValid, errCode, message := isValidCondition(result{
			cart:           cart,
			promoCondition: voucher.AndConditions,
			customer:       customer,
			operator:       "AND",
			applyDiscount:  voucher.ApplyDiscount,
			organization: func() enum.PromotionOrganizerValue {
				if voucher.PromotionOrganizer != nil {
					return *voucher.PromotionOrganizer
				}
				return enum.PromotionOrganizer.MARKETING
			}(),
			sellerCodes: func() []string {
				if voucher.SellerCodes != nil {
					return *voucher.SellerCodes
				}
				return []string{}
			}(),
		})
		rewardCountRes = rewardCount
		if !isValid {
			return rewardCount, isValid, errCode, message
		}
	}

	if voucher.OrConditions != nil {
		rewardCount, isValid, errCode, message := isValidCondition(result{
			cart:           cart,
			promoCondition: voucher.OrConditions,
			customer:       customer,
			operator:       "OR",
			applyDiscount:  voucher.ApplyDiscount,
			organization: func() enum.PromotionOrganizerValue {
				if voucher.PromotionOrganizer != nil {
					return *voucher.PromotionOrganizer
				}
				return enum.PromotionOrganizer.MARKETING
			}(),
			sellerCodes: func() []string {
				if voucher.SellerCodes != nil {
					return *voucher.SellerCodes
				}
				return []string{}
			}(),
		})
		rewardCountRes = rewardCount
		if !isValid {
			return rewardCount, isValid, errCode, message
		}
	}

	return rewardCountRes, true, "", ""
}

func getReward(voucher *model.Voucher, cart *model.Cart, rewardCount int) (float64, []model.Gift, string) {
	if voucher.MaxAutoApplyCount == nil {
		voucher.MaxAutoApplyCount = utils.ParseInt64ToPointer(1)
	}
	if voucher.MaxAutoApplyCount != nil && *voucher.MaxAutoApplyCount < int64(rewardCount) {
		rewardCount = int(*voucher.MaxAutoApplyCount)
	}
	if voucher != nil && len(voucher.Rewards) > 0 {
		reward := voucher.Rewards[0]
		if reward.Type == nil {
			return 0, nil, ""
		}
		switch *reward.Type {
		case enum.RewardType.ABSOLUTE:
			if voucher.ApplyType == enum.ApplyType.AUTO && rewardCount > 1 {
				reward.AbsoluteDiscount = reward.AbsoluteDiscount * float64(rewardCount)
			}
			return reward.AbsoluteDiscount, nil, ""
		case enum.RewardType.GIFT:
			sellerCode := ""
			if len(reward.Gifts) > 0 {
				gift := reward.Gifts[0]
				tmp := strings.Split(gift.Sku, ".")
				sellerCode = tmp[0]
				if sellerCode == "MARKETING" {
					sellerCode = ""
				}
			}
			if voucher.ApplyType == enum.ApplyType.AUTO && rewardCount > 1 {
				newGifts := make([]model.Gift, 0)
				for _, gift := range reward.Gifts {
					newGifts = append(newGifts, model.Gift{
						Sku:      gift.Sku,
						Quantity: gift.Quantity * int64(rewardCount),
					})
				}
				reward.Gifts = newGifts
			}
			return 0, reward.Gifts, sellerCode
		case enum.RewardType.PERCENTAGE:
			if voucher.ApplyType == enum.ApplyType.AUTO && rewardCount > 1 {
				reward.PercentageDiscount = reward.PercentageDiscount * float64(rewardCount)
			}
			discount := (cart.Price * float64(reward.PercentageDiscount)) / 100
			if discount > reward.MaxDiscount {
				discount = reward.MaxDiscount
			}
			return utils.RoundDown(discount), nil, ""
		}
	}
	return 0, nil, ""
}

func getProductConditions(voucher *model.Voucher) []model.ProductConditionField {
	products := make([]model.ProductConditionField, 0)
	if voucher.AndConditions != nil && len(voucher.AndConditions[enum.ConditionType.PRODUCT].AndConditions) > 0 {
		for _, productCondition := range voucher.AndConditions[enum.ConditionType.PRODUCT].AndConditions {
			products = append(products, productCondition.ProductConditionField)
		}
	}
	return products
}

func setViewData(voucher *model.Voucher) *model.VoucherViewWebOnly {
	return &model.VoucherViewWebOnly{
		DisplayName:          voucher.DisplayName,
		VoucherID:            voucher.VoucherID,
		Code:                 voucher.Code,
		StartTime:            voucher.StartTime,
		EndTime:              voucher.EndTime,
		PublicTime:           voucher.PublicTime,
		ConditionDescription: voucher.ConditionDescription,
		CanUse:               voucher.CanUse,
		ErrorMessage:         VoucherError[voucher.ErrorCode],
		ErrorCode:            voucher.ErrorCode,
		Discount:             voucher.Discount,
		MaxUsage:             voucher.MaxUsage,
		UsageTotal:           voucher.UsageTotal,
		Gifts: func() []model.Gift {
			if len(voucher.Gifts) > 0 {
				return voucher.Gifts
			}
			if len(voucher.Rewards) > 0 && *voucher.Rewards[0].Type == enum.RewardType.GIFT {
				return voucher.Rewards[0].Gifts
			}
			return nil
		}(),
		PromotionName: func() string {
			if voucher.DisplayName != "" {
				return voucher.DisplayName
			}
			return voucher.PromotionName
		}(),
		Description: func() string {
			if voucher.DisplayName != "" {
				return voucher.DisplayName
			}
			return voucher.PromotionName
		}(),
		ActionStatus: func() string {
			if voucher.CanUse == true {
				return "AVAILABLE"
			}
			if voucher.ErrorCode == string(enum.VoucherErrorCode.INVALID_NUMBER_APPLY) {
				return "DISABLED"
			}
			return "INVALID"
		}(),
		GroupCode: func() string {
			if voucher.VoucherGroupCode != nil {
				return *voucher.VoucherGroupCode
			}
			return ""
		}(),
		SellerCode: func() string {
			return "BUYMED"
			// if voucher.PromotionOrganizer != nil && *voucher.PromotionOrganizer == enum.PromotionOrganizer.INTERNAL_SELLER {
			// 	return "INTERNAL_SELLER"
			// }
			// if voucher.SellerCodes != nil && len(*voucher.SellerCodes) > 0 {
			// 	sellerCode := (*voucher.SellerCodes)[0]
			// 	return sellerCode
			// }
			// if voucher.SellerCode != nil {
			// 	return *voucher.SellerCode
			// }
			// return ""
		}(),
	}
}
