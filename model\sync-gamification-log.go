package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/buymed.th/marketplace/promotion/model/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type SyncGamificationLog struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	Code                      string                          `json:"code,omitempty" bson:"code,omitempty"`
	GamificationCode          string                          `json:"gamificationCode,omitempty" bson:"gamification_code,omitempty"`
	GamificationID            int64                           `json:"gamificationID,omitempty" bson:"gamification_id,omitempty"`
	StartTime                 *time.Time                      `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime                   *time.Time                      `json:"endTime,omitempty" bson:"end_time,omitempty"`
	OrderFromTime             *time.Time                      `json:"orderFromTime,omitempty" bson:"order_from_time,omitempty"`
	OrderToTime               *time.Time                      `json:"orderToTime,omitempty" bson:"order_to_time,omitempty"`
	Status                    enum.GamificationLogStatusValue `json:"status,omitempty" bson:"status,omitempty"`
	SystemStatus              enum.GamificationLogStatusValue `json:"systemStatus,omitempty" bson:"system_status,omitempty"`
	AccountID                 int64                           `json:"accountID,omitempty" bson:"account_id,omitempty"` // action by
	Type                      enum.SyncGamificationType       `json:"type,omitempty" bson:"type,omitempty"`
	NumberOfJoinedCustomer    int                             `json:"numberOfJoinedCustomer,omitempty" bson:"number_of_joined_customer,omitempty"`
	NumberOfCompletedCustomer int                             `json:"numberOfCompletedCustomer,omitempty" bson:"number_of_completed_customer,omitempty"`
	FailReason                string                          `json:"failReason,omitempty" bson:"fail_reason,omitempty"`
}

// SyncGamificationLogDB is instance db
var SyncGamificationLogDB = &db.Instance{
	ColName:        "sync_gamification_log",
	TemplateObject: &SyncGamificationLog{},
}

// InitSyncGamificationLogModel is func init model level
func InitSyncGamificationLogModel(s *mongo.Database) {
	SyncGamificationLogDB.ApplyDatabase(s)
}
