package client

import (
	"encoding/json"
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/buymed.th/marketplace/promotion/model"

	"go.mongodb.org/mongo-driver/mongo"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
)

const (
	pathCreateNotification = "/integration/notification/v1/notification"
)

var NotificationClient = &notificationClient{}

type notificationClient struct {
	notificationClient *client.RestClient
	headers            map[string]string
}

// NewNotificationServiceClient ...
func NewNotificationServiceClient(apiHost, apiKey string, s *mongo.Database) *notificationClient {
	client := &notificationClient{
		notificationClient: client.NewRESTClient(
			apiHost,
			"notification_client",
			time.Duration(180*time.Second),
			2,
			time.Duration(2*time.Second),
		),
		headers: map[string]string{
			"Authorization": apiKey,
		},
	}
	// client.notificationClient.SetDBLog(s)
	return client
}

// CreateNotification is func create new Notification
func (cli *notificationClient) CreateNotification(in *model.Notification) *common.APIResponse {
	params := map[string]string{}
	if in.Username == "" {
		in.Username = fmt.Sprintf("CUSTOMER_%d", in.UserID)
	}
	res, err := cli.notificationClient.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, in, pathCreateNotification, nil)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_NOTIFICATION",
		}
	}

	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_NOTIFICATION",
		}
	}

	return result
}
