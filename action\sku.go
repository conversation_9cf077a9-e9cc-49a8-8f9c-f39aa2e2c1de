package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/buymed.th/marketplace/promotion/model"
	"gitlab.com/buymed.th/marketplace/promotion/model/enum"
	"go.mongodb.org/mongo-driver/bson"
)

// WarmupSkuSaleInfo warmup 1 document
func WarmupSkuSaleInfo(skuSaleInfo *model.SkuSaleInfo) {
	if skuSaleInfo.CampaignCode == nil && skuSaleInfo.CampaignProductCode == nil && len(skuSaleInfo.SkuCode) == 0 {
		return
	}

	// check data in sku sale info cache

	query := &model.SkuSaleInfo{
		SkuCode:             skuSaleInfo.SkuCode,
		CampaignCode:        skuSaleInfo.CampaignCode,
		CampaignProductCode: skuSaleInfo.CampaignProductCode,
	}

	if skuSaleInfo.IsActive != nil && !*skuSaleInfo.IsActive {
		result := model.SkuSaleInfoCacheDB.QueryOne(query)

		if result.Status != common.APIStatus.Ok {
			return
		}
	}

	locationCodeRaw := getLocationCodeRaw(skuSaleInfo.LocationCodes)
	customerScopeCodeRaw := getCustomerScopeRaw(skuSaleInfo.CustomerScopeCodes)
	skuSaleInfo.LocationCodes = &locationCodeRaw
	skuSaleInfo.CustomerScopeCodes = &customerScopeCodeRaw
	// update cache
	model.SkuSaleInfoCacheDB.Upsert(query, skuSaleInfo)
}

// WarmupAllSkuSaleInfo warmup all
func WarmupAllSkuSaleInfo() {
	offset := int64(0)
	limit := int64(500)
	TRUE := true

	for {
		result := model.CampaignProductDB.Query(&model.CampaignProduct{IsActive: &TRUE, Status: enum.CampaignProductStatus.NORMAL}, offset, limit, nil)
		if result.Status != common.APIStatus.Ok {
			break
		}

		for _, campaignPrd := range result.Data.([]*model.CampaignProduct) {
			condition := bson.M{
				"is_active":     &TRUE,
				"campaign_code": campaignPrd.CampaignCode,
				"$or": []bson.M{
					{"status": enum.CampaignStatus.UPCOMING},
					{"status": enum.CampaignStatus.PROCESSING},
				},
			}

			campaignResult := model.CampaignDB.QueryOne(condition)
			if campaignResult.Status != common.APIStatus.Ok {
				continue
			}
			campaign := campaignResult.Data.([]*model.Campaign)[0]
			WarmupSkuSaleInfo(&model.SkuSaleInfo{
				SkuCode:             campaignPrd.Sku,
				Name:                campaign.CampaignName,
				CampaignCode:        &campaign.CampaignCode,
				CampaignProductCode: &campaignPrd.CampaignProductCode,
				StartTime:           campaign.StartTime,
				EndTime:             campaign.EndTime,
				SaleType:            enum.SaleType.CAMPAIGN,
				IsActive:            &TRUE,
				Status:              campaignPrd.Status,
				LocationCodes:       campaign.Regions,
				CustomerScopeCodes:  campaign.CustomerScopes,
			})
		}
		offset += limit
	}
}

// WarmupSkuSaleInfoByCampaign warmup 1 document
func WarmupSkuSaleInfoByCampaign(query *model.Campaign) {
	if len(query.CampaignCode) == 0 {
		return
	}

	result := model.CampaignDB.QueryOne(&model.Campaign{
		CampaignCode: query.CampaignCode,
	})
	if result.Status != common.APIStatus.Ok {
		return
	}

	campaign := result.Data.([]*model.Campaign)[0]

	// upsert sku sale info cache by campaign code
	offset := int64(0)
	limit := int64(500)
	TRUE := true

	isActive := false

	if campaign.IsActive != nil && *campaign.IsActive == true && (campaign.Status == enum.CampaignStatus.UPCOMING || campaign.Status == enum.CampaignStatus.PROCESSING) {
		isActive = true
	}

	for {
		result := model.CampaignProductDB.Query(&model.CampaignProduct{CampaignCode: campaign.CampaignCode, IsActive: &TRUE, Status: enum.CampaignProductStatus.NORMAL}, offset, limit, nil)
		if result.Status != common.APIStatus.Ok {
			break
		}

		for _, campaignPrd := range result.Data.([]*model.CampaignProduct) {

			WarmupSkuSaleInfo(&model.SkuSaleInfo{
				SkuCode:             campaignPrd.Sku,
				Name:                campaign.CampaignName,
				CampaignCode:        &campaignPrd.CampaignCode,
				CampaignProductCode: &campaignPrd.CampaignProductCode,
				StartTime:           campaign.StartTime,
				EndTime:             campaign.EndTime,
				SaleType:            enum.SaleType.CAMPAIGN,
				IsActive:            &isActive,
				Status:              campaignPrd.Status,
				LocationCodes:       campaign.Regions,
				CustomerScopeCodes:  campaign.CustomerScopes,
			})
		}

		offset += limit
	}
}
