package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type LuckyWheelLog struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	CreatedBy       int64              `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	AccountID      int64  `json:"accountId,omitempty" bson:"account_id,omitempty"`
	CustomerID     int64  `json:"customerId,omitempty" bson:"customer_id,omitempty"`
	CustomerPhone  string `json:"customerPhone,omitempty" bson:"customer_phone,omitempty"`
	LuckyWheelCode string `json:"luckyWheelCode,omitempty" bson:"lucky_wheel_code,omitempty"`
	ItemCode       string `json:"itemCode,omitempty" bson:"item_code,omitempty"`
	Message        string `json:"message,omitempty" bson:"message,omitempty"`
	MessageHidden  string `json:"messageHidden,omitempty" bson:"message_hidden,omitempty"`
	ItemImage      string `json:"itemImage,omitempty" bson:"item_image,omitempty"`
	IsHasGift      bool   `json:"isHasGift,omitempty" bson:"is_has_gift,omitempty"`

	Question *LuckyWheelQuestionLog `json:"question,omitempty" bson:"question,omitempty"`

	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
}

type LuckyWheelQuestionLog struct {
	QuestionCode  string     `json:"questionCode,omitempty" bson:"question_code,omitempty"`
	Question      string     `json:"question,omitempty" bson:"question,omitempty"`
	CorrectAnswer GameAnswer `json:"answer,omitempty" bson:"answer,omitempty"`
	UserAnswer    GameAnswer `json:"userAnswer,omitempty" bson:"user_answer,omitempty"`
}

var LuckyWheelLogDB = &db.Instance{
	ColName:        "lucky_wheel_log",
	TemplateObject: &LuckyWheelLog{},
}

// InitLuckyWheelLogModel is func init model
func InitLuckyWheelLogModel(s *mongo.Database) {
	LuckyWheelLogDB.ApplyDatabase(s)
}
