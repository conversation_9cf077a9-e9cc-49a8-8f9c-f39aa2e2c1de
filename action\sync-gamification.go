package action

import (
	"fmt"
	"time"

	"gitlab.com/buymed.th/marketplace/promotion/client"
	"gitlab.com/buymed.th/marketplace/promotion/model"
	"gitlab.com/buymed.th/marketplace/promotion/model/enum"
	"gitlab.com/buymed.th/marketplace/promotion/utils"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"go.mongodb.org/mongo-driver/bson"
)

func SyncGamificationResultFromOrder(acc *model.Account, orderID int64) *common.APIResponse {
	order, err := client.Services.Order.GetOrder(orderID)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "GET_ORDER_FAIL",
		}
	}
	customer, err := client.Services.Customer.GetCustomerByAccountID(order.AccountID)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.<PERSON><PERSON>r(),
			ErrorCode: "GET_CUSTOMER_FAIL",
		}
	}

	gamificationList := getGamificationToSync(order, customer)
	ids := make([]*int64, 0)
	for _, gamification := range gamificationList {
		ids = append(ids, &gamification.GamificationID)
	}
	detailList := getGamificationDetailToSync(ids)
	resultList := getGamificationResultToSync(ids, order.AccountID)
	resultMap := makeGamificationResultMap(resultList)
	gamificationMap := makeGamificationMap(gamificationList)
	now := time.Now()
	for _, detail := range detailList {
		if detail.Condition != nil {
			switch detail.Condition.Type {
			case enum.GamificationConditionType.TotalOrderPrice:
				gamificationUpdate := gamificationMap[detail.GamificationID]
				if order.TotalPrice == nil {
					continue
				}
				if result, ok := resultMap[fmt.Sprintf("%d_%d", detail.GamificationDetailID, order.AccountID)]; ok && result != nil {
					isSynced := false
					for _, data := range result.Data {
						if data.OrderID == orderID {
							isSynced = true
						}
					}
					if isSynced {
						continue
					}
					curStatus := result.Status
					result.Value = result.Value + *order.TotalPrice
					if result.Value >= detail.Condition.Target && result.Status == enum.GamificationResultStatus.IN_PROGRESS {
						result.Status = enum.GamificationResultStatus.COMPLETED
						result.CompletedTime = &now
					}
					result.Data = append(result.Data, &model.GamificationResultData{OrderID: orderID, Value: *order.TotalPrice, CreatedTime: &now})
					if res := model.GamificationResultDB.UpdateOne(model.GamificationResult{GamificationResultID: result.GamificationResultID}, result); res.Status != common.APIStatus.Ok {
						continue
					} else {
						if result.Status == enum.GamificationResultStatus.COMPLETED && curStatus == enum.GamificationResultStatus.IN_PROGRESS {
							if gamificationUpdate.NumberOfCompletedCustomer == nil {
								gamificationUpdate.NumberOfCompletedCustomer = utils.ParseIntToPointer(0)
							}
							gamificationUpdate.NumberOfCompletedCustomer = utils.ParseIntToPointer(*gamificationUpdate.NumberOfCompletedCustomer + 1)
						}
					}
				} else {
					newResult := model.GamificationResult{
						CreatedBy:              acc.AccountID,
						GamificationCode:       detail.GamificationCode,
						GamificationID:         detail.GamificationID,
						GamificationDetailCode: detail.GamificationDetailCode,
						GamificationDetailID:   detail.GamificationDetailID,
						AccountID:              order.AccountID,
						Value:                  *order.TotalPrice,
						Status:                 enum.GamificationResultStatus.IN_PROGRESS,
						Data: []*model.GamificationResultData{{
							OrderID:     orderID,
							Value:       *order.TotalPrice,
							CreatedTime: &now,
						}},
					}
					if newResult.Value >= detail.Condition.Target {
						newResult.Status = enum.GamificationResultStatus.COMPLETED
						newResult.CompletedTime = &now
					}
					newResult.GamificationResultID, newResult.GamificationResultCode = model.GenGamificationResult()
					if res := model.GamificationResultDB.Create(newResult); res.Status != common.APIStatus.Ok {
						continue
					} else {
						if gamificationUpdate.NumberOfCompletedCustomer == nil {
							gamificationUpdate.NumberOfJoinedCustomer = utils.ParseIntToPointer(0)
						}
						if gamificationUpdate.NumberOfJoinedCustomer == nil {
							gamificationUpdate.NumberOfJoinedCustomer = utils.ParseIntToPointer(0)
						}
						gamificationUpdate.NumberOfJoinedCustomer = utils.ParseIntToPointer(*gamificationUpdate.NumberOfJoinedCustomer + 1)
						if newResult.Status == enum.GamificationResultStatus.COMPLETED {
							gamificationUpdate.NumberOfCompletedCustomer = utils.ParseIntToPointer(*gamificationUpdate.NumberOfCompletedCustomer + 1)
						}
					}
				}

				model.GamificationDB.UpdateOne(model.Gamification{GamificationID: gamificationUpdate.GamificationID}, gamificationUpdate)
			}
		}
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Sync success",
	}
}

func getGamificationToSync(order *model.Order, customer *model.Customer) []*model.Gamification {
	gamificationList := make([]*model.Gamification, 0)
	now := time.Now()
	query := model.Gamification{IsActive: utils.ParseBoolToPointer(true), ComplexQuery: []*bson.M{
		{
			"$or": []bson.M{
				{
					"scope.customer_ids": customer.CustomerID,
				},
				{
					"scope.customer_ids": bson.M{"$size": 0},
				},
				{
					"scope.customer_ids": nil,
				},
			},
		},
		{
			"$or": []bson.M{
				{
					"scope.customer_scopes": customer.Scope,
				},
				{
					"scope.customer_scopes": bson.M{"$size": 0},
				},
				{
					"scope.customer_scopes": nil,
				},
			},
		},
		{
			"start_time": bson.M{"$lte": now},
			"end_time":   bson.M{"$gte": now},
		},
	}}
	if qGamification := model.GamificationDB.Query(query, 0, 0, nil); qGamification.Status == common.APIStatus.Ok {
		gamificationList = qGamification.Data.([]*model.Gamification)
	}
	return gamificationList
}

func getGamificationDetailToSync(ids []*int64) []*model.GamificationDetail {
	details := make([]*model.GamificationDetail, 0)
	queryDetail := &model.Gamification{ComplexQuery: []*bson.M{
		{
			"gamification_id": bson.M{"$in": ids},
		},
	}}
	offset := int64(0)
	limit := int64(1000)
	for {
		if qDetail := model.GamificationDetailDB.Query(queryDetail, offset*limit, limit, nil); qDetail.Status == common.APIStatus.Ok {
			details = append(details, qDetail.Data.([]*model.GamificationDetail)...)
			offset++
		} else {
			break
		}
	}
	return details
}

func getGamificationResultToSync(ids []*int64, accountID int64) []*model.GamificationResult {
	results := make([]*model.GamificationResult, 0)
	queryResult := &model.GamificationResult{
		ComplexQuery: []*bson.M{
			{
				"gamification_id": bson.M{"$in": ids},
			},
		}, AccountID: accountID,
	}
	offset := int64(0)
	limit := int64(1000)
	for {
		if qResult := model.GamificationResultDB.Query(queryResult, offset*limit, limit, nil); qResult.Status == common.APIStatus.Ok {
			results = append(results, qResult.Data.([]*model.GamificationResult)...)
			offset++
		} else {
			break
		}
	}
	return results
}

func makeGamificationMap(results []*model.Gamification) map[int64]*model.Gamification {
	resultMap := make(map[int64]*model.Gamification)
	for _, result := range results {
		resultMap[result.GamificationID] = result
	}
	return resultMap
}

func makeGamificationResultMap(results []*model.GamificationResult) map[string]*model.GamificationResult {
	resultMap := make(map[string]*model.GamificationResult)
	for _, result := range results {
		resultMap[fmt.Sprintf("%d_%d", result.GamificationDetailID, result.AccountID)] = result
	}
	return resultMap
}
