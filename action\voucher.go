package action

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/buymed.th/marketplace/promotion/client"
	"gitlab.com/buymed.th/marketplace/promotion/model"
	"gitlab.com/buymed.th/marketplace/promotion/model/enum"
	"gitlab.com/buymed.th/marketplace/promotion/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// GetVoucherByID get voucher by id
func GetVoucherByID(voucherID int64) *common.APIResponse {
	if voucherID == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã khuyến mãi không được để trống.",
			ErrorCode: "VOUCHER_ID_REQUIRED",
		}
	}
	return model.VoucherDB.QueryOne(bson.M{"voucher_id": voucherID})
}

// GetPromotionByCode func
func GetPromotionByCode(voucherCode string) *common.APIResponse {
	voucherCode = strings.TrimSpace(voucherCode)
	if voucherCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã khuyến mãi không được để trống.",
			ErrorCode: "VOUCHER_CODE_REQUIRED",
		}
	}

	voucherResult := model.VoucherDB.QueryOne(bson.M{
		"code": voucherCode,
		"status": bson.M{
			"$nin": []string{"DELETED"},
		},
	})
	if voucherResult.Status != common.APIStatus.Ok {
		return voucherResult
	}

	voucher := voucherResult.Data.([]*model.Voucher)[0]
	if voucher.PromotionID == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy thông tin khuyến mãi.",
			ErrorCode: "PROMOTION_NOT_FOUND",
		}
	}

	promotionResult := model.PromotionDB.QueryOne(bson.M{
		"promotion_id": voucher.PromotionID,
		"status": bson.M{
			"$nin": []string{"DELETED", "HIDE"},
		},
	})

	if promotionResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy thông tin khuyến mãi.",
			ErrorCode: "PROMOTION_NOT_FOUND",
		}
	}

	promotionInfo := promotionResult.Data.([]*model.Promotion)[0]
	if promotionInfo.PromotionID == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy thông tin khuyến mãi.",
			ErrorCode: "PROMOTION_NOT_FOUND",
		}
	}

	voucher.Promotion = promotionInfo
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Successfully",
		Data:    []*model.Voucher{voucher},
	}

}

// GetPromotionByCustomerID func
func GetPromotionByCustomerID(customerId int64) *common.APIResponse {
	if customerId == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã khuyến mãi không được để trống.",
			ErrorCode: "VOUCHER_ID_REQUIRED",
		}
	}
	return model.VoucherDB.Query(bson.M{"applied_customers": bson.M{"$in": []int64{customerId}}}, 0, 1000, nil)
}

// GetVouchers get vouchers
func GetVouchers(query *model.Voucher, offset int64, limit int64, getTotal bool, sortField string, sortType int, tabType string) *common.APIResponse {

	if len(tabType) > 0 {
		if tabType == "NOT_USED" {
			if query.CustomerID != nil {
				voucherCodes := make([]string, 0)
				if query.CustomerID != nil {
					offset2 := int64(0)
					limit2 := int64(1000)
					for {
						userPromotionResp := model.UserPromotionDB.Query(&bson.M{
							"customer_id": *query.CustomerID,
							"$or": []bson.M{
								{"amount": nil},
								{"amount": 0},
							},
							"status": bson.M{"$nin": []string{string(enum.CodeStatus.DELETED), string(enum.CodeStatus.INACTIVE), string(enum.CodeStatus.USED)}},
						}, offset2*limit2, limit2, nil)
						if userPromotionResp.Status != common.APIStatus.Ok {
							break
						}

						for _, userPromotion := range userPromotionResp.Data.([]*model.UserPromotion) {
							voucherCodes = append(voucherCodes, userPromotion.VoucherCode)
						}
						offset2++
					}

				}

				if len(voucherCodes) == 0 {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   "Không tìm thấy mã khuyến mãi.",
						ErrorCode: "VOUCHER_NOT_FOUND",
					}
				}

				query.ComplexQuery = append(query.ComplexQuery, &bson.M{
					"code": bson.M{
						"$in": voucherCodes,
					},
				})
			} else {
				query.ComplexQuery = append(query.ComplexQuery, &bson.M{
					"$or": []bson.M{
						{"usage_total": nil},
						{"usage_total": 0},
					},
				})
			}
		} else if tabType == "USED" {
			if query.CustomerID != nil {
				voucherCodes := make([]string, 0)
				if query.CustomerID != nil {
					offset2 := int64(0)
					limit2 := int64(1000)
					for {
						userPromotionResp := model.UserPromotionDB.Query(&bson.M{
							"customer_id": *query.CustomerID,
							"amount": bson.M{
								"$gt": 0,
							},
						}, offset2*limit2, limit2, nil)
						if userPromotionResp.Status != common.APIStatus.Ok {
							break
						}

						for _, userPromotion := range userPromotionResp.Data.([]*model.UserPromotion) {
							voucherCodes = append(voucherCodes, userPromotion.VoucherCode)
						}
						offset2++
					}
				}

				if len(voucherCodes) == 0 {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   "Không tìm thấy mã khuyến mãi.",
						ErrorCode: "VOUCHER_NOT_FOUND",
					}
				}

				query.ComplexQuery = append(query.ComplexQuery, &bson.M{
					"code": bson.M{
						"$in": voucherCodes,
					},
				})

			} else {
				query.ComplexQuery = append(query.ComplexQuery, &bson.M{
					"usage_total": &bson.M{
						"$exists": true,
						"$gt":     0,
					},
				})
			}
		}
	} else {
		if query.CustomerID != nil {
			voucherCodes := make([]string, 0)
			if query.CustomerID != nil {
				offset2 := int64(0)
				limit2 := int64(1000)
				for {
					userPromotionResp := model.UserPromotionDB.Query(&bson.M{
						"customer_id": *query.CustomerID,
						"status":      bson.M{"$nin": []string{string(enum.CodeStatus.DELETED), string(enum.CodeStatus.INACTIVE)}},
					}, offset2*limit2, limit2, nil)
					if userPromotionResp.Status != common.APIStatus.Ok {
						break
					}

					for _, userPromotion := range userPromotionResp.Data.([]*model.UserPromotion) {
						voucherCodes = append(voucherCodes, userPromotion.VoucherCode)
					}
					offset2++
				}

			}

			if len(voucherCodes) == 0 {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   "Không tìm thấy mã khuyến mãi.",
					ErrorCode: "VOUCHER_NOT_FOUND",
				}
			}

			query.ComplexQuery = append(query.ComplexQuery, &bson.M{
				"code": bson.M{
					"$in": voucherCodes,
				},
			})
		}
	}

	if query.ProductCode != "" {
		voucherResp := model.VoucherCacheDB.Query(bson.M{
			"ref_product": primitive.Regex{Pattern: fmt.Sprintf(".*%s.*", query.ProductCode), Options: ""},
		}, 0, 1000, nil)

		if voucherResp.Status != common.APIStatus.Ok {
			return voucherResp
		}

		voucherCodes := make([]string, 0)
		voucherRespData := voucherResp.Data.([]*model.Voucher)
		for _, voucherItem := range voucherRespData {
			voucherCodes = append(voucherCodes, voucherItem.Code)
		}

		if len(voucherCodes) > 0 {
			query.ComplexQuery = append(query.ComplexQuery, &bson.M{
				"code": &bson.M{
					"$in": voucherCodes,
				},
			})
		}

	}

	result := model.VoucherDB.Query(
		query,
		offset,
		limit,
		&primitive.M{sortField: sortType})
	if getTotal {
		countResult := model.VoucherDB.Count(query)
		result.Total = countResult.Total
	}
	return result
}

/*
// getActiveVoucherOldVersion get active voucher
func getActiveVoucherOldVersion(account *model.Account) *common.APIResponse {
	customerInfo, err := client.Services.Customer.GetCustomerByAccountID(account.AccountID)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Forbidden,
			Message:   "Tài khoản không xác định",
			ErrorCode: "CUSTOMER_NOT_FOUND",
		}
	}

	conditions := bson.M{
		"status":         enum.PromotionStatus.ACTIVE,
		"promotion_type": enum.PromotionType.VOUCHERCODE,
	}

	conditions["$or"] = []bson.M{
		bson.M{
			"scopes.customer_level_codes": customerInfo.Level,
		},
		bson.M{
			"scopes.customer_level_codes": "ALL",
		},
		bson.M{
			"scopes.customer_level_codes": bson.M{
				"$exists": false,
			},
		},
	}

	queryResult := model.PromotionDB.Query(
		conditions,
		0,
		1000,
		nil,
	)
	if queryResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			Message:   "Không có chương trình khuyến mãi nào.",
			ErrorCode: "PROMOTION_NOT_FOUND",
		}
	}
	promotionList := queryResult.Data.([]*model.Promotion)
	voucherList := []*model.Voucher{}

	for _, promotion := range promotionList {
		getVoucherResult := model.VoucherDB.Query(bson.M{
			"promotion_id": promotion.PromotionID,
			"type":         enum.VoucherType.PUBLIC,
			"status":       enum.VoucherStatus.ACTIVE,
			"end_time": bson.M{
				"$gt": time.Now(),
			},
			"public_time": bson.M{
				"$lt": time.Now(),
			},
			"$or": bson.A{
				bson.M{
					"applied_customers": bson.M{"$size": 0},
				},
				bson.M{
					"applied_customers": nil,
				},
				bson.M{
					"applied_customers": customerInfo.CustomerID,
				},
			},
		}, 0, 0, nil)

		if getVoucherResult.Status == common.APIStatus.Ok {
			vouchers := getVoucherResult.Data.([]*model.Voucher)
			for _, voucher := range vouchers {
				voucher.Promotion = promotion
				voucherList = append(voucherList, voucher)
			}
		}
	}

	if len(voucherList) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			Message:   "Không có mã khuyến mãi nào.",
			ErrorCode: "VOUCHER_NOT_FOUND",
		}
	}

	resp := getDetailVouchers(voucherList, customerInfo.CustomerID)

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Danh sách mã khuyến mãi.",
		Data:    resp,
	}
}
*/
//
//// GetActiveVoucher get active voucher
//func GetActiveVoucher(cart *model.Cart, account *model.Account) *common.APIResponse {
//	if cart == nil {
//		return getActiveVoucherOldVersion(account)
//	}
//	customerInfo, err := client.Services.Customer.GetCustomerByAccountID(account.AccountID)
//	if err != nil {
//		return &common.APIResponse{
//			Status:    common.APIStatus.Forbidden,
//			Message:   "Tài khoản không xác định",
//			ErrorCode: "CUSTOMER_NOT_FOUND",
//		}
//	}
//
//	conditions := bson.M{
//		"status":         enum.PromotionStatus.ACTIVE,
//		"promotion_type": enum.PromotionType.VOUCHERCODE,
//	}
//
//	conditions["$or"] = []bson.M{
//		bson.M{
//			"scopes.customer_level_codes": customerInfo.Level,
//		},
//		bson.M{
//			"scopes.customer_level_codes": "ALL",
//		},
//		bson.M{
//			"scopes.customer_level_codes": bson.M{
//				"$exists": false,
//			},
//		},
//	}
//
//	queryResult := model.PromotionDB.Query(
//		conditions,
//		0,
//		1000,
//		nil,
//	)
//	if queryResult.Status != common.APIStatus.Ok {
//		return &common.APIResponse{
//			Status:    common.APIStatus.NotFound,
//			Message:   "Không có chương trình khuyến mãi nào.",
//			ErrorCode: "PROMOTION_NOT_FOUND",
//		}
//	}
//	promotionList := queryResult.Data.([]*model.Promotion)
//	voucherList := []*model.Voucher{}
//
//	promotionIDs := make([]int64, 0)
//	mapUserPromotion := make(map[string]*model.UserPromotion)
//	mapPromotion := make(map[int64]*model.Promotion)
//
//	regionCodes := make([]string, 0)
//	mapRegionCodes := make(map[string][]string)
//
//	for _, promotion := range promotionList {
//		mapPromotion[promotion.PromotionID] = promotion
//		promotionIDs = append(promotionIDs, promotion.PromotionID)
//		for _, scope := range promotion.Scopes {
//			regionCodes = append(regionCodes, scope.AreaCodes...)
//		}
//	}
//
//	for _, region := range regionCodes {
//		mapRegionCodes[region] = []string{region}
//	}
//
//	regionResp := client.LocationClient.GetRegionListByRegionCodes()
//	if regionResp.Status == common.APIStatus.Ok {
//		for _, region := range regionResp.Data {
//			if region.Scope == "SALE_REGION" && len(mapRegionCodes[region.Code]) > 0 {
//				mapRegionCodes[region.Code] = append(mapRegionCodes[region.Code], region.ProvinceCodes...)
//			}
//		}
//	}
//
//	cart.ScopeRegionCodes = mapRegionCodes
//	userPromotionsResult := model.UserPromotionDB.Query(bson.M{
//		"customer_id": cart.CustomerID,
//		"promotion_id": bson.M{
//			"$in": promotionIDs,
//		},
//	}, 0, 0, nil)
//
//	if userPromotionsResult.Status == common.APIStatus.Ok {
//		userPromotions := userPromotionsResult.Data.([]*model.UserPromotion)
//		for _, userPromo := range userPromotions {
//			mapUserPromotion[userPromo.VoucherCode] = userPromo
//		}
//	}
//
//	getVouchersResult := model.VoucherDB.Query(bson.M{
//		"promotion_id": bson.M{
//			"$in": promotionIDs,
//		},
//		"type":   enum.VoucherType.PUBLIC,
//		"status": enum.VoucherStatus.ACTIVE,
//		"end_time": bson.M{
//			"$gt": time.Now(),
//		},
//		"public_time": bson.M{
//			"$lt": time.Now(),
//		},
//		"$or": bson.A{
//			bson.M{
//				"applied_customers": bson.M{"$size": 0},
//			},
//			bson.M{
//				"applied_customers": nil,
//			},
//			bson.M{
//				"applied_customers": customerInfo.CustomerID,
//			},
//		},
//	}, 0, 0, nil)
//
//	if getVouchersResult.Status != common.APIStatus.Ok {
//		return &common.APIResponse{
//			Status:    common.APIStatus.NotFound,
//			Message:   "Không có chương trình khuyến mãi nào.",
//			ErrorCode: "VOUCHER_NOT_FOUND",
//		}
//	}
//
//	// Scan N voucher
//	vouchers := getVouchersResult.Data.([]*model.Voucher)
//	for idx, voucher := range vouchers {
//		if mapUserPromotion[voucher.Code] != nil && voucher.MaxUsagePerCustomer > 0 && voucher.MaxUsagePerCustomer <= mapUserPromotion[voucher.Code].Amount {
//			continue
//		}
//		voucher.Promotion = mapPromotion[voucher.PromotionID]
//		isMatch, canView, _, _, _ := getMatchingCondition(*voucher, *cart)
//		if !canView {
//			continue
//		}
//		vouchers[idx].CanUse = &isMatch
//		voucherList = append(voucherList, voucher)
//	}
//
//	if len(voucherList) == 0 {
//		return &common.APIResponse{
//			Status:    common.APIStatus.NotFound,
//			Message:   "Không có mã khuyến mãi nào.",
//			ErrorCode: "VOUCHER_NOT_FOUND",
//		}
//	}
//
//	resp := getDetailVouchers(voucherList, customerInfo.CustomerID)
//
//	return &common.APIResponse{
//		Status:  common.APIStatus.Ok,
//		Message: "Danh sách mã khuyến mãi.",
//		Data:    resp,
//	}
//}
//
//// SearchActiveVoucher search active voucher
//func SearchActiveVoucher(voucherCode string, account *model.Account) *common.APIResponse {
//	if !client.Services.Customer.IsOnline() {
//		return &common.APIResponse{
//			Status:    common.APIStatus.Error,
//			Message:   "Đăng ký tài khoản đang bảo trì, vui lòng thử lại sau",
//			ErrorCode: "CUSTOMER_UNVAILABLE",
//		}
//	}
//
//	customerInfo, err := client.Services.Customer.GetCustomerByAccountID(account.AccountID)
//	if err != nil {
//		return &common.APIResponse{
//			Status:    common.APIStatus.Forbidden,
//			Message:   "Tài khoản không xác định",
//			ErrorCode: "CUSTOMER_NOT_FOUND",
//		}
//	}
//
//	getVoucherResult := model.VoucherDB.QueryOne(bson.M{
//		"code":   voucherCode,
//		"type":   enum.VoucherType.PUBLIC,
//		"status": enum.VoucherStatus.ACTIVE,
//		"end_time": bson.M{
//			"$gt": time.Now(),
//		},
//		"public_time": bson.M{
//			"$lt": time.Now(),
//		},
//	})
//
//	if getVoucherResult.Status != common.APIStatus.Ok {
//		return &common.APIResponse{
//			Status:    common.APIStatus.NotFound,
//			Message:   "Không tìm thấy mã khuyến mãi nào.",
//			ErrorCode: "VOUCHER_NOT_FOUND",
//		}
//	}
//
//	voucher := getVoucherResult.Data.([]*model.Voucher)[0]
//
//	conditions := bson.M{
//		"promotion_id":   voucher.PromotionID,
//		"status":         enum.PromotionStatus.ACTIVE,
//		"promotion_type": enum.PromotionType.VOUCHERCODE,
//	}
//
//	conditions["$or"] = []bson.M{
//		bson.M{
//			"scopes.customer_level_codes": customerInfo.Level,
//		},
//		bson.M{
//			"scopes.customer_level_codes": "ALL",
//		},
//		bson.M{
//			"scopes.customer_level_codes": bson.M{
//				"$exists": false,
//			},
//		},
//	}
//
//	queryResult := model.PromotionDB.QueryOne(conditions)
//	if queryResult.Status != common.APIStatus.Ok {
//		return &common.APIResponse{
//			Status:    common.APIStatus.NotFound,
//			Message:   "Không tìm thấy mã khuyến mãi nào.",
//			ErrorCode: "VOUCHER_NOT_FOUND",
//		}
//	}
//	promotion := queryResult.Data.([]*model.Promotion)[0]
//	voucher.Promotion = promotion
//	return &common.APIResponse{
//		Status:  common.APIStatus.Ok,
//		Message: "Danh sách mã khuyến mãi.",
//		Data:    []model.Voucher{*voucher},
//	}
//}

// CreateManyVouchers
func CreateManyVouchers(vouchers *model.CreateManyVoucherRequest, account *model.Account) *common.APIResponse {
	// Validate code
	if len(vouchers.Vouchers) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không có mã khuyến mãi nào.",
			ErrorCode: "CODE_REQUIRED",
		}
	}

	// Validate vouchers
	for _, voucher := range vouchers.Vouchers {
		resp := CreateVoucher(&voucher, account)
		if resp.Status != common.APIStatus.Ok {
			return resp
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Tạo mã khuyến mãi thành công.",
	}
}

// CreateVoucher create a new voucher
func CreateVoucher(voucher *model.Voucher, account *model.Account) *common.APIResponse {
	// Validate code
	if voucher.Code == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã khuyến mãi không được để trống.",
			ErrorCode: "CODE_REQUIRED",
		}
	}

	// Validate status
	if voucher.Status != nil && !isVoucherStatusValid(*voucher.Status) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Trạng thái mã khuyến mãi không hợp lệ.",
			ErrorCode: "STATUS_INVALID",
		}
	}

	// Validate startTime
	if voucher.StartTime == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Thời gian bắt đầu không được để trống.",
			ErrorCode: "START_TIME_REQUIRE",
		}
	}

	// Validate endTime
	if voucher.EndTime == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Thời gian kết thúc không được để trống.",
			ErrorCode: "END_TIME_REQUIRE",
		}
	}

	// Validate publicTime
	if voucher.PublicTime == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Thời gian hiển thị không được để trống.",
			ErrorCode: "PUBLIC_TIME_REQUIRE",
		}
	}

	// Validate startTime
	if voucher.StartTime.Unix() > voucher.EndTime.Unix() {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Thời gian bắt đầu phải nhỏ hơn thời gian kết thúc.",
			ErrorCode: "START_TIME_INVALID",
		}
	}

	// Validate publicTime
	if voucher.PublicTime.Unix() > voucher.StartTime.Unix() {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Thời gian hiển thị không được sau ngày bắt đầu khuyến mãi.",
			ErrorCode: "PUBLIC_TIME_INVALID",
		}
	}

	getVoucherResult := model.VoucherDB.QueryOne(
		bson.M{
			"code": voucher.Code,
		},
	)

	// Validate Code
	if getVoucherResult.Status == common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã khuyến mãi đã tồn tại.",
			ErrorCode: "CODE_INVALID",
		}
	}

	var promotion *model.Promotion

	getPromotionResult := model.PromotionDB.QueryOne(
		bson.M{
			"promotion_id": voucher.PromotionID,
		},
	)
	if getPromotionResult.Status == common.APIStatus.Ok {
		promotion = getPromotionResult.Data.([]*model.Promotion)[0]

	}

	if voucher.UsageType == nil {
		voucher.UsageType = &enum.UsageType.ALONE
	}

	if voucher.Status == nil {
		voucher.Status = &enum.VoucherStatus.ACTIVE
	}

	if voucher.DisplayName == "" && promotion != nil {
		voucher.PromotionName = promotion.PromotionName
		voucher.DisplayName = promotion.Description
	}

	if voucher.CustomerApplyType == "" {
		if voucher.AppliedCustomers != nil && len(*voucher.AppliedCustomers) > 0 {
			voucher.CustomerApplyType = enum.CustomerApplyType.MANY
		} else {
			voucher.CustomerApplyType = enum.CustomerApplyType.ALL
		}
	}

	voucher.VoucherID = model.GenId("VOUCHER_ID")
	temp := time.Now()
	voucher.CreatedBy = account.AccountID
	voucher.CreatedByUserName = &account.Username
	voucher.CreatedTime = &temp
	voucher.VersionNo = uuid.New().String()
	normCodeStr := strings.Replace(utils.NormalizeString(voucher.Code), " ", "-", -1)
	normDisplayNameStr := strings.Replace(utils.NormalizeString(voucher.DisplayName), " ", "-", -1)
	voucher.HashTag = fmt.Sprintf("%d-%s-%s", voucher.PromotionID, normCodeStr, normDisplayNameStr)

	// set default value to voucher
	if promotion != nil {
		if len(voucher.OrConditions) == 0 || voucher.OrConditions == nil {
			voucher.OrConditions = promotion.OrConditions
		}

		if len(voucher.AndConditions) == 0 || voucher.AndConditions == nil {
			voucher.AndConditions = promotion.AndConditions
		}

		if len(voucher.Scopes) == 0 {
			voucher.Scopes = promotion.Scopes
		}

		if len(voucher.Scopes) > 0 {
			voucherPlatform := findVoucherPlatform(voucher.Scopes)
			promotionPlatform := findVoucherPlatform(promotion.Scopes)
			if voucherPlatform == nil && promotionPlatform != nil {
				voucher.Scopes = append(voucher.Scopes, *promotionPlatform)
			}
		}

		if len(voucher.Rewards) == 0 && promotion.Rewards != nil && len(*promotion.Rewards) > 0 {
			voucher.Rewards = *promotion.Rewards
		}

		if (voucher.ConditionDescription == nil || len(*voucher.ConditionDescription) == 0) && promotion.ConditionDescription != nil {
			voucher.ConditionDescription = promotion.ConditionDescription
		}
		// set apply type
		voucher.ApplyType = promotion.ApplyType

		// set voucher group code
		if promotion.VoucherGroupCode != nil {
			voucher.VoucherGroupCode = promotion.VoucherGroupCode
		}

		if voucher.Priority == nil || *voucher.Priority == 0 {
			voucher.Priority = promotion.Priority
		}
	}

	createResult := model.VoucherDB.Create(voucher)

	if createResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   "Tạo mã khuyến mãi không thành công!",
			ErrorCode: "SERVER_ERROR",
		}
	}

	go func() {
		if voucher.AppliedCustomers != nil {
			CreateUserVoucher(account, &model.UserPromotion{
				CustomerIDs: *voucher.AppliedCustomers,
				VoucherCode: voucher.Code,
			})
		}
		WarmUpVoucherByCode(voucher.Code, voucher)
	}()
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Tạo mã khuyến mãi thành công",
		Data:    createResult.Data.([]*model.Voucher),
	}
}

func findVoucherPlatform(scopes []model.Scope) *model.Scope {
	for _, scope := range scopes {
		if scope.Type == &enum.ScopeType.DISPLAY_PLATFORM {
			return &scope
		}
	}
	return nil
}

// RefundVoucher is func to refund voucher
func RefundVoucher(req *model.PromotionRefundRequest) *common.APIResponse {
	voucherCodes := req.VoucherCodes
	accountId := req.AccountID
	if len(voucherCodes) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã khuyến mãi không được để trống.",
			ErrorCode: "VOUCHER_ID_REQUIRED",
		}
	}

	customerInfo, err := client.Services.Customer.GetCustomerByAccountID(accountId)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Forbidden,
			Message:   "Tài khoản không xác định",
			ErrorCode: "CUSTOMER_NOT_FOUND",
		}
	}
	queryVoucher := model.Voucher{
		ComplexQuery: []*bson.M{
			{
				"code": bson.M{"$in": voucherCodes},
			},
		},
	}
	qVoucher := model.VoucherDB.Query(queryVoucher, 0, 0, nil)
	if qVoucher.Status != common.APIStatus.Ok {
		return qVoucher
	}
	vouchers := qVoucher.Data.([]*model.Voucher)
	for _, voucher := range vouchers {
		qUsed := model.UserPromotionDB.QueryOne(model.UserPromotion{CustomerID: customerInfo.CustomerID, VoucherCode: voucher.Code})
		if qUsed.Status != common.APIStatus.Ok {
			return qUsed
		}
		useValue := qUsed.Data.([]*model.UserPromotion)[0]
		newOrderIds := make([]int64, 0)
		newStatus := enum.CodeStatus.USED
		if voucher.CustomerApplyType == enum.CustomerApplyType.ALL && useValue.Amount != nil && *useValue.Amount == 1 {
			qResult := model.UserPromotionDB.Delete(model.UserPromotion{CustomerID: customerInfo.CustomerID, VoucherCode: voucher.Code})
			if qResult.Status != common.APIStatus.Ok {
				return qResult
			}
			model.UserPromotionCacheDB.Delete(model.UserPromotion{CustomerID: customerInfo.CustomerID, VoucherCode: voucher.Code})
		} else {
			if useValue.OrderIDs != nil {
				for _, id := range *useValue.OrderIDs {
					if id != req.OrderID {
						newOrderIds = append(newOrderIds, id)
					}
				}
			}

			if useValue.Amount != nil && *useValue.Amount == 1 {
				newStatus = enum.CodeStatus.ACTIVE
			}
			qResult := model.UserPromotionDB.UpdateOneWithOption(bson.M{
				"customer_id":  customerInfo.CustomerID,
				"voucher_code": voucher.Code,
			}, bson.M{
				"$inc": bson.M{"amount": -1},
				"$set": bson.M{"order_ids": newOrderIds, "status": newStatus},
			})
			if qResult.Status != common.APIStatus.Ok {
				return qResult
			}
		}
		// update voucher status
		model.VoucherDB.IncreOne(&model.Voucher{Code: voucher.Code},
			"usage_total", -1,
		)
		go func(voucher *model.Voucher) {
			if voucher.Status != nil && *voucher.Status == enum.VoucherStatus.HIDE &&
				voucher.MaxUsage != nil && *voucher.MaxUsage > 0 && voucher.UsageTotal != nil && (*voucher.UsageTotal-1) < *voucher.MaxUsage {
				updater := model.Voucher{NeedCheck: utils.ParseBoolToPointer(true)}
				if time.Now().Unix() <= voucher.EndTime.Unix() {
					updater.Status = &enum.VoucherStatus.ACTIVE
				}
				model.VoucherDB.UpdateOne(model.Voucher{Code: voucher.Code}, updater)
			}
			WarmUpVoucherByCode(voucher.Code, nil)
			WarmUpUserPromotion(voucher.Code, customerInfo.CustomerID, nil)
			createVoucherHistory(&model.VoucherHistory{
				CustomerID: customerInfo.CustomerID,
				Usage:      1,
				OrderID:    req.OrderID,
				Type:       enum.VoucherHistoryType.REFUND,
			}, voucher.PromotionID, customerInfo.CustomerID, voucher.Code)
		}(voucher)

	}
	return qVoucher
}

// UpdateVoucher update a voucher
func UpdateVoucher(voucher *model.UpdateVoucherRequest, accountID int64) *common.APIResponse {

	getVoucherResult := model.VoucherDB.QueryOne(
		bson.M{
			"voucher_id": voucher.VoucherID,
		},
	)

	// Validate Code
	if getVoucherResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã khuyến mãi không tồn tại.",
			ErrorCode: "CODE_INVALID",
		}
	}

	existVoucher := getVoucherResult.Data.([]*model.Voucher)[0]
	//currentVersionNo := existVoucher.VersionNo

	voucher.VersionNo = uuid.New().String()
	if voucher.UsageType == nil || *voucher.UsageType == "" {
		voucher.UsageType = &enum.UsageType.ALONE
	}
	normCodeStr := strings.Replace(utils.NormalizeString(existVoucher.Code), " ", "-", -1)
	normPromotionNameStr := strings.Replace(utils.NormalizeString(existVoucher.PromotionName), " ", "-", -1)
	voucher.HashTag = fmt.Sprintf("%d-%s-%s", existVoucher.PromotionID, normCodeStr, normPromotionNameStr)

	afterOption := options.After
	updateResult := model.VoucherDB.UpdateOne(
		bson.M{
			"voucher_id": voucher.VoucherID,
		},
		voucher,
		&options.FindOneAndUpdateOptions{
			ReturnDocument: &afterOption,
		},
	)

	if updateResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   "Cập nhật mã khuyến mãi không thành công!",
			ErrorCode: "SERVER_ERROR",
		}
	}
	go func() {
		WarmUpVoucherByCode(updateResult.Data.([]*model.Voucher)[0].Code, updateResult.Data.([]*model.Voucher)[0])
		WarmUpUserPromotion(updateResult.Data.([]*model.Voucher)[0].Code, 0, nil)
	}()

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật mã khuyến mãi thành công",
		Data:    updateResult.Data.([]*model.Voucher),
	}
}

// GetVouchersByCodes is func to get list vouchers by list codes
func GetVouchersByCodes(listCodes []string) *common.APIResponse {
	return model.VoucherDB.Query(bson.M{
		"code": bson.M{
			"$in": listCodes,
		},
		"status": bson.M{
			"$nin": []string{"DELETED"},
		},
	}, 0, int64(len(listCodes)), nil)
}

// UpdateVoucherStatus update a voucher status
func UpdateVoucherStatus(input *model.Voucher, AccountID int64) *common.APIResponse {
	// Verify input data
	if input.VoucherID <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "voucherId không được để trống.",
			ErrorCode: "INVALID_VOUCHER_ID_INPUT",
		}
	}

	if input.Status == nil || !isVoucherStatusValid(*input.Status) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Trạng thái không hợp lệ.",
			ErrorCode: "STATUS_INVALID",
		}
	}

	// Get Voucher
	voucherResult := model.VoucherDB.QueryOne(
		bson.M{
			"voucher_id": input.VoucherID,
		},
	)

	if voucherResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã khuyến mãi không tồn tại.",
			ErrorCode: "INVALID_VOUCHER_ID_INPUT",
		}
	}

	existedVoucher := voucherResult.Data.([]*model.Voucher)[0]

	// Check status hiên tại của VOUCHER có được cập nhật không ?
	canUpdate := false
	switch *existedVoucher.Status {
	case enum.VoucherStatus.HIDE:
		canUpdate = true
		break
	case enum.VoucherStatus.WAITING:
		canUpdate = true
		break
	case enum.VoucherStatus.ACTIVE:
		canUpdate = true
		break
	}

	if !canUpdate {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã khuyến mãi đã hết hạn. Không cho phép cập nhật trạng thái. Xin cảm ơn.",
			ErrorCode: "INVALID_VOUCHER_STATUS",
		}
	}

	now := time.Now()
	currentVersionNo := existedVoucher.VersionNo
	existedVoucher.UpdatedBy = AccountID
	existedVoucher.LastUpdatedTime = &now
	existedVoucher.VersionNo = uuid.New().String()
	existedVoucher.Status = input.Status

	afterOption := options.After
	updateResult := model.VoucherDB.UpdateOne(
		bson.M{
			"_id":        existedVoucher.ID,
			"version_no": currentVersionNo,
		},
		existedVoucher,
		&options.FindOneAndUpdateOptions{
			ReturnDocument: &afterOption,
		},
	)

	if updateResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Cập nhật trạng thái mã khuyến mãi thất bại.",
		}
	}
	go func() { WarmUpVoucherByCode(existedVoucher.Code, nil) }()

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật trạng thái mã khuyến mãi thành công.",
		Data:    updateResult.Data.([]*model.Voucher),
	}
}

// AutoExpireVoucher func
func AutoExpireVoucher() {
	voucherIds := []int64{}

	//GET_MORE:
	vouchersExpire := model.VoucherDB.Query(
		bson.M{
			"status": bson.M{
				"$in": []string{string(enum.VoucherStatus.ACTIVE), string(enum.VoucherStatus.HIDE)},
			},
			"end_time": bson.M{
				"$lte": time.Now(),
			},
		},
		0,
		100,
		nil,
	)

	if vouchersExpire.Status == common.APIStatus.Ok {
		vouchers := vouchersExpire.Data.([]*model.Voucher)
		if len(vouchers) > 0 {
			// force disable all
			for _, ele := range vouchers {
				voucherIds = append(voucherIds, ele.VoucherID)
			}
		}

		// if len(vouchers) == 100 {
		// 	goto GET_MORE
		// }
	}

	if len(voucherIds) > 0 {
		model.VoucherDB.UpdateMany(
			bson.M{
				"voucher_id": bson.M{
					"$in": voucherIds,
				},
			},
			bson.M{
				"status": string(enum.VoucherStatus.EXPIRED),
			})
	}
}

// AutoActiveVoucher func
func AutoActiveVoucher() {
	voucherIds := []int64{}

	// GET_MORE:
	vouchersNeedActive := model.VoucherDB.Query(
		bson.M{
			"status": string(enum.VoucherStatus.WAITING),
			"start_time": bson.M{
				"$lte": time.Now(),
			},
		},
		0,
		100,
		nil,
	)

	if vouchersNeedActive.Status == common.APIStatus.Ok {
		vouchers := vouchersNeedActive.Data.([]*model.Voucher)
		if len(vouchers) > 0 {
			// force disable all
			for _, ele := range vouchers {
				voucherIds = append(voucherIds, ele.VoucherID)
			}
		}

		// if len(vouchers) == 100 {
		// 	goto GET_MORE
		// }
	}

	if len(voucherIds) > 0 {
		model.VoucherDB.UpdateMany(
			bson.M{
				"voucher_id": bson.M{
					"$in": voucherIds,
				},
			},
			bson.M{
				"status": string(enum.VoucherStatus.ACTIVE),
			})
	}
}

func isVoucherTypeValid(voucherType enum.VoucherTypeValue) bool {
	switch voucherType {
	case enum.VoucherType.PRIVATE:
		return true
	case enum.VoucherType.PUBLIC:
		return true
	default:
		return false
	}
}

func isVoucherStatusValid(voucherStatus enum.VoucherStatusValue) bool {
	switch voucherStatus {
	case enum.VoucherStatus.HIDE:
		return true
	case enum.VoucherStatus.ACTIVE:
		return true
	case enum.VoucherStatus.WAITING:
		return true
	case enum.VoucherStatus.DELETED:
		return true
	case enum.VoucherStatus.EXPIRED:
		return true
	default:
		return false
	}
}

func GetCustomerVouchers(accountId, offset, limit int64, getTotal bool) *common.APIResponse {
	customerInfo, err := client.Services.Customer.GetCustomerByAccountID(accountId)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Forbidden,
			Message:   "Tài khoản không xác định",
			ErrorCode: "CUSTOMER_NOT_FOUND",
		}
	}

	voucherCodes := make([]string, 0)
	mapUserPromotion := make(map[string]*model.UserPromotion, 0)
	offset2 := int64(0)
	limit2 := int64(1000)
	for {
		userPromotionResp := model.UserPromotionDB.Query(&bson.M{
			"customer_id": customerInfo.CustomerID,
			"status":      bson.M{"$nin": []string{string(enum.CodeStatus.DELETED), string(enum.CodeStatus.INACTIVE)}},
		}, offset2*limit2, limit2, nil)
		if userPromotionResp.Status != common.APIStatus.Ok {
			break
		}

		for _, userPromotion := range userPromotionResp.Data.([]*model.UserPromotion) {
			voucherCodes = append(voucherCodes, userPromotion.VoucherCode)
			mapUserPromotion[userPromotion.VoucherCode] = userPromotion
		}
		offset2++
	}

	if len(voucherCodes) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			Message:   "Không có mã khuyến mãi.",
			ErrorCode: "VOUCHER_NOT_FOUND",
		}
	}

	query := model.Voucher{
		Status:            &enum.VoucherStatus.ACTIVE,
		CustomerApplyType: enum.CustomerApplyType.MANY,
	}

	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
		"$and": []*bson.M{
			{
				"code": bson.M{
					"$in": voucherCodes,
				},
				"apply_type": bson.M{
					"$nin": []string{string(enum.ApplyType.AUTO)},
				},
			},
		},
	})

	qResult := model.VoucherDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if qResult.Status != common.APIStatus.Ok {
		return qResult
	}
	voucherList := qResult.Data.([]*model.Voucher)

	resp := make([]*model.ViewVoucherResponse, 0)

	for _, voucher := range voucherList {
		maxUsagePerCustomer := *voucher.MaxUsagePerCustomer
		availableQuantity := *voucher.MaxUsage
		customerUsageTotal := mapUserPromotion[voucher.Code].Amount
		isUnlimited := false

		if (voucher.MaxUsage == nil || (voucher.MaxUsage != nil && *voucher.MaxUsage == 0)) && (voucher.MaxUsagePerCustomer == nil || (voucher.MaxUsagePerCustomer != nil && *voucher.MaxUsagePerCustomer == 0)) {
			isUnlimited = true
		} else {
			if voucher.UsageTotal != nil && voucher.MaxUsage != nil && *voucher.MaxUsage > 0 {
				availableQuantity = availableQuantity - *voucher.UsageTotal
				if customerUsageTotal != nil && *customerUsageTotal > 0 {
					availableQuantity = availableQuantity + *customerUsageTotal
				}
			}

			if maxUsagePerCustomer > 0 && (availableQuantity > maxUsagePerCustomer || availableQuantity == 0) {
				availableQuantity = maxUsagePerCustomer
			}
		}

		if !isUnlimited && customerUsageTotal != nil && availableQuantity <= *customerUsageTotal {
			continue
		}

		viewVoucher := setViewData(voucher)
		resp = append(resp, &model.ViewVoucherResponse{
			Voucher:            viewVoucher,
			UserPromotion:      mapUserPromotion[voucher.Code],
			AvailableQuantity:  &availableQuantity,
			CustomerUsageTotal: customerUsageTotal,
			IsUnlimited:        isUnlimited,
		})
	}
	if getTotal {
		total := model.VoucherDB.Count(&query).Total
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Data:    resp,
			Total:   total,
			Message: "Query voucher list of customer successfully",
		}
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Data:    resp,
		Message: "Query voucher list of customer successfully",
	}
}

func updateUsageVoucher(usedVoucher *model.UserPromotion, customerId int64, voucherCode string) *common.APIResponse {
	// update usage in user promotion

	qResult := model.UserPromotionDB.UpdateOneWithOption(bson.M{
		"customer_id":  customerId,
		"voucher_code": voucherCode,
	}, bson.M{
		"$inc": bson.M{"amount": -1},
	})

	if qResult.Status == common.APIStatus.Ok {
		// update usage total in voucher code
		qResult := model.VoucherDB.QueryOne(&model.Voucher{Code: voucherCode})
		if qResult.Status == common.APIStatus.Ok {
			voucher := qResult.Data.([]*model.Voucher)[0]

			// Hoàn trả voucher
			increResp := model.VoucherDB.IncreOne(&model.Voucher{Code: voucher.Code},
				"usage_total", -1,
			)

			if increResp.Status != common.APIStatus.Ok {
				return increResp
			}

			// Thêm code xử lý-> cập nhật status thành ACTIVE
			if voucher.Status != nil && *voucher.Status == enum.VoucherStatus.HIDE {
				if voucher.MaxUsage != nil && *voucher.MaxUsage > 0 && voucher.UsageTotal != nil && (*voucher.UsageTotal-1) < *voucher.MaxUsage {

					var status *enum.VoucherStatusValue = nil
					// nếu còn hạn
					if time.Now().Unix() <= voucher.EndTime.Unix() {
						status = &enum.VoucherStatus.ACTIVE
					}

					upResp := model.VoucherDB.UpdateOne(model.Voucher{
						ID:        voucher.ID,
						VersionNo: voucher.VersionNo,
					}, model.Voucher{
						Status: status,
					},
					)

					if upResp.Status != common.APIStatus.Ok {
						// Cập nhật voucher cần check (needCheck = true -> có worker đi check data)
						t := true
						return model.VoucherDB.UpdateOne(&model.Voucher{Code: voucherCode}, &model.Voucher{NeedCheck: &t})
					}
				}
			}

			return increResp

		}
		return qResult
	}
	return qResult
}

func getDetailVouchers(voucherList []*model.Voucher, customerId int64) []*model.DetailVoucherResponse {
	voucherCodes := make([]string, 0)
	for _, voucher := range voucherList {
		voucherCodes = append(voucherCodes, voucher.Code)
	}
	qUserPromotionResult := model.UserPromotionDB.Query(bson.M{
		"voucher_code": bson.M{
			"$in": voucherCodes,
		},
		"customer_id": customerId,
	}, 0, int64(len(voucherCodes)), nil)

	mapUserPromotion := make(map[string]*model.UserPromotion, 0)
	if qUserPromotionResult.Status == common.APIStatus.Ok {
		for _, userPromo := range qUserPromotionResult.Data.([]*model.UserPromotion) {
			mapUserPromotion[userPromo.VoucherCode] = userPromo
		}
	}

	resp := make([]*model.DetailVoucherResponse, 0)

	for _, voucher := range voucherList {
		resp = append(resp, &model.DetailVoucherResponse{
			Voucher:       voucher,
			UserPromotion: mapUserPromotion[voucher.Code],
		})
	}
	return resp
}

// CheckVoucher func
func CheckVoucher() {

	vouchersNeedActive := model.VoucherDB.Query(
		bson.M{
			"need_check": true,
		},
		0,
		100,
		nil,
	)

	f := false

	if vouchersNeedActive.Status == common.APIStatus.Ok {
		vouchers := vouchersNeedActive.Data.([]*model.Voucher)
		if len(vouchers) > 0 {

			for _, voucher := range vouchers {

				// Nếu voucher đang bị ẩn
				if voucher.Status != nil && *voucher.Status == enum.VoucherStatus.HIDE {
					if voucher.MaxUsage != nil && *voucher.MaxUsage > 0 && voucher.UsageTotal != nil && *voucher.UsageTotal < *voucher.MaxUsage {

						var status *enum.VoucherStatusValue = nil
						// nếu còn hạn
						if time.Now().Unix() <= voucher.EndTime.Unix() {
							status = &enum.VoucherStatus.ACTIVE
						}

						model.VoucherDB.UpdateOne(model.Voucher{
							ID:        voucher.ID,
							VersionNo: voucher.VersionNo,
						}, model.Voucher{
							NeedCheck: &f,
							Status:    status,
						},
						)
					}
				} else {
					// Cập nhật need check = false
					model.VoucherDB.UpdateOne(model.Voucher{
						ID:        voucher.ID,
						VersionNo: voucher.VersionNo,
					}, model.Voucher{
						NeedCheck: &f,
					},
					)

				}
				WarmUpVoucherByCode(voucher.Code, nil)
			}

		}
	}
}

/*
GetListVoucher is func to get list voucher
@author: tuanv.tran
*/
func GetListVoucher(query *model.Voucher, offset, limit int64, getTotal bool, sortField string, sortType int) *common.APIResponse {
	result := model.VoucherDB.Query(query, offset, limit, &primitive.M{sortField: sortType})
	if result.Status != common.APIStatus.Ok {
		return result
	}
	if getTotal {
		result.Total = model.VoucherDB.Count(query).Total
	}
	return result
}

/*
MigrateHashTagVoucher is func to update hash tag
*/
func MigrateHashTagVoucher() *common.APIResponse {
	voucherRes := model.VoucherDB.Query(model.Voucher{}, 0, 0, nil)
	if voucherRes.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Không tìm thấy mã khuyến mãi",
		}
	}

	vouchers := voucherRes.Data.([]*model.Voucher)
	for _, voucher := range vouchers {
		normCodeStr := strings.Replace(utils.NormalizeString(voucher.Code), " ", "-", -1)
		normPromotionNameStr := strings.Replace(utils.NormalizeString(voucher.PromotionName), " ", "-", -1)
		hashTag := fmt.Sprintf("%d-%s-%s", voucher.PromotionID, normCodeStr, normPromotionNameStr)

		updateResult := model.VoucherDB.UpdateOne(&model.Voucher{
			Code: voucher.Code,
		}, &model.Voucher{
			HashTag: hashTag,
		})

		if updateResult.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "Cập nhật mã khuyến mãi thất bại.",
			}
		}

		voucher.HashTag = hashTag
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật mã khuyến mãi thành công.",
		Data:    vouchers,
	}
}

// DeleteAppliedCustomers delete data appliedCustomers in voucher
func DeleteAppliedCustomers(input *model.DeleteAppliedCustomersRequest, AccountID int64) *common.APIResponse {
	voucherResult := model.VoucherDB.QueryOne(&model.Voucher{
		VoucherID: input.VoucherID,
	})

	if voucherResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã khuyến mãi không tồn tại.",
			ErrorCode: "INVALID_VOUCHER_ID_INPUT",
		}
	}

	existedVoucher := voucherResult.Data.([]*model.Voucher)[0]

	appliedCustomers := []int64{}
	existedVoucher.UpdatedBy = AccountID
	existedVoucher.VersionNo = uuid.New().String()
	existedVoucher.AppliedCustomers = &appliedCustomers

	afterOption := options.After

	return model.VoucherDB.UpdateOne(
		&model.Voucher{
			VoucherID: input.VoucherID,
		},
		existedVoucher,
		&options.FindOneAndUpdateOptions{
			ReturnDocument: &afterOption,
		},
	)
}

// GetVoucherUsageHistoryList get list voucher usage history
func GetVoucherUsageHistoryList(query *model.Voucher, offset, limit int64, getTotal bool, tabType string) *common.APIResponse {
	if len(tabType) > 0 {
		if tabType == "NOT_USED" {
			if query.CustomerID != nil {
				voucherCodes := make([]string, 0)
				if query.CustomerID != nil {
					offset2 := int64(0)
					limit2 := int64(1000)
					for {
						userPromotionResp := model.UserPromotionDB.Query(&bson.M{
							"customer_id": *query.CustomerID,
							"$or": []bson.M{
								{"amount": nil},
								{"amount": 0},
							},
							"status": bson.M{"$nin": []string{string(enum.CodeStatus.DELETED), string(enum.CodeStatus.INACTIVE), string(enum.CodeStatus.USED)}},
						}, offset2*limit2, limit2, nil)
						if userPromotionResp.Status != common.APIStatus.Ok {
							break
						}

						for _, userPromotion := range userPromotionResp.Data.([]*model.UserPromotion) {
							voucherCodes = append(voucherCodes, userPromotion.VoucherCode)
						}
						offset2++
					}

				}

				if len(voucherCodes) == 0 {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   "Không tìm thấy mã khuyến mãi.",
						ErrorCode: "VOUCHER_NOT_FOUND",
					}
				}

				query.ComplexQuery = append(query.ComplexQuery, &bson.M{
					"code": bson.M{
						"$in": voucherCodes,
					},
				})
			} else {
				query.ComplexQuery = append(query.ComplexQuery, &bson.M{
					"$or": []bson.M{
						{"usage_total": nil},
						{"usage_total": 0},
					},
				})
			}
		} else if tabType == "USED" {
			if query.CustomerID != nil {
				voucherCodes := make([]string, 0)
				if query.CustomerID != nil {
					offset2 := int64(0)
					limit2 := int64(1000)
					for {
						userPromotionResp := model.UserPromotionDB.Query(&bson.M{
							"customer_id": *query.CustomerID,
							"amount": bson.M{
								"$gt": 0,
							},
						}, offset2*limit2, limit2, nil)
						if userPromotionResp.Status != common.APIStatus.Ok {
							break
						}

						for _, userPromotion := range userPromotionResp.Data.([]*model.UserPromotion) {
							voucherCodes = append(voucherCodes, userPromotion.VoucherCode)
						}
						offset2++
					}
				}

				if len(voucherCodes) == 0 {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   "Không tìm thấy mã khuyến mãi.",
						ErrorCode: "VOUCHER_NOT_FOUND",
					}
				}

				query.ComplexQuery = append(query.ComplexQuery, &bson.M{
					"code": bson.M{
						"$in": voucherCodes,
					},
				})
			} else {
				query.ComplexQuery = append(query.ComplexQuery, &bson.M{
					"usage_total": &bson.M{
						"$exists": true,
						"$gt":     0,
					},
				})
			}
		}
	}

	result := model.VoucherDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if result.Status != common.APIStatus.Ok {
		return result
	}
	if getTotal {
		result.Total = model.VoucherDB.Count(query).Total
	}
	return result
}

// GetVoucherBySku is func to get voucher by sku
func GetVoucherBySku(acc *model.Account, skus []string, getVoucherInfo bool, offset, limit int, getTotal bool) *common.APIResponse {
	type respData struct {
		Sku         string                      `json:"sku"`
		ProductCode string                      `json:"productCode"`
		Vouchers    []*model.VoucherViewWebOnly `json:"vouchers,omitempty"`
		HasGift     bool                        `json:"hasGift"`
	}
	resp := make([]*respData, 0)
	productCodes := make([]string, 0)
	//sellerCodes := make([]string, 0)
	mapSku := make(map[string]string, 0)
	mapProduct := make(map[string]string, 0)
	mapSkuGift := make(map[string]bool, 0)

	customer, err := client.Services.Customer.GetCustomerByAccountID(acc.AccountID) // get customer info
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "GET_CUSTOMER_ERROR",
		}
	}
	for _, sku := range skus {
		mapSku[sku] = sku
		tmp := strings.Split(sku, ".")
		if len(tmp) > 0 {
			productCodes = append(productCodes, tmp[1])
			mapProduct[tmp[1]] = sku
		}
	}
	query := model.Voucher{
		ComplexQuery: []*bson.M{
			{
				"ref_product": bson.M{"$ne": nil},
			},
		},
	}

	if len(skus) > 0 {
		query = model.Voucher{
			ComplexQuery: []*bson.M{
				{
					"$or": []*bson.M{
						{
							"ref_product": bson.M{"$in": skus},
						},
						{
							"ref_product": bson.M{"$in": productCodes},
						},
					},
				},
			},
		}
	}
	now := time.Now()
	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
		"public_time": bson.M{"$lte": now},
		"end_time":    bson.M{"$gte": now},
		"status":      "ACTIVE",
		"apply_type":  enum.ApplyType.AUTO,
	})

	regions := make([]*bson.M, 0)
	regions = append(regions, &bson.M{
		"region_scope_map.ALL": true,
	})
	regions = append(regions, &bson.M{
		"region_scope_map": nil,
	})
	regions = append(regions, &bson.M{
		fmt.Sprintf("region_scope_map.%s", customer.ProvinceCode): true,
	})
	regionResp := client.LocationClient.GetRegionList([]string{customer.ProvinceCode})
	if regionResp.Status == common.APIStatus.Ok {
		for _, region := range regionResp.Data {
			regions = append(regions, &bson.M{
				fmt.Sprintf("region_scope_map.%s", region.Code): true,
			})
		}
	}
	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
		"$or": regions,
	})

	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
		"$or": []*bson.M{
			{
				"level_scope_map.ALL": true,
			},
			{
				"level_scope_map": nil,
			},
			{
				fmt.Sprintf("level_scope_map.%s", customer.Level): true,
			},
		},
	})
	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
		"$or": []*bson.M{
			{
				"customer_scope_map.ALL": true,
			},
			{
				"customer_scope_map": nil,
			},
			{
				fmt.Sprintf("customer_scope_map.%s", customer.Scope): true,
			},
		},
	})
	qVoucher := model.VoucherCacheDB.Query(query, 0, 0, &primitive.M{"priority": -1})
	mapVoucher := make(map[string][]*model.VoucherViewWebOnly)
	voucherCodes := make([]string, 0)
	vouchers := make([]*model.Voucher, 0)
	if qVoucher.Status == common.APIStatus.Ok {
		for _, voucher := range qVoucher.Data.([]*model.Voucher) {
			voucherCodes = append(voucherCodes, voucher.Code)
			vouchers = append(vouchers, voucher)
		}
	}
	mapUseVoucher := make(map[string]bool)
	mapNotUseVoucher := make(map[string]bool)
	qUseVoucher := model.UserPromotionCacheDB.Query(model.UserPromotion{CustomerID: customer.CustomerID, ComplexQuery: []*bson.M{
		{
			"voucher_code": bson.M{"$in": voucherCodes},
		},
	}}, 0, 0, nil)
	if qUseVoucher.Status == common.APIStatus.Ok {
		for _, use := range qUseVoucher.Data.([]*model.UserPromotion) {
			if use.Status != nil && *use.Status != enum.CodeStatus.DELETED {
				mapUseVoucher[use.VoucherCode] = true
			}
			if use.Status != nil && *use.Status == enum.CodeStatus.INACTIVE {
				mapNotUseVoucher[use.VoucherCode] = true
			}
		}
	}
	for _, voucher := range vouchers {
		if voucher.CustomerApplyType == enum.CustomerApplyType.MANY && !mapUseVoucher[voucher.Code] {
			continue
		}
		if voucher.CustomerApplyType == enum.CustomerApplyType.ALL && mapNotUseVoucher[voucher.Code] {
			continue
		}
		mapCheckExist := make(map[string]bool)
		for _, key := range voucher.RefProduct {
			if len(skus) > 0 {
				value := mapProduct[key]
				if value == "" {
					value = mapSku[key]
				}
				if value != "" {
					mapVoucher[value] = append(mapVoucher[value], setViewData(voucher))
					if len(voucher.Rewards) > 0 && len(voucher.Rewards[0].Gifts) > 0 {
						mapSkuGift[value] = true
					}
				}
			} else {
				if data, ok := mapCheckExist[key]; ok && data {
					continue
				}
				d := respData{}
				strArr := strings.Split(key, ".")
				if len(strArr) > 1 {
					d.Sku = key
				} else {
					d.ProductCode = key
				}

				if len(voucher.Rewards) > 0 && len(voucher.Rewards[0].Gifts) > 0 {
					mapCheckExist[key] = true
					d.HasGift = true
				}
				resp = append(resp, &d)
			}

		}
	}

	if len(skus) > 0 {
		resp = make([]*respData, 0)
		for k, v := range mapVoucher {
			d := respData{
				Sku:     k,
				HasGift: mapSkuGift[k],
			}
			if getVoucherInfo {
				d.Vouchers = v
			}
			resp = append(resp, &d)
			if getVoucherInfo {
				break
			}
		}
	}

	return &common.APIResponse{
		Status: common.APIStatus.Ok,
		Data:   resp,
	}
}

func CreateVoucherByPromotionID(account *model.Account, customerID, promotionID int64, voucherCode string, voucherID int64) *common.APIResponse {
	qPromotion := model.PromotionDB.QueryOne(model.Promotion{
		PromotionID: promotionID,
	})
	if qPromotion.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			Message:   "Không tìm thấy chương trình khuyến mãi",
			ErrorCode: "PROMOTION_NOT_FOUND",
		}
	}

	promotion := qPromotion.Data.([]*model.Promotion)[0]
	voucher := model.Voucher{
		DisplayName:          promotion.Description,
		VoucherID:            0,
		Code:                 "",
		PromotionID:          promotion.PromotionID,
		PromotionName:        promotion.PromotionName,
		ApplyType:            promotion.ApplyType,
		StartTime:            promotion.StartTime,
		EndTime:              promotion.EndTime,
		PublicTime:           promotion.PublicTime,
		MaxUsage:             utils.ParseInt64ToPointer(1),
		MaxUsagePerCustomer:  utils.ParseInt64ToPointer(1),
		MaxAutoApplyCount:    promotion.MaxAutoApplyCount,
		Priority:             promotion.Priority,
		VoucherType:          &enum.VoucherType.PUBLIC,
		VoucherGroupCode:     promotion.VoucherGroupCode,
		Status:               &enum.VoucherStatus.ACTIVE,
		Scopes:               promotion.Scopes,
		OrConditions:         promotion.OrConditions,
		AndConditions:        promotion.AndConditions,
		ConditionDescription: promotion.ConditionDescription,
		CustomerApplyType:    "MANY",
		SystemNote:           "Create by promotion",
		// SystemDisplay:        "BUYMED",
		// PromotionOrganizer:   promotion.PromotionOrganizer,
		// ShortName:            promotion.ShortName,
		// VoucherImage:         promotion.VoucherImage,
		// Tag:                  promotion.Tag,
		// ApplyDiscount:        promotion.ApplyDiscount,
	}
	if promotion.MaxUsage != nil {
		voucher.MaxUsage = promotion.MaxUsage
	}
	if promotion.MaxUsagePerCustomer != nil {
		voucher.MaxUsagePerCustomer = promotion.MaxUsagePerCustomer
	}
	if promotion.VoucherType != nil {
		voucher.VoucherType = promotion.VoucherType
	}
	normCodeStr := strings.Replace(utils.NormalizeString(voucher.Code), " ", "-", -1)
	normDisplayNameStr := strings.Replace(utils.NormalizeString(voucher.DisplayName), " ", "-", -1)
	// normShortNameStr := strings.Replace(utils.NormalizeString(voucher.ShortName), " ", "-", -1)
	// voucher.HashTag = fmt.Sprintf("%d-%s-%s-%s", voucher.PromotionID, normCodeStr, normDisplayNameStr, normShortNameStr)
	voucher.HashTag = fmt.Sprintf("%d-%s-%s", voucher.PromotionID, normCodeStr, normDisplayNameStr)

	if voucherCode != "" && voucherID != 0 {
		voucher.VoucherID, voucher.Code = voucherID, voucherCode
	} else {
		voucher.VoucherID, voucher.Code = model.GenVoucherID()
	}
	if promotion.Rewards != nil {
		voucher.Rewards = *promotion.Rewards
	}
	voucher.HashTag = fmt.Sprintf("%s-%d-%s", utils.NormalizeString(voucher.Code), promotion.PromotionID, utils.NormalizeString(promotion.Description))
	res := model.VoucherDB.Create(&voucher)
	if res.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   "Tạo mã khuyến mãi không thành công!",
			ErrorCode: "SERVER_ERROR",
		}
	}
	resUserVoucher := model.UserPromotionDB.Create(&model.UserPromotion{
		CustomerID:  customerID,
		PromotionID: voucher.PromotionID,
		VoucherCode: voucher.Code,
		Status:      &enum.CodeStatus.ACTIVE,
	})
	if resUserVoucher.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   "Tạo mã khuyến mãi không thành công!",
			ErrorCode: "SERVER_ERROR",
		}
	}
	go func() {
		WarmUpVoucherByCode(voucher.Code, res.Data.([]*model.Voucher)[0])
		WarmUpUserPromotion(voucher.Code, customerID, nil)
	}()
	return res
}

func CreateMultipleVoucher(acc *model.Account, input *model.CreateMMultiVoucherRequest) *common.APIResponse {
	now := time.Now()
	batchCode := model.GenCodeWithTime()

	batch := &model.VoucherImportBatch{
		CreatedTime:     &now,
		LastUpdatedTime: &now,
		BatchCode:       batchCode,
		AccountID:       acc.AccountID,
		AccountFullname: acc.Fullname,
		Username:        acc.Username,
		Status:          &enum.StatusVoucherImportBatch.PROCESSING,
		Total:           len(input.Data),
		Success:         0,
		Failed:          0,
	}

	batchResult := model.VoucherImportBatchDB.Create(batch)
	if batchResult.Status != common.APIStatus.Ok {
		return batchResult
	}

	details := make([]*model.VoucherImportBatchDetail, 0, len(input.Data))
	for index, voucherData := range input.Data {
		detailCode := model.GenCodeWithTime(index + 1)
		voucherJSON, _ := json.Marshal(voucherData)

		detail := &model.VoucherImportBatchDetail{
			CreatedTime:     &now,
			LastUpdatedTime: &now,
			BatchCode:       batchCode,
			DetailCode:      detailCode,
			OrderIndex:      index + 1,
			VoucherData:     string(voucherJSON),
			Status:          &enum.StatusVoucherImportBatch.PENDING,
		}
		details = append(details, detail)
	}

	if len(details) < 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   "Không có dữ liệu để import",
			ErrorCode: "NO_DATA_TO_IMPORT",
		}
	}

	creatBatchDetail := model.VoucherImportBatchDetailDB.CreateMany(details)
	if creatBatchDetail.Status != common.APIStatus.Ok {
		return creatBatchDetail
	}

	query := bson.M{
		"batch_code": batchCode,
	}

	getVoucherData := model.VoucherImportBatchDetailDB.Query(
		query, 0, int64(len(details)), nil,
	)

	if getVoucherData.Status != common.APIStatus.Ok {
		return getVoucherData
	}

	voucherDataBatch := getVoucherData.Data.([]*model.VoucherImportBatchDetail)
	responeVoucher := make([]*model.VoucherImportBatchDetail, 0)

	for _, data := range voucherDataBatch {
		voucherData := data.VoucherData
		var updateField *model.VoucherImportBatchDetail
		var voucher model.Voucher
		updateField.LastUpdatedTime = &now
		err := json.Unmarshal([]byte(voucherData), &voucher)
		if err != nil {
			updateField.Status = &enum.StatusVoucherImportBatch.FAILED
			updateField.Response = "DATA_INVALID"
		}
		
		responeVoucher = append(responeVoucher, updateField)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Import thành công",
		Data:    batchCode,
	}

}
