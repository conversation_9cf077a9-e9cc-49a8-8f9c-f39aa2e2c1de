package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/buymed.th/marketplace/promotion/client"
	"gitlab.com/buymed.th/marketplace/promotion/model"
	"gitlab.com/buymed.th/marketplace/promotion/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

/*
CreateVoucherHistory is func to create voucher history
@author: tuanv.tran
*/
func CreateVoucherHistory(in *model.VoucherHistory) *common.APIResponse {
	return model.VoucherHistoryDB.Create(in)
}

/*
GetListVoucherHistory is func to get list voucher history
@author: tuanv.tran
*/
func GetListVoucherHistory(query *model.VoucherHistory, offset, limit int64, getTotal bool) *common.APIResponse {
	result := model.VoucherHistoryDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if result.Status != common.APIStatus.Ok {
		return result
	}
	if getTotal {
		result.Total = model.VoucherHistoryDB.Count(query).Total
	}
	return result
}

func createVoucherHistory(in *model.VoucherHistory, promotionID, voucherID int64, voucherCode string) {
	if in.Voucher == nil {
		qVoucher := model.VoucherDB.QueryOne(&model.Voucher{VoucherID: voucherID, Code: voucherCode})
		if qVoucher.Status == common.APIStatus.Ok {
			in.Voucher = qVoucher.Data.([]*model.Voucher)[0]
		}
	}

	if in.Promotion == nil {
		qPromotion := model.PromotionDB.QueryOne(&model.Promotion{PromotionID: promotionID})
		if qPromotion.Status == common.APIStatus.Ok {
			in.Promotion = qPromotion.Data.([]*model.Promotion)[0]
		}
	}
	model.VoucherHistoryDB.Create(in)
}

func GetListSelfVoucherHistory(acc *model.Account, query *model.VoucherHistory, offset, limit int64, getTotal bool) *common.APIResponse {
	customer, err := client.Services.Customer.GetCustomerByAccountID(acc.AccountID)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "GET_INFO_FAIL",
		}
	}
	query.CustomerID = customer.CustomerID
	query.Type = enum.VoucherHistoryType.USE

	var complexQuery = []*bson.M{}
	if query.DateFrom != nil {
		complexQuery = append(complexQuery, &bson.M{
			"created_time": bson.M{
				"$gte": query.DateFrom,
			},
		})
	}

	if query.DateTo != nil {
		complexQuery = append(complexQuery, &bson.M{
			"created_time": bson.M{
				"$lte": query.DateTo,
			},
		})
	}

	query.ComplexQuery = complexQuery

	result := model.VoucherHistoryDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if result.Status != common.APIStatus.Ok {
		return result
	}

	respData := make([]*model.VoucherHistoryViewWeb, 0)

	for _, voucherHistory := range result.Data.([]*model.VoucherHistory) {
		viewVoucher := setViewData(voucherHistory.Voucher)
		respData = append(respData, &model.VoucherHistoryViewWeb{
			ID:          voucherHistory.ID,
			CreatedTime: voucherHistory.CreatedTime,
			CustomerID:  voucherHistory.CustomerID,
			OrderID:     voucherHistory.OrderID,
			Usage:       voucherHistory.Usage,
			Type:        voucherHistory.Type,
			Voucher:     viewVoucher,
		})
	}

	if getTotal {
		total := model.VoucherHistoryDB.Count(query).Total
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Data:    respData,
			Total:   total,
			Message: "Query voucher history list of customer successfully",
		}
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Data:    respData,
		Message: "Query voucher history list of customer successfully",
	}
}
