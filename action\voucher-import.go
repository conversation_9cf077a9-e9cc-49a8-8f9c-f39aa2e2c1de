package action

import (
	"encoding/json"
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/buymed.th/marketplace/promotion/model"
	"go.mongodb.org/mongo-driver/bson"
)

// CreateMultipleVoucherBatch - Tạo batch import voucher multiple (>= 100 items)
func CreateMultipleVoucherBatch(acc *model.Account, input *model.CreateMMultiVoucherRequest) *common.APIResponse {
	if len(input.Data) < 100 {
		// Nếu < 100 items, xử lý trực tiếp như cũ
		return CreateMultipleVoucher(acc, input)
	}

	now := time.Now()
	batchCode := model.GenCodeWithTime()
	jobCode := model.GenCodeWithTime()

	// Tạo batch record
	batch := &model.VoucherImportBatch{
		CreatedTime:     &now,
		LastUpdatedTime: &now,
		BatchCode:       batchCode,
		AccountID:       acc.AccountID,
		AccountFullname: acc.Fullname,
		Username:        acc.Username,
		Status:          "PENDING",
		Total:           len(input.Data),
		Success:         0,
		Failed:          0,
		JobCode:         jobCode,
	}

	// Lưu batch vào database
	batchResult := model.VoucherImportBatchDB.Create(batch)
	if batchResult.Status != common.APIStatus.Ok {
		return batchResult
	}

	// Tạo background job để xử lý
	go func() {
		// Tạo chi tiết cho từng voucher
		details := make([]*model.VoucherImportBatchDetail, 0, len(input.Data))
		
		for index, voucherData := range input.Data {
			detailCode := model.GenCodeWithTime(index + 1)
			voucherJSON, _ := json.Marshal(voucherData)
			
			detail := &model.VoucherImportBatchDetail{
				CreatedTime:     &now,
				LastUpdatedTime: &now,
				BatchCode:       batchCode,
				DetailCode:      detailCode,
				JobCode:         jobCode,
				OrderIndex:      index,
				VoucherData:     string(voucherJSON),
				Status:          "PENDING",
			}
			details = append(details, detail)
		}

		// Lưu tất cả details
		if len(details) > 0 {
			model.VoucherImportBatchDetailDB.CreateMany(details)
			
			// Push job vào queue để xử lý
			model.VoucherImportJob.Push(model.VoucherImportJobItem{
				BatchCode: batchCode,
				AccountID: acc.AccountID,
				Username:  acc.Username,
				JobCode:   jobCode,
			}, &job.JobItemMetadata{
				Topic: "default",
				Keys:  []string{"VOUCHER_IMPORT_BATCH", batchCode},
			})
		}
	}()

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Batch import voucher đã được tạo và đang xử lý",
		Data: map[string]interface{}{
			"batchCode": batchCode,
			"total":     len(input.Data),
			"status":    "PENDING",
		},
	}
}

// VoucherImportJobExecutor - Xử lý job import voucher
func VoucherImportJobExecutor(item *job.JobItem) error {
	data, err := bson.Marshal(item.Data)
	if err != nil {
		return err
	}
	
	var jobItem model.VoucherImportJobItem
	err = bson.Unmarshal(data, &jobItem)
	if err != nil {
		return err
	}
	
	return ProcessVoucherImportBatch(&jobItem)
}

// ProcessVoucherImportBatch - Xử lý batch import voucher
func ProcessVoucherImportBatch(jobItem *model.VoucherImportJobItem) error {
	startTime := time.Now()
	
	// Update batch status thành PROCESSING
	model.VoucherImportBatchDB.UpdateOne(&model.VoucherImportBatch{
		BatchCode: jobItem.BatchCode,
	}, &model.VoucherImportBatch{
		Status:          "PROCESSING",
		LastUpdatedTime: &startTime,
	})

	// Lấy tất cả details của batch này
	detailsResult := model.VoucherImportBatchDetailDB.Query(&model.VoucherImportBatchDetail{
		BatchCode: jobItem.BatchCode,
		Status:    "PENDING",
	}, 0, 0, nil)

	if detailsResult.Status != common.APIStatus.Ok {
		return fmt.Errorf("failed to get batch details: %s", detailsResult.Message)
	}

	details := detailsResult.Data.([]*model.VoucherImportBatchDetail)
	successCount := 0
	failedCount := 0

	// Xử lý từng voucher
	for _, detail := range details {
		// Update detail status thành PROCESSING
		model.VoucherImportBatchDetailDB.UpdateOne(&model.VoucherImportBatchDetail{
			DetailCode: detail.DetailCode,
		}, &model.VoucherImportBatchDetail{
			Status:          "PROCESSING",
			LastUpdatedTime: &startTime,
		})

		// Parse voucher data
		var voucher model.Voucher
		err := json.Unmarshal([]byte(detail.VoucherData), &voucher)
		if err != nil {
			updateVoucherImportDetail(detail.DetailCode, "FAILED", "", err.Error(), "JSON_PARSE_ERROR")
			failedCount++
			continue
		}

		// Tạo voucher
		result := CreateSingleVoucher(&voucher)
		
		if result.Status == common.APIStatus.Ok {
			// Thành công
			voucherCode := ""
			if createdVoucher, ok := result.Data.(*model.Voucher); ok {
				voucherCode = createdVoucher.Code
			}
			updateVoucherImportDetail(detail.DetailCode, "SUCCESS", voucherCode, "", "")
			successCount++
		} else {
			// Thất bại
			updateVoucherImportDetail(detail.DetailCode, "FAILED", "", result.Message, result.ErrorCode)
			failedCount++
		}
	}

	// Update batch với kết quả cuối cùng
	endTime := time.Now()
	processingTime := int(endTime.Sub(startTime).Milliseconds())
	
	finalStatus := "COMPLETED"
	if failedCount > 0 && successCount == 0 {
		finalStatus = "FAILED"
	} else if failedCount > 0 {
		finalStatus = "PARTIAL_SUCCESS"
	}

	model.VoucherImportBatchDB.UpdateOne(&model.VoucherImportBatch{
		BatchCode: jobItem.BatchCode,
	}, &model.VoucherImportBatch{
		Status:          finalStatus,
		Success:         successCount,
		Failed:          failedCount,
		ProcessingTime:  processingTime,
		CompletedTime:   &endTime,
		LastUpdatedTime: &endTime,
	})

	return nil
}

// updateVoucherImportDetail - Cập nhật kết quả xử lý detail
func updateVoucherImportDetail(detailCode, status, voucherCode, errorMessage, errorCode string) {
	now := time.Now()
	updateData := &model.VoucherImportBatchDetail{
		Status:          status,
		LastUpdatedTime: &now,
		ErrorMessage:    errorMessage,
		ErrorCode:       errorCode,
	}
	
	if status == "SUCCESS" {
		updateData.VoucherCode = voucherCode
		updateData.CompletedTime = &now
	}
	
	model.VoucherImportBatchDetailDB.UpdateOne(&model.VoucherImportBatchDetail{
		DetailCode: detailCode,
	}, updateData)
}

// CreateSingleVoucher - Tạo một voucher đơn lẻ (tách từ logic cũ)
func CreateSingleVoucher(voucher *model.Voucher) *common.APIResponse {
	// Logic tạo voucher đơn lẻ - có thể tách từ CreateMultipleVoucher hiện tại
	// Hoặc gọi lại CreateMultipleVoucher với 1 item
	singleRequest := &model.CreateMMultiVoucherRequest{
		Data: []*model.Voucher{voucher},
	}
	
	// Tạm thời gọi lại function cũ với 1 item
	// Bạn có thể tối ưu hóa sau bằng cách tách logic tạo voucher đơn lẻ
	return CreateMultipleVoucher(nil, singleRequest)
}

// GetVoucherImportBatchList - Lấy danh sách batch import
func GetVoucherImportBatchList(acc *model.Account, query *model.VoucherImportBatch, offset, limit int64, getTotal bool) *common.APIResponse {
	// Chỉ cho phép xem batch của chính mình (trừ admin)
	if acc.AccountID != 0 { // Giả sử admin có AccountID = 0
		query.AccountID = acc.AccountID
	}
	
	result := model.VoucherImportBatchDB.Query(query, offset, limit, &bson.M{"created_time": -1})
	if result.Status != common.APIStatus.Ok {
		return result
	}
	
	if getTotal {
		result.Total = model.VoucherImportBatchDB.Count(query).Total
	}
	
	return result
}

// GetVoucherImportBatchDetail - Lấy chi tiết batch import
func GetVoucherImportBatchDetail(acc *model.Account, batchCode string, offset, limit int64, getTotal bool) *common.APIResponse {
	// Kiểm tra quyền truy cập batch
	batchResult := model.VoucherImportBatchDB.QueryOne(&model.VoucherImportBatch{
		BatchCode: batchCode,
	})
	
	if batchResult.Status != common.APIStatus.Ok {
		return batchResult
	}
	
	batch := batchResult.Data.([]*model.VoucherImportBatch)[0]
	if acc.AccountID != 0 && batch.AccountID != acc.AccountID {
		return &common.APIResponse{
			Status:    common.APIStatus.Unauthorized,
			Message:   "Không có quyền truy cập batch này",
			ErrorCode: "ACCESS_DENIED",
		}
	}
	
	// Lấy chi tiết
	query := &model.VoucherImportBatchDetail{
		BatchCode: batchCode,
	}
	
	result := model.VoucherImportBatchDetailDB.Query(query, offset, limit, &bson.M{"order_index": 1})
	if result.Status != common.APIStatus.Ok {
		return result
	}
	
	if getTotal {
		result.Total = model.VoucherImportBatchDetailDB.Count(query).Total
	}
	
	return result
}
