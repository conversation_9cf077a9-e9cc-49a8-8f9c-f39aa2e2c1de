package action

import (
	"math/rand"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/buymed.th/marketplace/promotion/model"
	"go.mongodb.org/mongo-driver/bson"
)

func GetGameQuestions(offset int64, limit int64, getTotal bool, code string, getByStatus *bool) *common.APIResponse {
	if limit == 0 {
		limit = 20
	}
	if offset < 0 {
		offset = 0
	}

	query := bson.M{
		"is_deleted": bson.M{
			"$ne": true,
		},
	}
	if code != "" {
		// get one by code
		query["code"] = code
		limit = 1
	}
	if getByStatus != nil {
		query["is_enabled"] = *getByStatus
	}

	result := model.GameQuestionDB.Query(&query, offset, limit, nil)
	if result.Status == common.APIStatus.Ok && getTotal {
		result.Total = model.GameQuestionDB.Count(&query).Total
	}
	return result
}

func ValidateQuestion(question model.GameQuestion) *common.APIResponse {
	if len(question.Answers) < 4 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Question must have 4 answers",
		}
	}
	if question.Question == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Question is required",
		}
	}
	hasCorrectAnswer := false
	for _, answer := range question.Answers {
		if answer.IsCorrect != nil && *answer.IsCorrect {
			hasCorrectAnswer = true
			break
		}
	}
	if !hasCorrectAnswer {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Question must have at least 1 correct answer",
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Question is valid",
	}
}

func CreateGameQuestion(question model.GameQuestion) *common.APIResponse {
	isQuestionValid := ValidateQuestion(question)
	if isQuestionValid.Status != common.APIStatus.Ok {
		return isQuestionValid
	}

	_, code := model.GetGameQuestionID()
	question.Code = code
	for i := range question.Answers {
		question.Answers[i].AnswerID, _ = model.GetGameAnswerID()
	}

	return model.GameQuestionDB.Create(question)
}

func UpdateGameQuestion(question model.GameQuestion) *common.APIResponse {
	if question.Code == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Code is required",
		}
	}

	isQuestionValid := ValidateQuestion(question)
	if isQuestionValid.Status != common.APIStatus.Ok {
		return isQuestionValid
	}
	question.IsDeleted = nil

	return model.GameQuestionDB.UpdateOne(&bson.M{
		"code": question.Code,
	}, question)
}

func DeleteGameQuestion(code string) *common.APIResponse {
	if code == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Code is required",
		}
	}

	return model.GameQuestionDB.UpdateOne(bson.M{"code": code}, bson.M{"is_deleted": true})
}

func shuffleQuestions(arr []model.GameAnswer) []model.GameAnswer {
	rand.Seed(time.Now().UnixNano()) // Seed the random number generator
	rand.Shuffle(len(arr), func(i, j int) {
		arr[i], arr[j] = arr[j], arr[i]
	})
	return arr
}

func GetGameQuestionSpin(wheelCode string) *common.APIResponse {
	// check lucky-wheel is required question or not
	if wheelCode != "" {
		luckyWheelResp := model.LuckyWheelDB.QueryOne(bson.M{"code": wheelCode})
		if luckyWheelResp.Status != common.APIStatus.Ok {
			return luckyWheelResp
		}
		luckyWheels, ok := luckyWheelResp.Data.([]*model.LuckyWheel)
		if !ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Cannot parse lucky wheel data",
			}
		}
		if len(luckyWheels) == 0 {
			return &common.APIResponse{
				Status:  common.APIStatus.NotFound,
				Message: "Lucky wheel not found",
			}
		}

		if luckyWheels[0].IsQuestionRequired == nil || !*luckyWheels[0].IsQuestionRequired {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "This lucky wheel does not require question",
			}
		}
	}

	questions := []*model.GameQuestion{}
	query := []bson.M{
		{
			"$match": bson.M{
				"is_deleted": bson.M{
					"$ne": true,
				},
				"is_enabled": true,
			},
		},
		{"$sample": bson.M{"size": 1}},
	}
	queryResult := model.GameQuestionDB.Aggregate(query, &questions)
	if queryResult.Status != common.APIStatus.Ok {
		return queryResult
	}

	if len(questions) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "No question found",
		}
	}

	// remove correct answer
	for i := range questions {
		for j := range questions[i].Answers {
			questions[i].Answers[j].IsCorrect = nil
		}
	}
	questions[0].Answers = shuffleQuestions(questions[0].Answers)

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Get game question spin success",
		Data:    questions,
	}
}
