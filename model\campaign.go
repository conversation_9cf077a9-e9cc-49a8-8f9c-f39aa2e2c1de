package model

import (
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/mongo/options"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/buymed.th/marketplace/promotion/model/enum"
	"gitlab.com/buymed.th/marketplace/promotion/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type Campaign struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	CreatedBy       int64              `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	UpdatedBy       int64              `json:"updatedBy,omitempty" bson:"updated_by,omitempty"`

	CampaignID   int64                    `json:"campaignID,omitempty" bson:"campaign_id,omitempty"`
	CampaignCode string                   `json:"campaignCode,omitempty" bson:"campaign_code,omitempty"`
	Banner       string                   `json:"banner,omitempty" bson:"banner,omitempty"`
	CampaignName string                   `json:"campaignName,omitempty" bson:"campaign_name,omitempty"`
	Description  string                   `json:"description,omitempty" bson:"description,omitempty"`
	CampaignType enum.CampaignValueType   `json:"campaignType,omitempty" bson:"campaign_type,omitempty"`
	Status       enum.CampaignStatusValue `json:"status,omitempty" bson:"status,omitempty"`

	// new update 27-10-2021
	RegistrationStartTime time.Time                    `json:"registrationStartTime,omitempty" bson:"registration_start_time,omitempty"`
	RegistrationEndTime   time.Time                    `json:"registrationEndTime,omitempty" bson:"registration_end_time,omitempty"`
	StartTime             time.Time                    `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime               time.Time                    `json:"endTime,omitempty" bson:"end_time,omitempty"`
	FlashSaleTimes        []*FlashSaleTime             `json:"flashSaleTimes,omitempty" bson:"flash_sale_times,omitempty"`
	FlashSaleTimesView    []*CampaignFlashSaleTimeItem `json:"flashSaleTimesView,omitempty" bson:"flash_sale_times_view,omitempty"`
	Reward                *CampaignReward              `json:"reward,omitempty" bson:"reward,omitempty"`

	SaleType enum.CampaignSaleValueType `json:"saleType,omitempty" bson:"sale_type,omitempty"`
	Fulfill  *float64                   `json:"fulfill,omitempty" bson:"fulfill,omitempty"`

	// condition
	SellerCodes    *[]string `json:"sellerCodes,omitempty" bson:"seller_codes,omitempty"`
	CustomerScopes *[]string `json:"customerScopes,omitempty" bson:"customer_scopes,omitempty"`
	Regions        *[]string `json:"regions,omitempty" bson:"regions,omitempty"`

	//
	TotalProduct      *int64 `json:"totalProduct,omitempty" bson:"total_product,omitempty"`
	TotalSeller       *int64 `json:"totalSeller,omitempty" bson:"total_seller,omitempty"`
	TotalRevenue      *int64 `json:"totalRevenue,omitempty" bson:"total_revenue,omitempty"`
	TotalSoldQuantity *int64 `json:"totalSoldQuantity,omitempty" bson:"total_sold_quantity,omitempty"`

	IsActive           *bool     `json:"isActive,omitempty" bson:"is_active,omitempty"`
	NeedCheck          *string   `json:"needCheckSync,omitempty" bson:"need_check_sync,omitempty"`
	Version            string    `json:"version,omitempty" bson:"version,omitempty"`
	ChildCampaignCodes *[]string `json:"childCampaignCodes,omitempty" bson:"child_campaign_codes,omitempty"`
	ViewType           *string   `json:"viewType,omitempty" bson:"view_type,omitempty"`

	Slug             string              `json:"slug,omitempty" bson:"slug,omitempty"`
	HashTag          string              `json:"-" bson:"hash_tag,omitempty"`
	ComplexQuery     []*bson.M           `json:"-" bson:"$and,omitempty"`
	CampaignSaleTime []*CampaignSaleTime `json:"campaignSaleTime,omitempty" bson:"-"`
	IsJoined         *bool               `json:"isJoined,omitempty" bson:"-"`

	ProcessingTimeFrom *time.Time `json:"processingTimeFrom,omitempty" bson:"-"`
	ProcessingTimeTo   *time.Time `json:"processingTimeTo,omitempty" bson:"-"`
}

type FlashSaleTime struct {
	Code      string                       `json:"code,omitempty" bson:"code,omitempty"`
	Name      string                       `json:"name,omitempty" bson:"name,omitempty"`
	StartTime time.Time                    `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime   *time.Time                   `json:"endTime,omitempty" bson:"end_time,omitempty"`
	Detail    []*CampaignFlashSaleTimeItem `json:"detail,omitempty" bson:"detail,omitempty"`
}

//	type FlashSaleTimeItem struct {
//		Code          string          `json:"code,omitempty" bson:"code,omitempty"`
//		Ref           string          `json:"ref,omitempty" bson:"ref,omitempty"`
//		Name          string          `json:"name,omitempty" bson:"name,omitempty"`
//		StartTime     *time.Time      `json:"startTime,omitempty" bson:"start_time,omitempty"`
//		EndTime       *time.Time      `json:"endTime,omitempty" bson:"end_time,omitempty"`
//		ProductIDs    *[]int64        `json:"productIDs,omitempty" bson:"product_ids,omitempty"`
//		CategoryCodes *[]string       `json:"categoryCodes,omitempty" bson:"category_codes,omitempty"`
//		Hour          []time.Duration `json:"-" bson:"hour,omitempty"`
//		Day           int             `json:"-" bson:"day,omitempty"`
//	}
type CampaignReward struct {
	PercentageDiscount *float64 `json:"percentageDiscount,omitempty" bson:"percentage_discount,omitempty"`
	AbsoluteDiscount   *float64 `json:"absoluteDiscount,omitempty" bson:"absolute_discount,omitempty"`
	MaxDiscount        *float64 `json:"maxDiscount,omitempty" bson:"max_discount,omitempty"`
}

var CampaignDB = &db.Instance{
	ColName:        "campaign",
	TemplateObject: &Campaign{},
}

var CampaignReaderDB = &db.Instance{
	ColName:        "campaign",
	TemplateObject: &Campaign{},
}

var CampaignCacheDB = &db.Instance{
	ColName:        "campaign",
	TemplateObject: &Campaign{},
}

func InitCampaignReaderModel(s *mongo.Database) {
	CampaignReaderDB.ApplyDatabase(s)
}

// InitCampaignModel is func init model
func InitCampaignModel(dbInst *db.Instance, s *mongo.Database) {
	dbInst.ApplyDatabase(s)

	t := true
	dbInst.CreateIndex(bson.D{
		primitive.E{Key: "status", Value: 1},
		primitive.E{Key: "is_active", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	// dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "campaign_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })

	// dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "campaign_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })

	// dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "status", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "total_product", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "slug", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "seller_codes", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "campaign_type", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "registration_end_time", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "need_check", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "start_time", Value: 1},
	// 	primitive.E{Key: "end_time", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
}

// InitCampaignCacheModel is func init model
func InitCampaignCacheModel(dbInst *db.Instance, s *mongo.Database) {
	dbInst.ApplyDatabase(s)

	t := true
	dbInst.CreateIndex(bson.D{
		primitive.E{Key: "campaign_id", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
		Unique:     &t,
	})

	dbInst.CreateIndex(bson.D{
		primitive.E{Key: "campaign_code", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
		Unique:     &t,
	})

	dbInst.CreateIndex(bson.D{
		primitive.E{Key: "status", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	dbInst.CreateIndex(bson.D{
		primitive.E{Key: "total_product", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	dbInst.CreateIndex(bson.D{
		primitive.E{Key: "slug", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	dbInst.CreateIndex(bson.D{
		primitive.E{Key: "seller_codes", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	dbInst.CreateIndex(bson.D{
		primitive.E{Key: "campaign_type", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	dbInst.CreateIndex(bson.D{
		primitive.E{Key: "registration_end_time", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	dbInst.CreateIndex(bson.D{
		primitive.E{Key: "need_check", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	dbInst.CreateIndex(bson.D{
		primitive.E{Key: "start_time", Value: 1},
		primitive.E{Key: "end_time", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})
}

func (c *Campaign) ToHashTag() {
	c.HashTag = fmt.Sprintf("%s,%s,%s,%d", c.CampaignName, utils.NormalizeString(c.CampaignName), c.CampaignCode, c.CampaignID)
}

var CampaignFlashSaleTime = map[string]*CampaignFlashSaleTimeItem{
	"0600-1159": {
		Code: "0600-1159",
		Name: "06h00 - 11h59",
		Hour: []time.Duration{
			time.Duration(6 * time.Hour),
			time.Duration(12*time.Hour - 1*time.Minute),
		},
	},
	"1200-1759": {
		Code: "1200-1759",
		Name: "12h00 - 17h59",
		Hour: []time.Duration{
			time.Duration(12 * time.Hour),
			time.Duration(18*time.Hour - 1*time.Minute),
		},
	},
	"1800-2400": {
		Code: "1800-2400",
		Name: "18h00 - 24h00",
		Hour: []time.Duration{
			time.Duration(18 * time.Hour),
			time.Duration(24 * time.Hour),
		},
	},
}
