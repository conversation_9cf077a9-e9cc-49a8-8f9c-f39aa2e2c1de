package main

import (
	"fmt"
	"os"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/buymed.th/marketplace/promotion/action"
	"gitlab.com/buymed.th/marketplace/promotion/api"
	"gitlab.com/buymed.th/marketplace/promotion/client"
	"gitlab.com/buymed.th/marketplace/promotion/conf"
	"gitlab.com/buymed.th/marketplace/promotion/model"
	"gitlab.com/buymed.th/marketplace/promotion/model/cache"
	"go.mongodb.org/mongo-driver/mongo"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
)

type infoData struct {
	Service     string    `json:"service"`
	Environment string    `json:"environment"`
	Version     string    `json:"version"`
	StartTime   time.Time `json:"startTime"`
}

var globalInfo *infoData

func info(req sdk.APIRequest, res sdk.APIResponder) error {
	return res.Respond(&common.APIResponse{
		Status: common.APIStatus.Ok,
		Data:   []interface{}{globalInfo},
	})
}

func onDBReaderConnected(s *mongo.Database) error {
	model.InitCampaignReaderModel(s)
	model.InitCampaignProductReaderModel(s)
	return nil
}

// onDBConnected function that handle on connected to DB event
func onDBConnected(s *mongo.Database) error {
	fmt.Println("Connected to DB " + s.Name())
	model.InitGamificationModel(s)
	model.InitGamificationDetailModel(s)
	model.InitGamificationResultModel(s)
	model.InitGamificationResultDeletedModel(s)
	model.InitSyncGamificationLogModel(s)
	model.InitPromotionModel(s)
	model.InitUserPromotionModel(s)
	model.InitCampaignModel(model.CampaignDB, s)
	model.InitVoucherModel(s)
	model.InitVoucherHistoryModel(s)
	model.InitIdGenModel(s)
	model.InitGiftSettingModel(s)
	model.InitTicketModel(s)
	model.InitCampaignProductModel(model.CampaignProductDB, s, conf.Config.SkipIndex)
	model.InitCampaignSaleTimeModel(model.CampaignSaleTimeDB, s)
	model.InitCampaignHistoryModel(s)
	model.InitImportResultModel(s)
	model.InitImportResultDetailModel(s)
	model.InitVoucherImportBatchModel(s)
	model.InitVoucherImportBatchDetailModel(s)
	model.InitCheckProductFulfillmentLogModel(s)
	model.InitCheckProductFulfillmentDetailModel(s)
	model.InitTrackingModel(s)
	model.InitSettingModel(s)
	model.InitTrackingEventModel(s)
	model.InitCampaignFlashSaleTimeItemModel(model.CampaignFlashSaleTimeItemDB, s)
	model.InitVoucherGroupTypeModel(s)
	model.InitVoucherGroupConnectionModel(s)
	model.InitGamificationCustomerModel(s)
	model.InitLuckyWheelModel(s)
	model.InitLuckyWheelItemModel(s)
	model.InitCustomerLuckyWheelModel(s)
	model.InitLuckyWheelLogModel(s)
	model.InitGamificationLeaderBoardModel(s)
	model.InitGamificationResultLogModel(s)
	model.InitShareLogModel(s)
	model.InitCheckinConfigModel(s)
	model.InitCheckinLogModel(s)
	model.InitCheckinItemModel(s)
	model.InitCheckinCustomerModel(s)
	model.InitCustomerLuckyWheelLogModel(s)
	model.InitGameQuestionModel(s)
	return nil
}

func onDBLogConnected(s *mongo.Database) error {
	fmt.Println("Connected to DB " + s.Name())

	client.InitClientWithLog(s)
	return nil
}

// onCacheConnected is func handle event connected to db cache
func onCacheConnected(s *mongo.Database) error {
	fmt.Println("Connected to DB " + s.Name())
	model.InitCampaignCacheModel(model.CampaignCacheDB, s)
	model.InitCampaignProductCacheModel(model.CampaignProductCacheDB, s, false)
	model.InitSkuModel(model.SkuCacheDB, s)
	model.InitSkuModel(model.SkuMainCacheDB, s)
	model.InitSkuSaleInfoModel(model.SkuSaleInfoCacheDB, s)
	model.InitVoucherCacheModel(s)
	cache.InitGamificationValueModel(s)
	model.InitUserPromotionCacheModel(s)
	cache.InitFlagModel(s)
	return nil
}

// onJobConnected is func handle event connected to db cache
func onJobConnected(s *mongo.Database) error {
	fmt.Println("Connected to DB " + s.Name())
	model.InitSyncGamificationJob(s, action.SyncGamificationResultTask)
	model.InitCheckProductFulfillmentJob(s, action.CheckProductFulfillmentTask)
	model.InitImportCampaignProductJob(s, action.ImportCampaignProductJob)
	model.InitVoucherImportJob(s, action.VoucherImportJobExecutor)
	model.InitSyncGamificationSingleJob(s, action.SyncGamificationConsumer)
	// model.InitUpdateCampaignTicketStatusJob(s, action.UpdateCampaignTicketStatusConsumer)
	// model.InitImportCampaignTicketJob(s, action.ImportCampaignTicketConsumer)
	model.InitRewardGenJob(s, action.ExecuteGamificationRewardJob)
	model.InitUserVoucherCreationJob(s, action.ExecuteUserVoucherCreateJob)
	model.InitRewardVoucherUpdationJob(s, action.ExecuteRewardVoucherUpdationJob)
	model.InitScoreGamificationJob(s)
	model.InitReScoreGamificationJob(s)
	return nil
}

func main() {

	serviceName := "Marketplace.Promotion"

	globalInfo = &infoData{
		Service:     serviceName,
		Version:     os.Getenv("version"),
		Environment: conf.Config.Env,
		StartTime:   time.Now(),
	}

	// setup new app
	var app = sdk.NewApp(serviceName + " service")

	configMap, _ := app.GetConfigFromEnv()
	// if err != nil {
	// 	fmt.Println("Parse config error: " + err.Error())
	// 	fmt.Println("Exiting app ...")
	// 	return
	// }
	startAppWithConfig(app, configMap)
}

func startAppWithConfig(app *sdk.App, configMap map[string]string) {

	// DB main
	// marketplaceProductDbAddr := configMap["marketplaceProductDbAddr"]
	// if marketplaceProductDbAddr == "" {
	// 	marketplaceProductDbAddr = configMap["dbAddr"]
	// }
	// marketplaceProductDbUser := configMap["marketplaceProductDbUser"]
	// if marketplaceProductDbUser == "" {
	// 	marketplaceProductDbUser = configMap["dbUser"]
	// }
	// marketplaceProductDbPass := configMap["marketplaceProductDbPass"]
	// if marketplaceProductDbPass == "" {
	// 	marketplaceProductDbPass = configMap["dbPassword"]
	// }
	// app.SetupDBClient(db.Configuration{
	// 	Address:     marketplaceProductDbAddr,
	// 	Username:    marketplaceProductDbUser,
	// 	Password:    marketplaceProductDbPass,
	// 	DBName:      conf.Config.MainDBName,
	// 	AuthDB:      conf.Config.MainAuthDB,
	// 	DoWriteTest: true,
	// }, onDBConnected)

	// logAddr := configMap["logAddr"]
	// if logAddr == "" {
	// 	logAddr = configMap["dbAddr"]
	// }
	// logUser := configMap["logUser"]
	// if logUser == "" {
	// 	logUser = configMap["dbUser"]
	// }
	// logPassword := configMap["logPassword"]
	// if logPassword == "" {
	// 	logPassword = configMap["dbPassword"]
	// }

	// app.SetupDBClient(db.Configuration{
	// 	Address:     logAddr,
	// 	Username:    logUser,
	// 	Password:    logPassword,
	// 	DBName:      conf.Config.LogDBName,
	// 	AuthDB:      conf.Config.LogAuthDB,
	// 	DoWriteTest: true,
	// }, onDBLogConnected)

	// jobAddr := configMap["clusterQueueDbAddr"]
	// if jobAddr == "" {
	// 	jobAddr = configMap["dbAddr"]
	// }

	// jobUser := configMap["queueUser"]

	// if jobUser == "" {
	// 	jobUser = configMap["dbUser"]
	// }

	// jobPassword := configMap["queuePassword"]

	// if jobPassword == "" {
	// 	jobPassword = configMap["dbPassword"]
	// }

	// // if conf.Config.Env == "dev" || conf.Config.Env == "stg" {
	// // 	jobAddr = configMap["dbAddr"]
	// // 	jobUser = configMap["dbUser"]
	// // 	jobPassword = configMap["dbPassword"]
	// // }

	// app.SetupDBClient(db.Configuration{
	// 	Address:     jobAddr,
	// 	Username:    jobUser,
	// 	Password:    jobPassword,
	// 	DBName:      conf.Config.JobDBName,
	// 	AuthDB:      conf.Config.JobAuthDB,
	// 	DoWriteTest: true,
	// }, onJobConnected)

	// // setup second database
	// app.SetupDBClient(db.Configuration{
	// 	Address:     configMap["cacheAddr"],
	// 	Username:    configMap["cacheUser"],
	// 	Password:    configMap["cachePassword"],
	// 	DBName:      conf.Config.CacheDBName,
	// 	AuthDB:      "admin",
	// 	DoWriteTest: true,
	// }, onCacheConnected)

	mainDBConf := conf.Config.MainDBConf
	app.SetupDBClient(db.Configuration{
		Address:     mainDBConf.Addr,
		Username:    mainDBConf.User,
		Password:    mainDBConf.Password,
		DBName:      mainDBConf.DatabaseName,
		AuthDB:      mainDBConf.Auth,
		DoWriteTest: !mainDBConf.Readonly,
	}, onDBConnected)

	app.SetupDBClient(db.Configuration{
		Address:            mainDBConf.Addr,
		Username:           mainDBConf.User,
		Password:           mainDBConf.Password,
		DBName:             mainDBConf.DatabaseName,
		AuthDB:             mainDBConf.Auth,
		DoWriteTest:        !mainDBConf.Readonly,
		SecondaryPreferred: true,
	}, onDBReaderConnected)

	logDBConf := conf.Config.LogDBConf
	app.SetupDBClient(db.Configuration{
		Address:     logDBConf.Addr,
		Username:    logDBConf.User,
		Password:    logDBConf.Password,
		DBName:      logDBConf.DatabaseName,
		AuthDB:      logDBConf.Auth,
		DoWriteTest: !logDBConf.Readonly,
	}, onDBLogConnected)

	jobDBConf := conf.Config.JobDBConf
	app.SetupDBClient(db.Configuration{
		Address:     jobDBConf.Addr,
		Username:    jobDBConf.User,
		Password:    jobDBConf.Password,
		DBName:      jobDBConf.DatabaseName,
		AuthDB:      jobDBConf.Auth,
		DoWriteTest: !jobDBConf.Readonly,
	}, onJobConnected)

	// setup second database
	cacheDbConf := conf.Config.CacheDBConf
	app.SetupDBClient(db.Configuration{
		Address:     cacheDbConf.Addr,
		Username:    cacheDbConf.User,
		Password:    cacheDbConf.Password,
		DBName:      cacheDbConf.DatabaseName,
		AuthDB:      cacheDbConf.Auth,
		DoWriteTest: !cacheDbConf.Readonly,
	}, onCacheConnected)

	// setup API Server
	protocol := os.Getenv("protocol")
	if protocol == "" {
		protocol = "THRIFT"
	}

	// Init Validator
	model.InitCustomValidator()

	var server, _ = app.SetupAPIServer(protocol)
	_ = server.SetHandler(common.APIMethod.GET, "/api-info", info)

	_ = server.SetHandler(common.APIMethod.GET, "/gamification/list", api.GetGamificationList)
	_ = server.SetHandler(common.APIMethod.GET, "/me/gamification/list", api.GetGamificationSelfList)
	_ = server.SetHandler(common.APIMethod.GET, "/me/gamification-result/list", api.GetGamificationSelfResultList)
	_ = server.SetHandler(common.APIMethod.GET, "/me/gamification", api.GetSelfSingleGamification)
	_ = server.SetHandler(common.APIMethod.POST, "/me/gamification/join", api.SubmitJoinGamification)
	_ = server.SetHandler(common.APIMethod.GET, "/gamification", api.GetSingleGamification)
	_ = server.SetHandler(common.APIMethod.POST, "/gamification", api.CreateGamification)
	_ = server.SetHandler(common.APIMethod.PUT, "/gamification", api.UpdateGamification)
	_ = server.SetHandler(common.APIMethod.POST, "/gamification-result/sync", api.GamificationSync)
	_ = server.SetHandler(common.APIMethod.POST, "/gamification-result/sync-from-order", api.SyncGamificationFromOrder)
	_ = server.SetHandler(common.APIMethod.GET, "/gamification-log/list", api.GamificationLogGetList)
	_ = server.SetHandler(common.APIMethod.GET, "/gamification-result/list", api.GetGamificationResultList)
	_ = server.SetHandler(common.APIMethod.GET, "/gamification-by-seller/list", api.GetGamificationListBySellerCode)
	_ = server.SetHandler(common.APIMethod.PUT, "/tool/gamification/sync-data", api.ToolUpdateGamification)

	_ = server.SetHandler(common.APIMethod.POST, "/gamification/detail-reward", api.GenerateGamificationReward)
	_ = server.SetHandler(common.APIMethod.GET, "/gamification/customer/list", api.GamificationCustomerGetList)
	_ = server.SetHandler(common.APIMethod.POST, "/gamification/customer", api.AddGamificationCustomer)
	_ = server.SetHandler(common.APIMethod.PUT, "/gamification/customer", api.UpdateGamificationCustomerStatus)
	_ = server.SetHandler(common.APIMethod.DELETE, "/gamification/customer", api.DeleteGamificationCustomer)

	_ = server.SetHandler(common.APIMethod.POST, "/gamification-detail", api.CreateGamificationDetail)
	_ = server.SetHandler(common.APIMethod.PUT, "/gamification-detail", api.UpdateGamificationDetail)
	_ = server.SetHandler(common.APIMethod.GET, "/gamification-detail/list", api.GetListGamificationDetail)
	_ = server.SetHandler(common.APIMethod.GET, "/gamification-detail", api.GetSingleGamification)

	_ = server.SetHandler(common.APIMethod.POST, "/gamification-score", api.PushGamificationScore)
	_ = server.SetHandler(common.APIMethod.POST, "/gamification-rescore", api.PushGamificationReScore)

	_ = server.SetHandler(common.APIMethod.GET, "/gamification-leaderboard/list", api.GetGamiLeaderboardsList)
	_ = server.SetHandler(common.APIMethod.GET, "/gamification-result-log/list", api.GetListGamificationResultLog)
	_ = server.SetHandler(common.APIMethod.DELETE, "/gamification-result-log", api.DeleteKeyGamificationResultLog)
	_ = server.SetHandler(common.APIMethod.GET, "/sync-customer-join-number", api.SyncCustomerJoinGamification)

	// gamification spinner
	_ = server.SetHandler(common.APIMethod.GET, "/lucky-wheel", api.GetSingleLuckyWheel)
	_ = server.SetHandler(common.APIMethod.POST, "/lucky-wheel", api.CreateLuckyWheel)
	_ = server.SetHandler(common.APIMethod.PUT, "/lucky-wheel", api.UpdateLuckyWheel)
	_ = server.SetHandler(common.APIMethod.GET, "/lucky-wheel/list", api.GetLuckyWheelList)
	_ = server.SetHandler(common.APIMethod.POST, "/lucky-wheel/spin", api.PostSpinLuckyWheel)
	_ = server.SetHandler(common.APIMethod.GET, "/lucky-wheel-item/list", api.GetLuckyWheelItemList)
	_ = server.SetHandler(common.APIMethod.POST, "/lucky-wheel-item", api.CreateLuckyWheelItem)
	_ = server.SetHandler(common.APIMethod.PUT, "/lucky-wheel-item", api.UpdateLuckyWheelItem)
	_ = server.SetHandler(common.APIMethod.GET, "/me/lucky-wheel", api.GetSelfLuckyWheel)
	_ = server.SetHandler(common.APIMethod.GET, "/me/lucky-wheel-log/list", api.GetSelfLuckyWheelLog)
	_ = server.SetHandler(common.APIMethod.GET, "/lucky-wheel-log/list", api.GetLuckyWheelLog)
	_ = server.SetHandler(common.APIMethod.GET, "/me/lucky-wheel-mission/list", api.GetMissionLuckyWheel)
	_ = server.SetHandler(common.APIMethod.POST, "/customer-lucky-wheel", api.CreateCustomerLuckyWheel)
	_ = server.SetHandler(common.APIMethod.POST, "/increase-lk-turn", api.IncreaseCustomerLuckyWheel)

	// promotion api
	_ = server.SetHandler(common.APIMethod.GET, "/promotion", api.PromotionGet)
	_ = server.SetHandler(common.APIMethod.GET, "/promotion/list", api.PromotionGetList)
	//_ = server.SetHandler(common.APIMethod.GET, "/promotion/active", api.ActivePromotionWithVoucherGet)
	_ = server.SetHandler(common.APIMethod.POST, "/promotion", api.PromotionCreate)
	_ = server.SetHandler(common.APIMethod.PUT, "/promotion", api.PromotionUpdate)
	_ = server.SetHandler(common.APIMethod.PUT, "/promotion/status", api.PromotionStatusUpdate)
	_ = server.SetHandler(common.APIMethod.POST, "/promotion/promotion-copy", api.PromotionCopy)
	_ = server.SetHandler(common.APIMethod.POST, "/promotion/migrate-hash-tag", api.MigrateHashTagPromotion)

	// user-code api
	_ = server.SetHandler(common.APIMethod.GET, "/user", api.UserPromotionGet)
	_ = server.SetHandler(common.APIMethod.POST, "/use", api.UsePromotion)
	//_ = server.SetHandler(common.APIMethod.POST, "/voucher/use", api.UsePromotion)
	_ = server.SetHandler(common.APIMethod.POST, "/voucher/used", api.UsedPromotion)
	//_ = server.SetHandler(common.APIMethod.POST, "/voucher/check", api.CheckPromotion)
	//_ = server.SetHandler(common.APIMethod.POST, "/voucher/cart", api.CheckPromotion)
	//server.SetHandler(common.APIMethod.GET, "/voucher/active", api.GetActiveVoucher)
	//server.SetHandler(common.APIMethod.POST, "/voucher/active", api.GetAvailableVoucher)
	_ = server.SetHandler(common.APIMethod.POST, "/voucher/migrate-hash-tag", api.MigrateHashTagVoucher)
	_ = server.SetHandler(common.APIMethod.POST, "/user-voucher", api.UserVoucherCreate)
	_ = server.SetHandler(common.APIMethod.PUT, "/user-voucher", api.UserVoucherUpdate)
	_ = server.SetHandler(common.APIMethod.GET, "/user-voucher/list", api.UserVoucherGetList)
	_ = server.SetHandler(common.APIMethod.POST, "/tool/many-user-voucher", api.CreateManyUserVoucher)
	_ = server.SetHandler(common.APIMethod.PUT, "/tool/many-update-voucher", api.UpdateManyUserVoucher)
	// _ = server.SetHandler(common.APIMethod.DELETE, "/user-voucher", api.UserVoucherDelete)

	// voucher api
	_ = server.SetHandler(common.APIMethod.GET, "/voucher", api.GetVouchers)
	_ = server.SetHandler(common.APIMethod.GET, "/voucher/list", api.VoucherGetList)
	_ = server.SetHandler(common.APIMethod.POST, "/voucher", api.CreateVoucher)
	_ = server.SetHandler(common.APIMethod.POST, "/voucher/list", api.ListVoucherGetByCodes)
	_ = server.SetHandler(common.APIMethod.PUT, "/voucher", api.UpdateVoucher)
	_ = server.SetHandler(common.APIMethod.POST, "/voucher/refund", api.RefundVoucher)
	_ = server.SetHandler(common.APIMethod.PUT, "/voucher/status", api.UpdateVoucherStatus)
	_ = server.SetHandler(common.APIMethod.GET, "/voucher-history/list", api.VoucherHistoryGetList)
	_ = server.SetHandler(common.APIMethod.PUT, "/voucher/delete-applied-customers", api.DeleteAppliedCustomers)
	_ = server.SetHandler(common.APIMethod.GET, "/voucher/history/list", api.GetVoucherUsageHistoryList)
	_ = server.SetHandler(common.APIMethod.POST, "/tool/many-voucher", api.CreateManyVoucher)
	_ = server.SetHandler(common.APIMethod.POST, "/voucher/multiple-voucher", api.CreateMultipleVoucher)
	_ = server.SetHandler(common.APIMethod.GET, "/voucher/import-batch/list", api.GetVoucherImportBatchList)
	_ = server.SetHandler(common.APIMethod.GET, "/voucher/import-batch/detail", api.GetVoucherImportBatchDetail)
	_ = server.SetHandler(common.APIMethod.GET, "/me/voucher", api.GetCustomerVouchers)
	_ = server.SetHandler(common.APIMethod.GET, "/me/voucher/history", api.SelfVoucherHistoryGetList)

	// gift setting
	_ = server.SetHandler(common.APIMethod.POST, "/gift-setting", api.GiftSettingCreate)
	_ = server.SetHandler(common.APIMethod.GET, "/gift-setting", api.GiftSettingGet)
	// _ = server.SetHandler(common.APIMethod.GET, "/me/promo-gift", api.GetSelfGiftInfo)
	// campaign
	server.SetHandler(common.APIMethod.GET, "/campaign/list", api.CampaignList) // lay danh sach
	server.SetHandler(common.APIMethod.GET, "/campaign/active/list", api.CampaignActiveList)
	server.SetHandler(common.APIMethod.POST, "/campaign/list", api.CampaignSearchList)                          // lay danh sach
	server.SetHandler(common.APIMethod.POST, "/campaign", api.CampaignCreate)                                   // tao campaign
	server.SetHandler(common.APIMethod.PUT, "/campaign", api.CampaignUpdate)                                    // cap nhat campaign
	server.SetHandler(common.APIMethod.GET, "/campaign", api.CampaignGet)                                       // lay thong tin campaign
	server.SetHandler(common.APIMethod.GET, "/campaign/product/list", api.CampaignProductList)                  // lay thong tin campaign detail
	server.SetHandler(common.APIMethod.POST, "/campaign/product", api.CampaignProductCreate)                    // add product to campaign
	server.SetHandler(common.APIMethod.POST, "/campaign/product/import", api.ImportCampaignProduct)             // import product to campaign
	server.SetHandler(common.APIMethod.PUT, "/campaign/product", api.CampaignProductUpdate)                     // update product in campaign
	server.SetHandler(common.APIMethod.DELETE, "/campaign/product", api.CampaignProductDelete)                  // remove product from campaign
	server.SetHandler(common.APIMethod.POST, "/campaign/check", api.CheckCampaignWithCart)                      // check campaign with cart
	server.SetHandler(common.APIMethod.PUT, "/campaign/product/sold-quantity", api.UpdateSoldQuantityFromOrder) // update product in campaign
	server.SetHandler(common.APIMethod.PUT, "/campaign/product/sync-sku", api.SyncSkuInfoInCampaign)            // update sku info in campaign
	server.SetHandler(common.APIMethod.GET, "/campaign-history/list", api.CampaignHistoryGetList)               // update product in campaign
	server.SetHandler(common.APIMethod.GET, "/ticket/list", api.CampaignTicketList)
	server.SetHandler(common.APIMethod.PUT, "/ticket/status", api.TicketUpdateStatus) // temp using for campaign ticket
	server.SetHandler(common.APIMethod.POST, "/campaign/product/check", api.CheckProductCampaignRegister)
	server.SetHandler(common.APIMethod.POST, "/campaign/product/sync", api.SyncProductCampaignSaleTime)
	server.SetHandler(common.APIMethod.POST, "/campaign/product/warm-up", api.WarmupCampaignProductBySkuMain)
	server.SetHandler(common.APIMethod.POST, "/campaign/active-by-sku", api.GetCampaignActiveBySku)
	server.SetHandler(common.APIMethod.POST, "/campaign/check-fulfillment", api.CheckCampaignProductFulfillment)
	server.SetHandler(common.APIMethod.GET, "/campaign/check-fulfillment/list", api.GetCheckProductFulfillmentLogList)
	server.SetHandler(common.APIMethod.GET, "/campaign/check-fulfillment-detail/list", api.GetCheckProductFulfillmentDetailList)
	server.SetHandler(common.APIMethod.GET, "/campaign/migrate-campaign-flash-sale-items", api.MigrateCampaignFlashSaleItems)

	_ = server.SetHandler(common.APIMethod.GET, "/seller/campaign/list", api.SellerCampaignList)  // use for seller
	_ = server.SetHandler(common.APIMethod.POST, "/seller/campaign/list", api.CampaignSearchList) // use for seller
	_ = server.SetHandler(common.APIMethod.GET, "/seller/campaign", api.SellerCampaignGet)
	_ = server.SetHandler(common.APIMethod.POST, "/seller/campaign/register", api.SellerCampaignCreate)
	_ = server.SetHandler(common.APIMethod.POST, "/seller/campaign/check", api.SellerCampaignCheck)
	_ = server.SetHandler(common.APIMethod.GET, "/seller/ticket/list", api.SellerCampaignListTicket) // use for seller
	_ = server.SetHandler(common.APIMethod.PUT, "/seller/ticket", api.SellerTicketUpdate)
	_ = server.SetHandler(common.APIMethod.GET, "/seller/campaign/product/list", api.SellerCampaignProductList)
	_ = server.SetHandler(common.APIMethod.PUT, "/campaign/product/turn-off-by-sku", api.TurnOffProductInCampaignBySku)
	// _ = server.SetHandler(common.APIMethod.GET, "/seller/campaign/ticket/list", api.GetSellerCampaignTicketList)
	// _ = server.SetHandler(common.APIMethod.PUT, "/seller/campaign/mul-ticket-status", api.UpdateMultiTicketStatus)
	// _ = server.SetHandler(common.APIMethod.POST, "/seller/campaign/mul-campaign-ticket", api.CreateMultiTicket)

	_ = server.SetHandler(common.APIMethod.POST, "/campaign-product/import", api.ImportCampaignProduct)

	_ = server.SetHandler(common.APIMethod.GET, "/import-result/list", api.ImportResultGetList)
	_ = server.SetHandler(common.APIMethod.GET, "/import-result-detail/list", api.ImportResultDetailGetList)

	// promotion new format condition
	_ = server.SetHandler(common.APIMethod.POST, "/voucher/check-with-cart", api.VoucherCheckWithCart)
	_ = server.SetHandler(common.APIMethod.POST, "/voucher/list-from-order", api.VoucherActiveList)
	_ = server.SetHandler(common.APIMethod.POST, "/voucher/list-by-sku", api.GetVoucherBySku)
	_ = server.SetHandler(common.APIMethod.GET, "/me/voucher/list", api.SelfVoucherActiveList)
	_ = server.SetHandler(common.APIMethod.POST, "/me/voucher/list", api.VoucherActiveList)

	// migrate
	_ = server.SetHandler(common.APIMethod.POST, "/voucher/migrate-data", api.MigrateVoucherData)

	// share
	// _ = server.SetHandler(common.APIMethod.POST, "/share-log", api.CreateShareLog)
	// _ = server.SetHandler(common.APIMethod.GET, "/share-log/list", api.ShareLogGetList)

	// tracking event

	_ = server.SetHandler(common.APIMethod.POST, "/tracking", api.TrackingCreate)
	_ = server.SetHandler(common.APIMethod.POST, "/tracking-event", api.TrackingEventCreate)
	_ = server.SetHandler(common.APIMethod.GET, "/tracking-event/list", api.TrackingEventGetList)
	_ = server.SetHandler(common.APIMethod.GET, "/tracking/list", api.TrackingGetList)
	_ = server.SetHandler(common.APIMethod.GET, "/tracking/statistic", api.TrackingGetStatistic)

	_ = server.SetHandler(common.APIMethod.GET, "/setting", api.SettingGet)
	_ = server.SetHandler(common.APIMethod.PUT, "/setting", api.SettingUpdate)

	// Voucher Type
	const voucherGroupTypePath = "/voucher-group-type"
	_ = server.SetHandler(common.APIMethod.GET, voucherGroupTypePath+"/list", api.GetVoucherGroupTypeList)
	_ = server.SetHandler(common.APIMethod.GET, voucherGroupTypePath, api.GetVoucherGroupType)
	_ = server.SetHandler(common.APIMethod.POST, voucherGroupTypePath, api.CreateVoucherGroupType)
	_ = server.SetHandler(common.APIMethod.PUT, voucherGroupTypePath, api.UpdateVoucherGroupType)

	// Voucher Type
	const voucherGroupConnectionPath = "/voucher-group-connection"
	_ = server.SetHandler(common.APIMethod.GET, voucherGroupConnectionPath+"/list", api.GetVoucherGroupConnectionsList)
	_ = server.SetHandler(common.APIMethod.GET, voucherGroupConnectionPath, api.GetVoucherGroupConnection)
	_ = server.SetHandler(common.APIMethod.POST, voucherGroupConnectionPath, api.CreateVoucherGroupConnection)
	_ = server.SetHandler(common.APIMethod.PUT, voucherGroupConnectionPath, api.UpdateVoucherGroupConnection)

	// checkin
	// _ = server.SetHandler(common.APIMethod.POST, "/checkin-config", api.CreateCheckinConfig)
	// _ = server.SetHandler(common.APIMethod.PUT, "/checkin-config", api.UpdateCheckinConfig)
	// _ = server.SetHandler(common.APIMethod.GET, "/checkin-config", api.GetCheckinConfigByCode)
	// _ = server.SetHandler(common.APIMethod.GET, "/checkin-config/list", api.GetCheckinConfigList)
	// _ = server.SetHandler(common.APIMethod.POST, "/checkin-item", api.CreateCheckinItem)
	// _ = server.SetHandler(common.APIMethod.PUT, "/checkin-item", api.UpdateCheckinItem)
	// _ = server.SetHandler(common.APIMethod.GET, "/checkin-item/list", api.GetCheckinItemList)
	// _ = server.SetHandler(common.APIMethod.GET, "/me/checkin", api.GetSelfCheckin)
	// _ = server.SetHandler(common.APIMethod.GET, "/daily-checkin/customer/list", api.DailyCheckinCustomerGetList)
	// _ = server.SetHandler(common.APIMethod.POST, "/daily-checkin/customer", api.AddCustomerToDailyCheckin)
	// _ = server.SetHandler(common.APIMethod.PUT, "/daily-checkin/customer", api.UpdateCustomerOfDailyCheckin)
	// _ = server.SetHandler(common.APIMethod.DELETE, "/daily-checkin/customer", api.DeleteCustomerOfDailyCheckin)
	// _ = server.SetHandler(common.APIMethod.GET, "/daily-checkin-log/list", api.GetDailyCheckinLog)
	// _ = server.SetHandler(common.APIMethod.POST, "/me/checkin", api.SelfCheckin)

	// game questions
	{
		_ = server.SetHandler(common.APIMethod.GET, "/game-questions", api.GetGameQuestions)
		_ = server.SetHandler(common.APIMethod.POST, "/game-questions", api.CreateGameQuestion)
		_ = server.SetHandler(common.APIMethod.PUT, "/game-questions", api.UpdateGameQuestion)
		_ = server.SetHandler(common.APIMethod.DELETE, "/game-questions", api.DeleteGameQuestion)
		_ = server.SetHandler(common.APIMethod.GET, "/game-questions/spin-question", api.GetGameQuestionSpin)
	}

	// expose
	server.Expose(80)

	//worker := app.SetupWorker()
	//worker.SetDelay(30)
	//worker.SetRepeatPeriod(1)
	//worker.SetTask(func() { _ = client.Services.HealthCheck() })

	// onScheduledWorker := app.SetupWorker()
	// onScheduledWorker.SetTask(action.OnScheduledCheck)
	// onScheduledWorker.SetDelay(15)
	// onScheduledWorker.SetRepeatPeriod(15)

	workerWarmupMasterData := app.SetupWorker()
	workerWarmupMasterData.SetTask(func() {
		action.WarmupRegionMasterdataCache()
		action.WarmupLevelMasterdataCache()
	})
	workerWarmupMasterData.SetRepeatPeriod(3600)
	workerWarmupMasterData.SetDelay(10)

	// this worker does not run in UAT
	if conf.Config.Env != "uat" {
		workerCancelSoldOutProduct := app.SetupWorker()
		workerCancelSoldOutProduct.SetTask(action.WorkerCancelSoldOutProduct)
		workerCancelSoldOutProduct.SetRepeatPeriod(60 * 30)
		workerCancelSoldOutProduct.SetDelay(10)
	}

	app.OnAllDBConnected(func() {
		go action.WarmupAllSkuSaleInfo()
		//go action.WarmUpAllCampaign()
		action.WarmupRegionMasterdataCache()
		action.WarmupLevelMasterdataCache()
		//go action.WarmUpAllVoucher()

		if conf.Config.Env == "uat" {
			return
		}
		model.SyncGamificationResultJobExecutor.StartConsume()
		model.CheckProductFulfillmentJobExecutor.StartConsume()

		// consumer
		model.CampaignProductImportJob.StartConsume()
		model.VoucherImportJob.StartConsume()
		model.SyncGamificationJob.StartConsume()
		// model.UpdateCampaignTicketeStatusJob.StartConsume()
		// model.ImportCampaignTicketJob.StartConsume()
		model.RewardGenJob.StartConsume()
		model.UserVoucherCreationJob.StartConsume()
		model.RewardVoucherUpdationJob.StartConsume()
	})

	//workerSyncGamification := app.SetupWorker()
	//workerSyncGamification.SetTask(action.SyncGamificationResultTask)
	//workerSyncGamification.SetDelay(2)
	//workerSyncGamification.SetRepeatPeriod(1800)

	// load activity template
	// workerExpirePromotion := app.SetupWorker()
	// workerExpirePromotion.SetTask(action.AutoExpirePromotion).SetRepeatPeriod(30).SetDelay(30)

	// workerActivePromotion := app.SetupWorker()
	// workerActivePromotion.SetTask(action.AutoActivePromotion).SetRepeatPeriod(30).SetDelay(30)

	// workerExpireVoucher := app.SetupWorker()
	// workerExpireVoucher.SetTask(action.AutoExpireVoucher).SetRepeatPeriod(30).SetDelay(30)

	// workerActiveVoucher := app.SetupWorker()
	// workerActiveVoucher.SetTask(action.AutoActiveVoucher).SetRepeatPeriod(30).SetDelay(30)

	// workerCheckVoucher := app.SetupWorker()
	// workerCheckVoucher.SetTask(action.CheckVoucher).SetRepeatPeriod(30).SetDelay(10)

	// workerCheckCampaign := app.SetupWorker()
	// workerCheckCampaign.SetTask(action.CheckSyncCampaign).SetRepeatPeriod(180).SetDelay(10)

	// launch app
	_ = app.Launch()
}
