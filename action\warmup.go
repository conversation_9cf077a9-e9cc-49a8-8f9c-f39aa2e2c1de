package action

import (
	"fmt"

	"gitlab.com/buymed.th/marketplace/promotion/conf"

	"math"
	"strings"

	"gitlab.com/buymed.th/marketplace/promotion/model"
	"gitlab.com/buymed.th/marketplace/promotion/model/cache"
	"gitlab.com/buymed.th/marketplace/promotion/model/enum"
	"gitlab.com/buymed.th/marketplace/promotion/utils"

	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func OnScheduledCheck() {
	t := true
	f := false
	// campaign
	flagResult := cache.FlagDB.UpdateOne(cache.Flag{
		SyncCampaign: &t,
	}, cache.Flag{
		SyncCampaign: &f,
	})
	if flagResult.Status == common.APIStatus.Ok {
		WarnUpAllCampaign()
	}

	// voucher
	flagResult = cache.FlagDB.UpdateOne(cache.Flag{
		SyncVoucher: &t,
	}, cache.Flag{
		SyncVoucher: &f,
	})
	if flagResult.Status == common.APIStatus.Ok {
		WarmUpAllVoucher()
	}

	// voucher
	flagResult = cache.FlagDB.UpdateOne(cache.Flag{
		SyncUserPromotion: &t,
	}, cache.Flag{
		SyncUserPromotion: &f,
	})
	if flagResult.Status == common.APIStatus.Ok {
		WarmUpAllUserPromotion()
	}
}

func WarmUpVoucherByCode(code string, voucher *model.Voucher) {
	search := code
	if voucher == nil {
		qVoucher := model.VoucherDB.QueryOne(model.Voucher{Code: code})
		if qVoucher.Status != common.APIStatus.Ok {
			return
		}
		voucher = qVoucher.Data.([]*model.Voucher)[0]
	}
	voucher.Usable = utils.ParseBoolToPointer(true)
	if voucher.AppliedCustomers != nil {
		voucher.AppliedCustomerMap = make(map[int64]bool)
		for _, customer := range *voucher.AppliedCustomers {
			voucher.AppliedCustomerMap[customer] = true
		}
	}
	for _, scope := range voucher.Scopes {
		if scope.Type != nil && *scope.Type == enum.ScopeType.AREA {
			if voucher.RegionScopeMap == nil {
				voucher.RegionScopeMap = make(map[string]bool)
			}
			if scope.QuantityType != nil && *scope.QuantityType == enum.QuantityType.ALL {
				voucher.RegionScopeMap["ALL"] = true
			} else if scope.QuantityType != nil && *scope.QuantityType == enum.QuantityType.MANY {
				for _, region := range scope.AreaCodes {
					voucher.RegionScopeMap[region] = true
				}
			}
		} else if scope.Type != nil && *scope.Type == enum.ScopeType.CUSTOMER_LEVEL {
			if voucher.LevelScopeMap == nil {
				voucher.LevelScopeMap = make(map[string]bool)
			}
			if scope.QuantityType != nil && *scope.QuantityType == enum.QuantityType.ALL {
				voucher.LevelScopeMap["ALL"] = true
			} else if scope.QuantityType != nil && *scope.QuantityType == enum.QuantityType.MANY {
				for _, level := range scope.CustomerLevelCodes {
					voucher.LevelScopeMap[level] = true
				}
			}
		} else if scope.Type != nil && *scope.Type == enum.ScopeType.CUSTOMER_SCOPE {
			if voucher.CustomerScopeMap == nil {
				voucher.CustomerScopeMap = make(map[string]bool)
			}
			if scope.QuantityType != nil && *scope.QuantityType == enum.QuantityType.ALL {
				voucher.CustomerScopeMap["ALL"] = true
			} else if scope.QuantityType != nil && *scope.QuantityType == enum.QuantityType.MANY {
				for _, level := range scope.CustomerScopes {
					voucher.CustomerScopeMap[level] = true
				}
			}
		}
	}
	updater := voucher
	normStr := strings.Replace(utils.NormalizeString(search), " ", "-", -1)
	updater.HashTag = normStr
	updater.RefProduct = setVoucherRefProduct(voucher)
	qUpdate := model.VoucherCacheDB.Upsert(model.Voucher{Code: voucher.Code}, updater)
	if voucher.Status != nil {
		if conf.Config.Env == "prd" {
			model.UserPromotionCacheDB.UpdateMany(model.UserPromotion{VoucherCode: code}, model.UserPromotion{VoucherStatus: string(*voucher.Status)})
		}
	}
	fmt.Println(voucher.Code, qUpdate.Message)
}

func WarmUpAllVoucherActive() *common.APIResponse {
	offset, limit := int64(0), int64(1000)
	for {
		qVoucher := model.VoucherDB.Query(model.Voucher{Status: &enum.VoucherStatus.ACTIVE}, offset*limit, limit, &primitive.M{"_id": 1})
		if qVoucher.Status != common.APIStatus.Ok {
			break
		}
		for _, voucher := range qVoucher.Data.([]*model.Voucher) {
			WarmUpVoucherByCode("", voucher)
			WarmUpUserPromotionByVoucher(voucher)
		}
		offset++
	}
	return &common.APIResponse{
		Status:    common.APIStatus.Ok,
		Message:   "",
		ErrorCode: "",
		Total:     0,
	}
}

func WarmUpAllVoucher() *common.APIResponse {
	offset, limit := int64(0), int64(1000)
	for {
		qVoucher := model.VoucherDB.Query(model.Voucher{}, offset*limit, limit, &primitive.M{"_id": 1})
		if qVoucher.Status != common.APIStatus.Ok {
			break
		}
		for _, voucher := range qVoucher.Data.([]*model.Voucher) {
			WarmUpVoucherByCode("", voucher)
			WarmUpUserPromotionByVoucher(voucher)
		}
		offset++
	}
	return &common.APIResponse{
		Status:    common.APIStatus.Ok,
		Message:   "",
		ErrorCode: "",
		Total:     0,
	}
}

func WarmUpUserPromotion(code string, customerID int64, use *model.UserPromotion) *common.APIResponse {
	if use != nil {
		use.ID = primitive.NilObjectID
		upsertResult := model.UserPromotionCacheDB.Upsert(model.UserPromotion{VoucherCode: use.VoucherCode, CustomerID: use.CustomerID}, use)
		fmt.Println(use.VoucherCode, upsertResult.Message)
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "OK",
		}
	}
	query := model.UserPromotion{VoucherCode: code, CustomerID: customerID}
	offset, limit := int64(0), int64(1000)
	voucherStatus := ""
	var voucherCustomerApplyType enum.CustomerApplyTypeValue
	for {
		qUses := model.UserPromotionDB.Query(query, offset*limit, limit, nil)
		if qUses.Status != common.APIStatus.Ok {
			break
		}
		if qVoucher := model.VoucherDB.QueryOne(model.Voucher{Code: code}); qVoucher.Status == common.APIStatus.Ok {
			if qVoucher.Data.([]*model.Voucher)[0].Status != nil {
				voucherCustomerApplyType = qVoucher.Data.([]*model.Voucher)[0].CustomerApplyType
				voucherStatus = string(*qVoucher.Data.([]*model.Voucher)[0].Status)
			}
		}
		for _, use := range qUses.Data.([]*model.UserPromotion) {
			use.VoucherStatus = voucherStatus
			use.CustomerApplyType = voucherCustomerApplyType
			model.UserPromotionCacheDB.Upsert(model.UserPromotion{VoucherCode: use.VoucherCode, CustomerID: use.CustomerID}, use)
		}
		offset++
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
	}
}

func WarmUpUserPromotionByVoucher(voucher *model.Voucher) {
	query := model.UserPromotion{VoucherCode: voucher.Code}
	if qDelete := model.UserPromotionCacheDB.Delete(query); qDelete.Status != common.APIStatus.Ok {
		return
	}
	offset, limit := int64(0), int64(1000)
	for {
		uses := make([]*model.UserPromotion, 0)
		qUses := model.UserPromotionDB.Query(query, offset*limit, limit, nil)
		if qUses.Status != common.APIStatus.Ok {
			break
		}
		for _, use := range qUses.Data.([]*model.UserPromotion) {
			if voucher.Status != nil {
				use.VoucherStatus = string(*voucher.Status)
			}
			use.CustomerApplyType = voucher.CustomerApplyType
			uses = append(uses, use)
			//model.UserPromotionCacheDB.Upsert(model.UserPromotion{VoucherCode: use.VoucherCode, CustomerID: use.CustomerID}, use)
		}
		offset++
		qCreate := model.UserPromotionCacheDB.CreateMany(uses)
		fmt.Println(voucher.Code, qCreate.Message)
	}
}

func WarmUpAllUserPromotion() *common.APIResponse {
	offset, limit := int64(0), int64(1000)
	for {
		qUse := model.UserPromotionDB.Query(model.UserPromotion{Status: &enum.CodeStatus.ACTIVE}, offset*limit, limit, &primitive.M{"voucher_code": 1, "last_updated_time": -1})
		if qUse.Status != common.APIStatus.Ok {
			break
		}
		uses := qUse.Data.([]*model.UserPromotion)
		voucherCode := ""
		var voucher *model.Voucher
		for _, use := range uses {
			if voucherCode != use.VoucherCode {
				qVoucher := model.VoucherDB.QueryOne(model.Voucher{Code: use.VoucherCode})
				if qVoucher.Status == common.APIStatus.Ok {
					voucher = qVoucher.Data.([]*model.Voucher)[0]
				}
				if voucher != nil {
					if voucher.Status != nil {
						use.VoucherStatus = string(*voucher.Status)
					}
					use.CustomerApplyType = voucher.CustomerApplyType
				}
			}
			WarmUpUserPromotion("", 0, use)
		}
		offset++
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
	}
}

func MigrateOldPromotion() *common.APIResponse {
	offset, limit := int64(0), int64(1000)
	for {
		qPromotion := model.PromotionDB.Query(bson.M{}, offset*limit, limit, nil)
		if qPromotion.Status != common.APIStatus.Ok {
			break
		}
		for _, promotion := range qPromotion.Data.([]*model.Promotion) {
			if len(promotion.Conditions) > 0 {
				description := ""
				andConditions := make(map[enum.ConditionTypeValue]model.PromotionType)
				oldCondition := promotion.Conditions[0]
				if oldCondition.MaxTotalOrder != nil || oldCondition.MinTotalOrder != nil || oldCondition.MinDaysNoOrder != nil {
					andConditions[enum.ConditionType.CUSTOMER_HISTORY] = model.PromotionType{
						AndConditions: []model.PromotionCondition{
							{
								CustomerConditionField: model.CustomerConditionField{
									MinDayNoOrder: func() *int {
										if oldCondition.MinDaysNoOrder != nil && *oldCondition.MinDaysNoOrder != 0 {
											description = description + fmt.Sprintf("<h4>* Số ngày không đặt hàng: %d ngày</h4>\n", *oldCondition.MinDaysNoOrder)
											return utils.ParseIntToPointer(int(*oldCondition.MinDaysNoOrder))
										}
										return nil
									}(),
									MinOrderCount: func() *int {
										if oldCondition.MinTotalOrder != nil && *oldCondition.MinTotalOrder != 0 {
											description = description + fmt.Sprintf("<h4>* Số đơn hàng tối thiểu: %d đơn hàng</h4>\n", *oldCondition.MinTotalOrder)
											return utils.ParseIntToPointer(int(*oldCondition.MinTotalOrder))
										}
										return nil
									}(),
									MaxOrderCount: func() *int {
										if oldCondition.MaxTotalOrder != nil && *oldCondition.MaxTotalOrder != 0 {
											description = description + fmt.Sprintf("<h4>* Số đơn hàng tối đa: %d đơn hàng</h4>\n", *oldCondition.MaxTotalOrder)
											return utils.ParseIntToPointer(int(*oldCondition.MaxTotalOrder))
										}
										return nil
									}(),
									IndexOrder: nil,
								},
							},
						},
					}
				}
				if oldCondition.MinOrderValue != nil && *oldCondition.MinOrderValue != 0 {
					description = description + fmt.Sprintf("<h4>* Giá trị đơn hàng từ %sđ</h4>\n", utils.FormatVNDCurrency(fmt.Sprint(*oldCondition.MinOrderValue)))

					andConditions[enum.ConditionType.ORDER_VALUE] = model.PromotionType{
						AndConditions: []model.PromotionCondition{
							{
								OrderConditionField: model.OrderConditionField{
									MinTotalPrice: utils.ParseIntToPointer(int(*oldCondition.MinOrderValue)),
								},
							},
						},
					}
				}
				promotion.AndConditions = andConditions
				if len(description) == 0 {
					description = "<h4>* Áp dụng cho mọi đơn hàng <h4>"
				}
				promotion.ConditionDescription = utils.ParseStringToPointer(description)
				updateRes := model.PromotionDB.UpdateOne(bson.M{"promotion_id": promotion.PromotionID}, promotion)

				rewards := make([]model.Reward, 0)
				if promotion.Rewards != nil && len(*promotion.Rewards) > 0 {
					rewards = *promotion.Rewards
				}
				model.VoucherDB.UpdateMany(bson.M{"promotion_id": promotion.PromotionID}, model.Voucher{
					Rewards: rewards,
				})
				fmt.Println(promotion.PromotionID, updateRes.Message)
			}
		}
		offset++
	}
	fmt.Println("DONE")
	return &common.APIResponse{
		Status:    common.APIStatus.Ok,
		Message:   "",
		ErrorCode: "",
		Total:     0,
	}
}

func setVoucherRefProduct(voucher *model.Voucher) []string {
	refProducts := make([]string, 0)
	if data, ok := voucher.OrConditions[enum.ConditionType.PRODUCT]; ok {
		for _, c := range data.OrConditions {
			refProduct := c.ProductConditionField.ProductCode
			if c.ProductConditionField.SellerCode != nil {
				refProduct = fmt.Sprintf("%s.%s", *c.ProductConditionField.SellerCode, c.ProductConditionField.ProductCode)
			}
			refProducts = append(refProducts, refProduct)
		}
		for _, c := range data.AndConditions {
			refProduct := c.ProductConditionField.ProductCode
			if c.ProductConditionField.SellerCode != nil {
				refProduct = fmt.Sprintf("%s.%s", *c.ProductConditionField.SellerCode, c.ProductConditionField.ProductCode)
			}
			refProducts = append(refProducts, refProduct)
		}
	}
	if data, ok := voucher.AndConditions[enum.ConditionType.PRODUCT]; ok {
		for _, c := range data.AndConditions {
			refProduct := c.ProductConditionField.ProductCode
			if c.ProductConditionField.SellerCode != nil {
				refProduct = fmt.Sprintf("%s.%s", *c.ProductConditionField.SellerCode, c.ProductConditionField.ProductCode)
			}
			refProducts = append(refProducts, refProduct)
		}
		for _, c := range data.OrConditions {
			refProduct := c.ProductConditionField.ProductCode
			if c.ProductConditionField.SellerCode != nil {
				refProduct = fmt.Sprintf("%s.%s", *c.ProductConditionField.SellerCode, c.ProductConditionField.ProductCode)
			}
			refProducts = append(refProducts, refProduct)
		}
	}
	return refProducts
}

func MigrateOldVoucher() *common.APIResponse {
	offset, limit := int64(0), int64(1000)
	for {
		qVoucher := model.VoucherDB.Query(model.Voucher{ComplexQuery: []*bson.M{{"system_note": nil}}}, offset*limit, limit, &primitive.M{"_id": 1})
		if qVoucher.Status != common.APIStatus.Ok {
			break
		}
		vouchers := qVoucher.Data.([]*model.Voucher)
		promotionIDs := make([]int64, 0)
		promotionIDMap := make(map[int64]bool)
		promotionMap := make(map[int64]*model.Promotion)
		for _, voucher := range vouchers {
			if data, ok := promotionIDMap[voucher.PromotionID]; ok && data {
				continue
			}
			promotionIDs = append(promotionIDs, voucher.PromotionID)
		}
		qPromotion := model.PromotionDB.Query(&model.Promotion{
			ComplexQuery: []*bson.M{
				{
					"promotion_id": bson.M{"$in": promotionIDs},
				},
			},
		}, 0, 0, nil)
		if qPromotion.Status == common.APIStatus.Ok {
			for _, promotion := range qPromotion.Data.([]*model.Promotion) {
				promotionMap[promotion.PromotionID] = promotion
			}
		}
		for _, voucher := range vouchers {
			promotion := promotionMap[voucher.PromotionID]
			if promotion == nil {
				continue
			}
			if (len(voucher.OrConditions) == 0 || voucher.OrConditions == nil) && (len(voucher.AndConditions) == 0 || voucher.AndConditions == nil) {
				voucher.OrConditions = make(map[enum.ConditionTypeValue]model.PromotionType, 0)
				voucher.AndConditions = make(map[enum.ConditionTypeValue]model.PromotionType, 0)
				if promotion.OrConditions != nil {
					voucher.OrConditions = promotion.OrConditions
				}
				if promotion.AndConditions != nil {
					voucher.AndConditions = promotion.AndConditions
				}
			}
			if len(voucher.Scopes) == 0 {
				voucher.Scopes = promotion.Scopes
			}
			if len(voucher.Rewards) == 0 && promotion.Rewards != nil && len(*promotion.Rewards) > 0 {
				voucher.Rewards = *promotion.Rewards
			}
			if voucher.ConditionDescription == nil {
				voucher.ConditionDescription = promotion.ConditionDescription
			}
			if voucher.PromotionType == nil {
				voucher.PromotionType = promotion.PromotionType
			}
			if voucher.ApplyType == "" {
				voucher.ApplyType = enum.ApplyType.MANUAL
			}
			if voucher.DisplayName == "" {
				voucher.DisplayName = promotion.Description
			}
			if voucher.PromotionName == "" {
				voucher.DisplayName = promotion.Description
			}
			if voucher.CustomerApplyType == "" {
				if voucher.AppliedCustomers != nil && len(*voucher.AppliedCustomers) > 0 {
					voucher.CustomerApplyType = enum.CustomerApplyType.MANY
				} else {
					voucher.CustomerApplyType = enum.CustomerApplyType.ALL
				}
			}
			voucher.SystemNote = voucher.SystemNote + "migrate data voucher at " + time.Now().Format("2006-01-02 15:04:05") + "\n"
			voucher.ID = primitive.NilObjectID
			qUpdate := model.VoucherDB.UpdateOne(model.Voucher{Code: voucher.Code}, voucher)
			fmt.Println(voucher.Code, qUpdate.Message)
		}
		offset++
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
	}
}

func MigrateOldUserPromotion() *common.APIResponse {
	offset, limit := int64(0), int64(1000)
	for {
		qVoucher := model.VoucherDB.Query(model.Voucher{IsMigrateUserCode: false}, offset*limit, limit, &primitive.M{"_id": 1})
		if qVoucher.Status != common.APIStatus.Ok {
			break
		}
		vouchers := qVoucher.Data.([]*model.Voucher)
		for _, voucher := range vouchers {
			if voucher.AppliedCustomers != nil && len(*voucher.AppliedCustomers) > 0 {
				uses := make([]*model.UserPromotion, 0)
				for _, customerID := range *voucher.AppliedCustomers {
					if qUse := model.UserPromotionDB.QueryOne(&model.UserPromotion{VoucherCode: voucher.Code, CustomerID: customerID}); qUse.Status == common.APIStatus.Ok {
						continue
					}
					use := model.UserPromotion{
						CreatedBy:         voucher.CreatedBy,
						CustomerID:        customerID,
						PromotionID:       voucher.PromotionID,
						VoucherCode:       voucher.Code,
						Status:            &enum.CodeStatus.ACTIVE,
						CustomerApplyType: voucher.CustomerApplyType,
						SystemNote:        "migrate data user promotion at " + time.Now().Format("2006-01-02 15:04:05") + "\n",
					}
					if voucher.Status != nil {
						use.VoucherStatus = string(*voucher.Status)
					}
					uses = append(uses, &use)
					if len(uses) == 500 {
						qCreate := model.UserPromotionDB.CreateMany(uses)
						fmt.Println(voucher.Code, qCreate.Message)
						uses = make([]*model.UserPromotion, 0)
					}
				}
				if len(uses) > 0 {
					qCreate := model.UserPromotionDB.CreateMany(uses)
					fmt.Println(voucher.Code, qCreate.Message)
				}
			}
			model.VoucherDB.UpdateOne(model.Voucher{Code: voucher.Code}, model.Voucher{IsMigrateUserCode: true})
		}
		offset++
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
	}
}

func MigrateVoucherPrice() *common.APIResponse {
	go func() {
		offset, limit := 0, 1000
		for {
			qRes := model.VoucherCacheDB.Query(bson.M{}, int64(offset*limit), int64(limit), &primitive.M{"_id": -1})
			if qRes.Status != common.APIStatus.Ok {
				fmt.Println(qRes.Message)
				return
			}
			data := qRes.Data.([]*model.Voucher)
			for _, item := range data {
				query := model.Voucher{
					VoucherID: item.VoucherID,
				}
				updater := model.Voucher{
					SystemNote: "MigrateCurrency",
				}
				//if len(item.Rewards) > 0 {
				//	item.Rewards[0].AbsoluteDiscount = math.Round(item.Rewards[0].AbsoluteDiscount * float64(4150))
				//	item.Rewards[0].MaxDiscount = math.Round(item.Rewards[0].MaxDiscount * float64(4150))
				//	updater.Rewards = item.Rewards
				//}
				if item.AndConditions != nil {
					if conditon, ok := item.AndConditions[enum.ConditionType.ORDER_VALUE]; ok {
						if len(conditon.OrConditions) > 0 {
							conditon.OrConditions[0].OrderConditionField.MinTotalPrice = utils.ParseIntToPointer(int(math.Round(float64(*conditon.OrConditions[0].OrderConditionField.MinTotalPrice) * float64(4150))))
						}
					}
				}
				updater.AndConditions = item.AndConditions
				updateRes := model.VoucherDB.UpdateOne(query, updater)
				fmt.Println(item.VoucherID, updateRes.Message)
			}
			offset++
		}
	}()
	return &common.APIResponse{
		Status:    common.APIStatus.Ok,
		Data:      nil,
		Message:   "",
		ErrorCode: "",
		Total:     0,
		Headers:   nil,
	}
}
