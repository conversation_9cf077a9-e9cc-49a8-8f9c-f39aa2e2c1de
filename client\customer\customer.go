package customer

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/buymed.th/marketplace/promotion/model"
)

const (
	pathHealthCheck             = "/marketplace/customer/v1/api-info"
	pathGetAllListLevelCustomer = "/marketplace/customer/v1/level/list"
	pathGetCustomerByCode       = "/marketplace/customer/v1/account"
	pathGetCustomerByIDs        = "/marketplace/customer/v1/account/list"
	pathUpdatePoint             = "/marketplace/customer/v1/point"
)

// Client is model define CustomerClient
type Client struct {
	svc            *client.RestClient
	headers        map[string]string
	healthCheck    bool
	healthStatus   string
	lastTimeUpdate *time.Time
}

// NewServiceClient is func create new service client
func NewServiceClient(apiHost, apiKey, logName string) *Client {
	if apiHost == "" || apiKey == "" {
		return nil
	}
	return &Client{
		svc: client.NewRESTClient(
			apiHost,
			logName,
			time.Duration(10*time.Second),
			1,
			time.Duration(10*time.Second),
		),
		headers: map[string]string{
			"Authorization": apiKey,
		},
		healthCheck:    false,
		healthStatus:   "Not check",
		lastTimeUpdate: nil,
	}
}

// HealthCheck is func healthcheck service
func (cli *Client) HealthCheck(context.Context) error {
	t := time.Now()
	cli.lastTimeUpdate = &t
	cli.healthStatus = "Checked"
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, nil, nil, pathHealthCheck, nil)
	if err != nil {
		cli.healthStatus = err.Error()
		cli.healthCheck = false
		return err
	}

	if res.Code != http.StatusOK {
		cli.healthStatus = "Service customer unavailable"
		cli.healthCheck = false
		return fmt.Errorf("%s", cli.healthStatus)
	}

	cli.healthCheck = true
	return nil
}

// GetListLevelCustomer is list level customer
func (cli *Client) GetListLevelCustomer() ([]*model.Level, error) {
	params := map[string]string{}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetAllListLevelCustomer, nil)
	if err != nil {
		return nil, err
	}

	var result *model.CustomerLevelResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}

	return result.Data, nil
}

// GetCustomerByAccountID ...
func (cli *Client) GetCustomerByAccountID(accountID int64) (*model.Customer, error) {

	params := map[string]string{
		"accountID":   fmt.Sprintf("%v", accountID),
		"accountType": "CUSTOMER",
	}

	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetCustomerByCode, nil)
	if err != nil {
		return nil, err
	}

	var result *model.CustomerResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}

	return result.Data[0], nil
}

// GetListCustomerByIDs ...
func (cli *Client) GetListCustomerByIDs(ids []int) ([]*model.Customer, error) {
	params := make(map[string]string)
	var payload = struct {
		Ids []int `json:"ids"`
	}{
		Ids: ids,
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, payload, pathGetCustomerByIDs, nil)
	if err != nil {
		return nil, err
	}

	var result *model.CustomerResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}

	return result.Data, nil
}

// IsOnline is func healthcheck service
func (cli *Client) IsOnline() bool {
	return cli.healthCheck
}

func (cli *Client) UpdatePoint(accountID int64, point int64, reason string) error {
	body := struct {
		AccountID int64  `json:"accountID"`
		Point     int64  `json:"point"`
		Type      string `json:"type"`
	}{
		AccountID: accountID,
		Point:     point,
		Type:      reason,
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, nil, body, pathUpdatePoint, nil)
	if err != nil {
		return err
	}

	var result *model.BaseAPIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return fmt.Errorf("%v", result.Message)
	}

	return nil
}
