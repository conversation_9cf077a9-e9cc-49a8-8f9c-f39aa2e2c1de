package api

import (
	"encoding/json"
	"strings"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/buymed.th/marketplace/promotion/action"
	"gitlab.com/buymed.th/marketplace/promotion/model"
)

var GetGamiLeaderboardsList = func(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset       = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit        = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal     = req.GetParam("getTotal") == "true"
		q            = req.GetParam("q")
		missionIds   = req.GetParam("missionIds")
		gamiResultsQ = req.GetParam("gamiResultsQ")
	)

	query := model.GamificationLeaderBoard{}
	gamiResultsQuery := model.GamificationResult{}

	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not parse input data.",
			})

		}
	}

	if gamiResultsQ != "" {
		err := json.Unmarshal([]byte(gamiResultsQ), &gamiResultsQuery)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not parse input data.",
			})

		}
	}

	if missionIds != "" {
		arr := strings.Split(missionIds, ",")
		for _, v := range arr {
			query.GamificationDetailIDs = append(query.GamificationDetailIDs, sdk.ParseInt64(v, 0))
		}
	}

	if acc := getActionSource(req); acc != nil {
		if gamiResultsQ != "" {
			return resp.Respond(action.GetGamificationResultListWithFilter(&query, &gamiResultsQuery, offset, limit, getTotal))
		}
		return resp.Respond(action.GetGamificationLeaderboardList(&query, offset, limit, getTotal))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func GetListGamificationResultLog(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		q        = req.GetParam("q")
	)
	if limit < 0 {
		limit = 20
	}

	if limit > 1000 {
		limit = 1000
	}

	if offset < 0 {
		offset = 0
	}

	// fill query
	query := model.GamificationResultLog{}
	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not parse input data.",
			})

		}
	}
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.GetListGamificationResultLog(&query, offset, limit, getTotal))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func DeleteKeyGamificationResultLog(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		key = req.GetParam("key")
	)

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.DeleteKeyGamificationResultLog(key))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}
