package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/buymed.th/marketplace/promotion/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type Gamification struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	CreatedBy       int64              `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	UpdatedBy       int64              `json:"updatedBy,omitempty" bson:"updated_by,omitempty"`

	GamificationCode string `json:"gamificationCode,omitempty" bson:"gamification_code,omitempty"`
	GamificationID   int64  `json:"gamificationID,omitempty" bson:"gamification_id,omitempty"`

	CreatedBySystem string `json:"createdBySystem,omitempty" bson:"created_by_system,omitempty"` // SELLER_CENTER, NORMAL, ""
	SellerCode      string `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`

	Scope            *GamificationScope `json:"scope,omitempty" bson:"scope,omitempty"`
	Name             string             `json:"name,omitempty" bson:"name,omitempty"`
	Description      string             `json:"description,omitempty" bson:"description,omitempty"`
	Banner           string             `json:"banner,omitempty" bson:"banner,omitempty"`
	StartTime        *time.Time         `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime          *time.Time         `json:"endTime,omitempty" bson:"end_time,omitempty"`
	EndCalResultTime *time.Time         `json:"endCalResultTime,omitempty" bson:"end_cal_result_time,omitempty"`
	PublicTime       *time.Time         `json:"publicTime,omitempty" bson:"public_time,omitempty"`
	RewardTime       *time.Time         `json:"rewardTime,omitempty" bson:"reward_time,omitempty"`

	// require customer to submit to join gamification
	RequireSubmit *bool `json:"requireSubmit,omitempty" bson:"require_submit,omitempty"`

	IsActive *bool  `json:"isActive,omitempty" bson:"is_active,omitempty"`
	HashTag  string `json:"-" bson:"hash_tag,omitempty"`

	// statistic
	NumberOfJoinedCustomer    *int `json:"numberOfJoinedCustomer,omitempty" bson:"number_of_joined_customer,omitempty"`
	NumberOfCompletedCustomer *int `json:"numberOfCompletedCustomer,omitempty" bson:"number_of_completed_customer,omitempty"`
	NumberOfDayCalResult      *int `json:"numberOfDayCalResult,omitempty" bson:"number_of_day_cal_result,omitempty"`

	ImageUrls       *[]string `json:"imageUrls" bson:"image_urls,omitempty"`
	Note            string    `json:"note,omitempty" bson:"note,omitempty"`
	Version         *string   `json:"version,omitempty" bson:"version,omitempty"`
	IsScoreRealTime *bool     `json:"isScoreRealTime,omitempty" bson:"is_score_real_time,omitempty"`

	// view only
	Details []*GamificationDetail `json:"details,omitempty" bson:"-"`

	// Status when execute generate gamification detail reward
	RewardStatus enum.RewardProgressType `json:"rewardStatus,omitempty" bson:"reward_status,omitempty"`

	// Customer submit status
	IsSubmitted bool `json:"isSubmitted,omitempty" bson:"-"`

	// query
	Status          string     `json:"status,omitempty" bson:"-"`
	ComplexQuery    []*bson.M  `json:"-" bson:"$and,omitempty"`
	CreatedTimeFrom *time.Time `json:"createdTimeFrom,omitempty" bson:"-"`
	CreatedTimeTo   *time.Time `json:"createdTimeTo,omitempty" bson:"-"`

	ProcessingTimeFrom *time.Time `json:"processingTimeFrom,omitempty" bson:"-"`
	ProcessingTimeTo   *time.Time `json:"processingTimeTo,omitempty" bson:"-"`

	IsRunParallel *bool `json:"isRunParallel,omitempty" bson:"is_run_parallel,omitempty"` //true: parallel, false: Sequential

	MaxQuantityShare    *int64 `json:"maxQuantityShare,omitempty" bson:"max_quantity_share,omitempty"`
	IsShareGamification *bool  `json:"isShareGamification,omitempty" bson:"is_share_gamification,omitempty"`
}

type GamificationScope struct {
	CustomerIDs       []int64  `json:"customerIDs,omitempty" bson:"customer_ids,omitempty"`
	CustomerScopes    []string `json:"customerScopes,omitempty" bson:"customer_scopes,omitempty"`
	CustomerAreas     []string `json:"customerAreas,omitempty" bson:"customer_areas,omitempty"`
	CustomerLevels    []string `json:"customerLevels,omitempty" bson:"customer_levels,omitempty"`
	CustomerApplyType string   `json:"customerApplyType,omitempty" bson:"customer_apply_type,omitempty"` // ALL || MANY
	Description       string   `json:"description,omitempty" bson:"description,omitempty"`
}

var GamificationDB = &db.Instance{
	ColName:        "gamification",
	TemplateObject: &Gamification{},
}

// InitGamificationModel is func init model
func InitGamificationModel(s *mongo.Database) {
	GamificationDB.ApplyDatabase(s)

	// t := true
	// GamificationDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "gamification_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })

	// GamificationDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "gamification_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })
}
