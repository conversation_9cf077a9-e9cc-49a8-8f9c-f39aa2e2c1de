package api

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/buymed.th/marketplace/promotion/action"
	"gitlab.com/buymed.th/marketplace/promotion/model"
)

func GetGameQuestions(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		limit     = sdk.ParseInt64(req.GetParam("limit"), 20)
		offset    = sdk.ParseInt64(req.GetParam("offset"), 0)
		getTotal  = req.GetParam("getTotal") == "true"
		code      = req.GetParam("code")
		isEnabled = req.GetParam("isEnabled")
	)

	var getByStatus *bool
	if isEnabled != "" {
		status := isEnabled == "true"
		getByStatus = &status
	}

	return resp.Respond(action.GetGameQuestions(offset, limit, getTotal, code, getByStatus))
}

func CreateGameQuestion(req sdk.APIRequest, resp sdk.APIResponder) error {
	var question model.GameQuestion
	if err := req.GetContent(&question); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Cannot parse input data.",
		})
	}
	return resp.Respond(action.CreateGameQuestion(question))
}

func UpdateGameQuestion(req sdk.APIRequest, resp sdk.APIResponder) error {
	var question model.GameQuestion
	if err := req.GetContent(&question); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Cannot parse input data.",
		})
	}
	return resp.Respond(action.UpdateGameQuestion(question))
}

func DeleteGameQuestion(req sdk.APIRequest, resp sdk.APIResponder) error {
	code := req.GetParam("code")
	return resp.Respond(action.DeleteGameQuestion(code))
}

func GetGameQuestionSpin(req sdk.APIRequest, resp sdk.APIResponder) error {
	wheelCode := req.GetParam("wheelCode")

	return resp.Respond(action.GetGameQuestionSpin(wheelCode))
}
