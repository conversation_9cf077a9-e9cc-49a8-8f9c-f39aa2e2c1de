package action

import (
	"fmt"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/buymed.th/marketplace/promotion/model"
	"gitlab.com/buymed.th/marketplace/promotion/utils"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func TrackingCreate(acc *model.Account, tracking *model.Tracking) *common.APIResponse {
	go func() {
		qEvent := model.TrackingEventDB.QueryOne(model.TrackingEvent{EventAction: tracking.EventAction})
		if qEvent.Status != common.APIStatus.Ok {
			return
		}
		event := qEvent.Data.([]*model.TrackingEvent)[0]
		tracking.EventLabel = event.EventLabel
		normEventLabelStr := strings.Replace(utils.NormalizeString(tracking.EventLabel), " ", "-", -1)
		normEventActionStr := strings.Replace(utils.NormalizeString(tracking.EventAction), " ", "-", -1)
		normPageStr := strings.Replace(utils.NormalizeString(tracking.Page), " ", "-", -1)
		tracking.HashTag = fmt.Sprintf("%s-%s-%s", normEventLabelStr, normEventActionStr, normPageStr)
		now := time.Now()
		now = now.Add(7 * time.Hour)
		tracking.TimeByYear = now.Year()
		tracking.TimeByMonth = now.Month()
		tracking.TimeByDay = now.Day()
		tracking.TimeByHour = now.Hour()
		model.TrackingDB.Create(tracking)
	}()
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Success",
	}
}

func TrackingGetList(query *model.Tracking, offset, limit int64, getTotal bool) *common.APIResponse {
	result := model.TrackingDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if getTotal {
		countResult := model.TrackingDB.Count(query)
		result.Total = countResult.Total
	}
	return result
}

func TrackingStatistic(query *model.Tracking) *common.APIResponse {
	type data struct {
		ID struct {
			EventAction string     `json:"eventAction" bson:"event_action"`
			TimeByYear  int        `json:"timeByYear,omitempty" bson:"time_by_year,omitempty"`
			TimeByMonth time.Month `json:"timeByMonth,omitempty" bson:"time_by_month,omitempty"`
			TimeByDay   int        `json:"timeByDay,omitempty" bson:"time_by_day,omitempty"`
			TimeByHour  int        `json:"timeByHour,omitempty" bson:"time_by_hour,omitempty"`
		} `json:"event" bson:"_id"`
		Total int `json:"total" bson:"total"`
	}
	result := make([]data, 0)
	groupID := primitive.M{
		"event_action": "$event_action",
	}
	if query.StatisticBy == "year" {
		groupID["time_by_year"] = "$time_by_year"
	}

	if query.StatisticBy == "month" {
		groupID["time_by_month"] = "$time_by_month"
		groupID["time_by_year"] = "$time_by_year"
	}

	if query.StatisticBy == "day" {
		groupID["time_by_day"] = "$time_by_day"
		groupID["time_by_month"] = "$time_by_month"
		groupID["time_by_year"] = "$time_by_year"
	}

	if query.StatisticBy == "hour" {
		groupID["time_by_day"] = "$time_by_day"
		groupID["time_by_month"] = "$time_by_month"
		groupID["time_by_year"] = "$time_by_year"
		groupID["time_by_hour"] = "$time_by_hour"
	}

	qTracking := model.TrackingDB.Aggregate([]primitive.M{
		{
			"$match": query,
		},
		{
			"$group": primitive.M{
				//"_id": primitive.M{
				//	"event_action": "$event_action",
				//	"time_by_hour": "$time_by_hour",
				//},
				"_id":   groupID,
				"total": primitive.M{"$sum": 1},
			},
		},
	}, &result)
	qTracking.Data = result
	return qTracking
}

func TrackingEventCreate(acc *model.Account, event *model.TrackingEvent) *common.APIResponse {
	normEventLabelStr := strings.Replace(utils.NormalizeString(event.EventLabel), " ", "-", -1)
	normEventActionStr := strings.Replace(utils.NormalizeString(event.EventAction), " ", "-", -1)
	event.HashTag = fmt.Sprintf("%s-%s", normEventLabelStr, normEventActionStr)
	return model.TrackingEventDB.Create(event)
}

func TrackingEventGetList(query *model.TrackingEvent, offset, limit int64, getTotal bool) *common.APIResponse {
	result := model.TrackingEventDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if getTotal {
		countResult := model.TrackingEventDB.Count(query)
		result.Total = countResult.Total
	}
	return result
}
