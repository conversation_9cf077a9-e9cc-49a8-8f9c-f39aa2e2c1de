package api

import (
	"fmt"
	"reflect"
	"strings"

	"gitlab.com/buymed.th/marketplace/promotion/action"
	"gitlab.com/buymed.th/marketplace/promotion/model"
	"gitlab.com/buymed.th/marketplace/promotion/model/enum"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// VoucherCheckWithCart ...
func VoucherCheckWithCart(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input = struct {
		VoucherCodes        []string   `json:"voucherCodes,omitempty" validate:"required"`
		Cart                model.Cart `json:"cart,omitempty" validate:"required"`
		AccountID           int64      `json:"accountId,omitempty"`
		GetVoucherAutoApply bool       `json:"getVoucherAutoApply"`

		SourceDetail *model.OrderSourceDetail `json:"sourceDetail,omitempty" bson:"-"`
	}{}
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}
	mapVoucherAuto := make(map[string]bool)
	for _, v := range input.Cart.RedeemApplyResult {
		if v.AutoApply {
			mapVoucherAuto[v.Code] = true
		}
	}
	newVoucherCodes := make([]string, 0)
	for _, v := range input.VoucherCodes {
		if _, ok := mapVoucherAuto[v]; !ok {
			newVoucherCodes = append(newVoucherCodes, v)
		}
	}
	input.VoucherCodes = newVoucherCodes
	input.Cart.SourceDetail = input.SourceDetail

	return resp.Respond(action.VoucherCheckWithCart(input.AccountID, input.VoucherCodes, &input.Cart, input.GetVoucherAutoApply))
}

// VoucherActiveList ...
func VoucherActiveList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input = struct {
		Cart        model.Cart `json:"cart,omitempty" validate:"required"`
		Offset      int64      `json:"offset"`
		Limit       int64      `json:"limit"`
		GetTotal    bool       `json:"getTotal"`
		GetValidate bool       `json:"getValidate"`
		Search      string     `json:"search"`
		AccountID   int64      `json:"accountId"`
		Scope       string     `json:"scope"`

		// SystemDisplay string `json:"systemDisplay,omitempty" bson:"-"`

		SourceDetail *model.OrderSourceDetail `json:"sourceDetail,omitempty" bson:"-"`
	}{}
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	isCartEmpty := reflect.DeepEqual(input.Cart, model.Cart{}) || len(input.Cart.CartItems) == 0
	input.Cart.IsCartEmpty = isCartEmpty
	// filter cart
	if input.Cart.CartItems != nil && input.Cart.TotalItem > 0 {
		newCartItems := make([]model.CartItemInternal, 0)
		input.Cart.Price = 0
		for _, item := range input.Cart.CartItems {
			if item.ProductSKU == "" {
				continue
			}
			if item.IsSelected == nil || (item.IsSelected != nil && *item.IsSelected) {
				newCartItems = append(newCartItems, item)
				input.Cart.Price += item.Total
			}
		}
		input.Cart.CartItems = newCartItems
		input.Cart.TotalItem = len(newCartItems)
	}
	if input.SourceDetail == nil {
		usInfo := getUAInfo(req.GetHeader("User-Agent"))
		sourceDetail := &model.OrderSourceDetail{
			Os:             usInfo.OSName,
			OsVersion:      usInfo.OSVersion,
			Browser:        usInfo.ClientName,
			BrowserVersion: usInfo.ClientVersion,
			Platform:       usInfo.Platform,
		}
		input.SourceDetail = sourceDetail
	}

	// Save current device,platform detail to cart model for validation
	input.Cart.SourceDetail = input.SourceDetail
	input.Cart.RedeemCode = []string{}
	if len(input.Cart.RedeemApplyResult) > 0 {
		for _, voucher := range input.Cart.RedeemApplyResult {
			if voucher.Code != "" {
				input.Cart.RedeemCode = append(input.Cart.RedeemCode, voucher.Code)
			}
		}
	}
	if input.Cart.TotalItem == 0 {
		input.Cart.TotalItem = len(input.Cart.CartItems)
	}
	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	if input.Offset < 0 {
		input.Offset = 0
	}

	if input.Limit <= 0 {
		input.Limit = 20
	}
	input.Limit = 1000
	var query = model.Voucher{}
	if input.Search != "" {
		searchItem := parserQ(input.Search)
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"$or": []*bson.M{
				{
					"hash_tag": primitive.Regex{Pattern: fmt.Sprintf(".*%s.*", searchItem), Options: ""},
					"type":     enum.VoucherType.PUBLIC,
				},
				{
					"code": strings.ToUpper(input.Search),
					"type": enum.VoucherType.PRIVATE,
				},
			},
		})
	} else if len(input.Cart.RedeemCode) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"$or": []*bson.M{
				{
					"type": enum.VoucherType.PUBLIC,
				},
				{
					// Get private voucher but in cart
					"code": &bson.M{
						"$in": input.Cart.RedeemCode,
					},
				},
			},
		})
	} else {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"type": enum.VoucherType.PUBLIC,
		})
	}
	if input.AccountID == 0 && getActionSource(req) != nil {
		acc := getActionSource(req)
		input.AccountID = acc.AccountID
	}
	// query.SystemDisplay = input.SystemDisplay
	return resp.Respond(action.VoucherActiveList(
		input.AccountID,
		&query,
		&input.Cart,
		input.Offset,
		input.Limit,
		input.GetTotal,
		input.GetValidate,
		input.Scope,
		// input.Search,
	))
}

// SelfVoucherActiveList ...
func SelfVoucherActiveList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = int64(sdk.ParseInt(req.GetParam("offset"), 0))
		limit    = int64(sdk.ParseInt(req.GetParam("limit"), 20))
		getTotal = req.GetParam("getTotal") == "true"
		search   = req.GetParam("search")
		scope    = req.GetParam("scope")

		// systemDisplay = req.GetParam("systemDisplay")
	)

	usInfo := getUAInfo(req.GetHeader("User-Agent"))
	sourceDetail := &model.OrderSourceDetail{
		Os:             usInfo.OSName,
		OsVersion:      usInfo.OSVersion,
		Browser:        usInfo.ClientName,
		BrowserVersion: usInfo.ClientVersion,
		Platform:       usInfo.Platform,
	}

	var query = model.Voucher{
		// SystemDisplay: systemDisplay,
	}
	if search != "" {
		searchItem := parserQ(search)
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"$or": []*bson.M{
				{
					"hash_tag": primitive.Regex{Pattern: fmt.Sprintf(".*%s.*", searchItem), Options: ""},
					"type":     enum.VoucherType.PUBLIC,
				},
				{
					"code": strings.ToUpper(search),
					"type": enum.VoucherType.PRIVATE,
				},
			},
		})
	} else {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"type": enum.VoucherType.PUBLIC,
		})
	}

	// if query.SystemDisplay == "" {
	// 	query.SystemDisplay = "BUYMED"
	// }

	return resp.Respond(action.SelfVoucherActiveList(getActionSource(req).AccountID, &query, offset, limit, getTotal, scope, sourceDetail)) // sourceDetail

}
