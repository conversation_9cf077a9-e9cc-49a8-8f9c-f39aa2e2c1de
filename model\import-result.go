package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type ImportResult struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	CreatedBy       int64               `json:"createdBy,omitempty" bson:"created_by,omitempty"`

	ModelName      string `json:"modelName,omitempty" bson:"model_name,omitempty"`
	Status         string `json:"status,omitempty" bson:"status,omitempty"`
	Code           string `json:"code,omitempty" bson:"code,omitempty"`
	Path           string `json:"path,omitempty" bson:"path,omitempty"`
	Success        int    `json:"success,omitempty" bson:"success,omitempty"`
	Fail           int    `json:"fail,omitempty" bson:"fail,omitempty"`
	Total          int    `json:"total,omitempty" bson:"total,omitempty"`
	ProcessingTime int    `json:"processingTime,omitempty" bson:"processing_time,omitempty"`

	AccountFullname string `json:"accountFullname,omitempty" bson:"account_fullname,omitempty"`
	Username        string `json:"username,omitempty" bson:"username,omitempty"`
	AccountID       int64  `json:"accountID,omitempty" bson:"account_id,omitempty"`

	CampaignCode     string   `json:"campaignCode,omitempty" bson:"campaign_code,omitempty"`
	VoucherCodeFails []string `json:"voucherCodeFails,omitempty" bson:"voucher_code_fails,omitempty"`

	ComplexQuery      []*bson.M  `json:"-" bson:"$and,omitempty"`
	CreatedTimeFrom   *time.Time `json:"createdTimeFrom,omitempty" bson:"-"`
	CreatedTimeTo     *time.Time `json:"createdTimeTo,omitempty" bson:"-"`
	CompletedTimeFrom *time.Time `json:"completedTimeFrom,omitempty" bson:"-"`
	CompletedTimeTo   *time.Time `json:"completedTimeTo,omitempty" bson:"-"`
}

type ImportItem struct {
	Code          string `json:"code,omitempty" bson:"code,omitempty"`
	Username      string `json:"username,omitempty" bson:"username,omitempty"`
	AccountID     int64  `json:"accountID,omitempty" bson:"account_id,omitempty"`
	ImportJobCode string `json:"importJobCode,omitempty" bson:"import_job_code,omitempty"`
}

// ImportResultDB ...
var ImportResultDB = &db.Instance{
	ColName:        "import_result",
	TemplateObject: &ImportResult{},
}

// InitImportResultModel is func init model import-result
func InitImportResultModel(s *mongo.Database) {
	ImportResultDB.ApplyDatabase(s)

	//t := true
	//_ = ImportResultDB.CreateIndex(bson.D{
	//	primitive.E{Key: "key", Value: 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//})
}
