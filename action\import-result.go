package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/buymed.th/marketplace/promotion/model"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func ImportResultGetList(acc *model.Account, query *model.ImportResult, offset, limit int64, getTotal bool, sort string) *common.APIResponse {
	result := model.ImportResultDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if result.Status != common.APIStatus.Ok {
		return result
	}
	if getTotal {
		result.Total = model.ImportResultDB.Count(query).Total
	}

	return result
}

func ImportResultDetailGetList(acc *model.Account, query *model.ImportResultDetail, offset, limit int64, getTotal bool, sort string) *common.APIResponse {
	result := model.ImportResultDetailDB.Query(query, offset, limit, &primitive.M{"_id": 1})
	if result.Status != common.APIStatus.Ok {
		return result
	}
	if getTotal {
		result.Total = model.ImportResultDetailDB.Count(query).Total
	}

	return result
}
