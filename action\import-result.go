package action

import (
	"fmt"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/buymed.th/marketplace/promotion/model"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func ImportResultGetList(acc *model.Account, query *model.ImportResult, offset, limit int64, getTotal bool, sort string) *common.APIResponse {
	result := model.ImportResultDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if result.Status != common.APIStatus.Ok {
		return result
	}
	if getTotal {
		result.Total = model.ImportResultDB.Count(query).Total
	}

	return result
}

func ImportResultDetailGetList(acc *model.Account, query *model.ImportResultDetail, offset, limit int64, getTotal bool, sort string) *common.APIResponse {
	result := model.ImportResultDetailDB.Query(query, offset, limit, &primitive.M{"_id": 1})
	if result.Status != common.APIStatus.Ok {
		return result
	}
	if getTotal {
		result.Total = model.ImportResultDetailDB.Count(query).Total
	}

	return result
}

func ImportBatchGetListVoucher(acc *model.Account, query *model.VoucherImportBatch, offset, limit int64, getTotal bool, sort string) *common.APIResponse {
	fmt.Printf("DEBUG: ImportBatchGetListVoucher called with query: %+v\n", query)
	fmt.Printf("DEBUG: ComplexQuery: %+v\n", query.ComplexQuery)

	result := model.VoucherImportBatchDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	fmt.Printf("DEBUG: Query result status: %s, data count: %d\n", result.Status, len(result.Data.([]*model.VoucherImportBatch)))

	if result.Status != common.APIStatus.Ok {
		return result
	}
	if getTotal {
		result.Total = model.VoucherImportBatchDB.Count(query).Total
	}

	return result
}
func ImportBatchDetailGetList(acc *model.Account, query *model.VoucherImportBatchDetail, offset, limit int64, getTotal bool, sort string) *common.APIResponse {
	result := model.VoucherImportBatchDetailDB.Query(query, offset, limit, &primitive.M{"_id": 1})
	if result.Status != common.APIStatus.Ok {
		return result
	}
	if getTotal {
		result.Total = model.VoucherImportBatchDetailDB.Count(query).Total
	}

	return result
}
