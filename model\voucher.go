package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/buymed.th/marketplace/promotion/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type UpdateManyUserVoucherRequest struct {
	Status   *enum.VoucherStatusValue `json:"status,omitempty"`
	EndTime  *time.Time               `json:"endTime,omitempty"`
	Vouchers []Voucher                `json:"vouchers,omitempty"`
}

type CreateManyVoucherRequest struct {
	Vouchers []Voucher `json:"vouchers"`
}

type Voucher struct {
	ID                primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime       *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	CreatedBy         int64              `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	CreatedByUserName *string            `json:"createdByUserName,omitempty" bson:"created_by_user_name,omitempty"`
	LastUpdatedTime   *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	UpdatedBy         int64              `json:"updatedBy,omitempty" bson:"updated_by,omitempty"`
	VersionNo         string             `json:"versionNo,omitempty" bson:"version_no,omitempty"`

	DisplayName          string                                    `json:"displayName,omitempty" bson:"display_name,omitempty"`
	ShortName            string                                    `json:"shortName,omitempty" bson:"short_name,omitempty"`
	VoucherID            int64                                     `json:"voucherId,omitempty" bson:"voucher_id,omitempty"`
	Code                 string                                    `json:"code,omitempty" bson:"code,omitempty"`
	PromotionID          int64                                     `json:"promotionId,omitempty" bson:"promotion_id,omitempty"`
	PromotionName        string                                    `json:"promotionName,omitempty" bson:"promotion_name,omitempty"`
	PromotionType        *enum.PromotionTypeValue                  `json:"promotionType,omitempty" bson:"promotion_type,omitempty"`
	ApplyType            enum.ApplyTypeValue                       `json:"applyType,omitempty" bson:"apply_type,omitempty"`
	StartTime            *time.Time                                `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime              *time.Time                                `json:"endTime,omitempty" bson:"end_time,omitempty"`
	PublicTime           *time.Time                                `json:"publicTime,omitempty" bson:"public_time,omitempty"`
	MaxUsage             *int64                                    `json:"maxUsage" bson:"max_usage,omitempty"`
	MaxUsagePerCustomer  *int64                                    `json:"maxUsagePerCustomer" bson:"max_usage_per_customer,omitempty"`
	MaxAutoApplyCount    *int64                                    `json:"maxAutoApplyCount,omitempty" bson:"max_auto_apply_count,omitempty"`
	UsageTotal           *int64                                    `json:"usageTotal" bson:"usage_total,omitempty"`
	VoucherType          *enum.VoucherTypeValue                    `json:"type,omitempty" bson:"type,omitempty"`
	VoucherGroupCode     *string                                   `json:"voucherGroupCode,omitempty" bson:"voucher_group_code,omitempty"`
	UsageType            *enum.UsageTypeValue                      `json:"usageType,omitempty" bson:"usage_type,omitempty"`
	AppliedCustomers     *[]int64                                  `json:"appliedCustomers" bson:"applied_customers,omitempty"`
	AppliedCustomerMap   map[int64]bool                            `json:"-" bson:"applied_customer_map,omitempty"`
	Status               *enum.VoucherStatusValue                  `json:"status,omitempty" bson:"status,omitempty"`
	Promotion            *Promotion                                `json:"promotion,omitempty" bson:"-"`
	UserPromotion        *UserPromotion                            `json:"userPromotion,omitempty" bson:"-"`
	HashTag              string                                    `json:"-" bson:"hash_tag,omitempty"`
	Usable               *bool                                     `json:"-" bson:"usable,omitempty"`
	Scopes               []Scope                                   `json:"scopes,omitempty" bson:"scopes,omitempty"`
	Rewards              []Reward                                  `json:"rewards,omitempty" bson:"rewards,omitempty"`
	OrConditions         map[enum.ConditionTypeValue]PromotionType `json:"orConditions,omitempty" bson:"or_conditions,omitempty"`
	AndConditions        map[enum.ConditionTypeValue]PromotionType `json:"andConditions,omitempty" bson:"and_conditions,omitempty"`
	ConditionDescription *string                                   `json:"conditionDescription,omitempty" bson:"condition_description,omitempty"`
	Priority             *int                                      `json:"priority,omitempty" bson:"priority,omitempty"`
	CustomerApplyType    enum.CustomerApplyTypeValue               `json:"customerApplyType,omitempty" bson:"customer_apply_type,omitempty"`
	IsSkipNextVoucher    *bool                                     `json:"isSkipNextVoucher,omitempty" bson:"is_skip_next_voucher,omitempty"`
	LevelScopeMap        map[string]bool                           `json:"-" bson:"level_scope_map,omitempty"`
	RegionScopeMap       map[string]bool                           `json:"-" bson:"region_scope_map,omitempty"`
	CustomerScopeMap     map[string]bool                           `json:"-" bson:"customer_scope_map,omitempty"`
	SystemNote           string                                    `json:"-" bson:"system_note,omitempty"`
	RefProduct           []string                                  `json:"-" bson:"ref_product,omitempty"`
	IsMigrateUserCode    bool                                      `json:"-" bson:"is_migrate_user_code,omitempty"`
	VoucherImage         *string                                   `json:"voucherImage,omitempty" bson:"voucher_image,omitempty"`
	Tag                  *string                                   `json:"tag,omitempty" bson:"tag,omitempty"`

	SellerCode  *string   `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	SellerCodes *[]string `json:"sellerCodes,omitempty" bson:"seller_codes,omitempty"`
	SellerName  *string   `json:"sellerName,omitempty" bson:"seller_name,omitempty"`

	NeedCheck *bool `json:"_" bson:"need_check,omitempty"`

	PromotionOrganizer *enum.PromotionOrganizerValue `json:"promotionOrganizer,omitempty" bson:"promotion_organizer,omitempty"`

	// For filter
	TimeFrom    *time.Time `json:"timeFrom" bson:"-"`
	TimeTo      *time.Time `json:"timeTo" bson:"-"`
	CustomerID  *int64     `json:"customerId,omitempty" bson:"-"`
	ProductCode string     `json:"productCode,omitempty" bson:"-"`

	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
	CanUse       bool      `json:"canUse,omitempty" bson:"-"`
	SkipReturn   bool      `json:"-" bson:"-"`
	ErrorMessage string    `json:"errorMessage,omitempty" bson:"-"`
	ErrorCode    string    `json:"errorCode,omitempty" bson:"-"`
	Discount     float64   `json:"discount,omitempty" bson:"-"`
	Gifts        []Gift    `json:"gifts,omitempty" bson:"-"`

	ApplyDiscount      *ApplyDiscountOptions `json:"applyDiscount,omitempty" bson:"apply_discount,omitempty"`
	PriceToCalDiscount int64                 `json:"priceToCalDiscount,omitempty" bson:"-"`
	DiscountInfos      []DiscountInfo        `json:"discountInfos,omitempty" bson:"-"`
}

type RefProduct struct {
	SellerCode  string `json:"-" bson:"seller_code,omitempty"`
	ProductCode string `json:"-" bson:"product_code,omitempty"`
	Sku         string `json:"-" bson:"sku,omitempty"`
}

type ApplyDiscountOptions struct {
	Tags      *[]string `json:"tags,omitempty" bson:"tags,omitempty"`
	NotInTags *[]string `json:"notInTags,omitempty" bson:"not_in_tags,omitempty"`
	Skus      *[]string `json:"skus,omitempty" bson:"skus,omitempty"`
	NotInSkus *[]string `json:"notInSkus,omitempty" bson:"not_in_skus,omitempty"`

	SkuName map[string]string `json:"skuName,omitempty" bson:"sku_name,omitempty"`
}

var VoucherDB = &db.Instance{
	ColName:        "voucher",
	TemplateObject: &Voucher{},
}

// InitVoucherModel is func init model
func InitVoucherModel(s *mongo.Database) {
	VoucherDB.ApplyDatabase(s)
}

type VoucherViewWebOnly struct {
	DisplayName          string     `json:"displayName,omitempty" bson:"-"`
	VoucherID            int64      `json:"voucherId,omitempty" bson:"-"`
	Code                 string     `json:"code,omitempty" bson:"-"`
	PromotionName        string     `json:"promotionName,omitempty" bson:"-"`
	Description          string     `json:"description,omitempty" bson:"-"`
	StartTime            *time.Time `json:"startTime,omitempty" bson:"-"`
	EndTime              *time.Time `json:"endTime,omitempty" bson:"-"`
	PublicTime           *time.Time `json:"publicTime,omitempty" bson:"-"`
	ConditionDescription *string    `json:"conditionDescription,omitempty" bson:"-"`
	CanUse               bool       `json:"canUse,omitempty" bson:"-"`
	ErrorMessage         string     `json:"errorMessage,omitempty" bson:"-"`
	ErrorCode            string     `json:"errorCode,omitempty" bson:"-"`
	Discount             float64    `json:"discount,omitempty" bson:"-"`
	Gifts                []Gift     `json:"gifts,omitempty" bson:"-"`
	MaxUsage             *int64     `json:"maxUsage,omitempty" bson:"-"`
	UsageTotal           *int64     `json:"usageTotal,omitempty" bson:"-"`
	Tag                  *string    `json:"tag,omitempty" bson:"-"`
	Voucher              struct {   // app old version
		Code             string     `json:"code,omitempty"`
		AppliedCustomers []int64    `json:"appliedCustomers" bson:"-"`
		PromotionName    string     `json:"promotionName,omitempty" bson:"-"`
		StartTime        *time.Time `json:"startTime,omitempty" bson:"-"`
		EndTime          *time.Time `json:"endTime,omitempty" bson:"-"`
		Promotion        struct {
			Description string `json:"description,omitempty"`
		} `json:"promotion,omitempty"`
	} `json:"voucher,omitempty" bson:"-"`

	ActionStatus string              `json:"actionStatus,omitempty" bson:"-"` // AVAILABLE, DISABLE, INUSE, INVALID
	GroupCode    string              `json:"groupCode,omitempty"`
	ApplyType    enum.ApplyTypeValue `json:"applyType,omitempty" bson:"-"`
	SellerCode   string              `json:"sellerCode,omitempty" bson:"-"`
}
type DiscountInfo struct {
	Discount float64 `json:"discount,omitempty"`
	Sku      string  `json:"sku,omitempty"`
	IsApply  bool    `json:"isApply,omitempty"`
	Message  string  `json:"message,omitempty"`

	SellerCodes []string `json:"sellerCodes,omitempty"`
}

var VoucherCacheDB = &db.Instance{
	ColName:        "voucher",
	TemplateObject: &Voucher{},
}

// InitVoucherCacheModel is func init model
func InitVoucherCacheModel(s *mongo.Database) {
	VoucherCacheDB.ApplyDatabase(s)
	t := true

	VoucherCacheDB.CreateIndex(bson.D{
		primitive.E{Key: "code", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	VoucherCacheDB.CreateIndex(bson.D{
		primitive.E{Key: "public_time", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	VoucherCacheDB.CreateIndex(bson.D{
		primitive.E{Key: "end_time", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	VoucherCacheDB.CreateIndex(bson.D{
		primitive.E{Key: "status", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	VoucherCacheDB.CreateIndex(bson.D{
		primitive.E{Key: "region_scope_map", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	VoucherCacheDB.CreateIndex(bson.D{
		primitive.E{Key: "level_scope_map", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	VoucherCacheDB.CreateIndex(bson.D{
		primitive.E{Key: "customer_scope_map", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	VoucherCacheDB.CreateIndex(bson.D{
		primitive.E{Key: "customer_apply_type", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	VoucherCacheDB.CreateIndex(bson.D{
		primitive.E{Key: "customer_apply_type", Value: 1},
		primitive.E{Key: "code", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	// index for query expired promotion
	VoucherCacheDB.CreateIndex(bson.D{
		primitive.E{Key: "public_time", Value: 1},
		primitive.E{Key: "end_time", Value: 1},
		primitive.E{Key: "status", Value: 1},
		primitive.E{Key: "code", Value: 1},
		primitive.E{Key: "apply_type", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

}
