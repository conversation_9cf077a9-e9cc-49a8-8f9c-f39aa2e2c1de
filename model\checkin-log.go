package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type CheckinLog struct {
	ID              primitive.ObjectID `json:"id,omitempty" bson:"_id,omitempty"`
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	CustomerID  int64  `json:"customerId,omitempty" bson:"customer_id,omitempty"`
	AccountID   int64  `json:"accountId,omitempty" bson:"account_id,omitempty"`
	CheckinCode string `json:"checkinCode,omitempty" bson:"checkin_code,omitempty"`
	ItemCode    string `json:"itemCode,omitempty" bson:"item_code,omitempty"`
	ItemIndex   int    `json:"itemIndex,omitempty" bson:"item_index,omitempty"`

	ImageUrl        string `json:"itemUrl,omitempty" bson:"item_url,omitempty"`
	TypeCheckinItem string `json:"typeCheckinItem,omitempty" bson:"type_checkin_item,omitempty"`
}

var CheckinLogDB = &db.Instance{
	ColName:        "checkin_log",
	TemplateObject: &CheckinLog{},
}

func InitCheckinLogModel(s *mongo.Database) {
	CheckinLogDB.ApplyDatabase(s)
}
