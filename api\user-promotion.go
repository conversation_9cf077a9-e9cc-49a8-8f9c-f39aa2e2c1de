package api

import (
	"encoding/json"
	"strconv"
	"strings"

	"go.mongodb.org/mongo-driver/bson"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/buymed.th/marketplace/promotion/action"
	"gitlab.com/buymed.th/marketplace/promotion/model"
	"gitlab.com/buymed.th/marketplace/promotion/model/enum"
)

// UserPromotionGet user code get
func UserPromotionGet(req sdk.APIRequest, resp sdk.APIResponder) error {
	// Check UpdatedBy
	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}
	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}

	var offset = int64(sdk.ParseInt(req.GetParam("offset"), 0))
	var limit = int64(sdk.ParseInt(req.GetParam("limit"), 20))
	var getTotal = req.GetParam("getTotal") == "true"
	return resp.Respond(
		action.UserPromotionList(model.UserPromotion{
			CustomerID: UserInfo.Account.AccountID,
			Status:     &enum.CodeStatus.ACTIVE,
		}, offset, limit, getTotal))
}

// // User use Promotion
func UsePromotion(req sdk.APIRequest, resp sdk.APIResponder) error {
	type myRequest struct {
		VoucherCode  string   `json:"voucherCode"`
		VoucherCodes []string `json:"voucherCodes"`
		OrderID      int      `json:"orderId"`
	}

	var input myRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Parsing input data error." + err.Error(),
		})
	}

	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error." + err.Error(),
			})
		}
	}

	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "ACCOUNT_NOT_FOUND",
			Message:   "Không tìm thấy thông tin tài khoản.",
		})
	}
	if input.VoucherCode != "" && len(input.VoucherCodes) == 0 {
		input.VoucherCodes = append(input.VoucherCodes, input.VoucherCode)
	}

	return resp.Respond(action.UseVoucher(UserInfo.Account.AccountID, 0, input.VoucherCodes))
}

// UsePromotion is func User use Promotion
// Author: tamnt
func UsedPromotion(req sdk.APIRequest, resp sdk.APIResponder) error {
	type myRequest struct {
		VoucherCode  string   `json:"voucherCode"`
		VoucherCodes []string `json:"voucherCodes"`
		AccountID    int64    `json:"accountId" validate:"required"`
		OrderID      int64    `json:"orderId" validate:"required"`
	}

	var input myRequest
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Parsing input data error." + err.Error(),
		})
	}
	if input.VoucherCode != "" && len(input.VoucherCodes) == 0 {
		input.VoucherCodes = append(input.VoucherCodes, input.VoucherCode)
	}
	return resp.Respond(action.UseVoucher(input.AccountID, input.OrderID, input.VoucherCodes))
}

func UserVoucherGetList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset      = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit       = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal    = req.GetParam("getTotal") == "true"
		q           = req.GetParam("q")
		canUse      = req.GetParam("canUse") == "true"
		customerIds = req.GetParam("customerIds")
	)

	query := model.UserPromotion{}

	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: "INVALID",
			})
		}
	}

	if canUse {
		query.ComplexQuery = []*bson.M{
			{
				"status": bson.M{"$nin": []string{string(enum.CodeStatus.DELETED), string(enum.CodeStatus.INACTIVE)}},
			},
		}
	}
	if query.Status != nil && *query.Status == enum.CodeStatus.USED {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"amount": bson.M{"$gte": 0},
		})
		query.Status = nil
	}

	if len(customerIds) > 0 {
		idsArr := strings.Split(customerIds, ",")
		listIDs := []int64{}
		for _, id := range idsArr {
			intID, _ := strconv.Atoi(id)
			if intID > 0 {
				listIDs = append(listIDs, int64(intID))
			}
		}
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"customer_id": &bson.M{
				"$in": listIDs,
			},
		})
	}

	return resp.Respond(action.GetListUserVoucher(&query, offset, limit, getTotal))
}

// CreateManyUserVoucher
func CreateManyUserVoucher(req sdk.APIRequest, resp sdk.APIResponder) error {
	var userVouchers model.CreateManyUserVoucherRequest
	if err := req.GetContent(&userVouchers); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data. " + err.Error(),
		})
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.CreateManyUserVoucher(acc, &userVouchers))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

// UpdateManyUserVoucher
func UpdateManyUserVoucher(req sdk.APIRequest, resp sdk.APIResponder) error {
	var vouchers model.UpdateManyUserVoucherRequest
	if err := req.GetContent(&vouchers); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data. " + err.Error(),
		})
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.UpdateManyUserVoucher(acc, &vouchers))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func UserVoucherCreate(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.UserPromotion
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data. " + err.Error(),
		})
	}

	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.CreateUserVoucher(acc, &input))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func UserVoucherUpdate(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.UserPromotion
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data. " + err.Error(),
		})
	}

	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.UpdateUserVoucher(acc, &input))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}
