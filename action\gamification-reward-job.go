package action

import (
	"fmt"
	"runtime/debug"
	"strconv"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/buymed.th/marketplace/promotion/client"
	"gitlab.com/buymed.th/marketplace/promotion/model"
	"gitlab.com/buymed.th/marketplace/promotion/model/enum"
	"gitlab.com/buymed.th/marketplace/promotion/utils"
	"go.mongodb.org/mongo-driver/bson"
)

const (
	ADDITIONAL_PRICE      = 500000
	MIN_PRICE_MESSAGE     = "Applicable to orders from"
	REWARD_GEN_JOB_OBJECT = "REWARD_GEN_JOB"

	// min absolute discount of a voucher should greater than 5000VND
	MIN_DISCOUNT = 5000
)

type GamiJobResult struct {
	*model.GamificationResult
	Reward model.GamificationDetailReward
	Acc    model.Account
	Gami   model.Gamification

	CompletedResultsCount int
}

func (gjr *GamiJobResult) setGamiDetailReward(detail *model.GamificationDetail) error {
	if detail == nil || detail.Reward == nil {
		return fmt.Errorf("gamification detail or reward is nil")
	}
	gjr.Reward = *detail.Reward
	return nil
}

// pushRewardGenJobs creates reward gen items and push them to queue,
// it can push 1 or multiple jobs according to discount value and settings in reward,
// return number of pushed jobs
func (gjr *GamiJobResult) pushRewardGenJobs(completedCount *int, pushCount int) (int, error) {
	var err error
	if completedCount == nil {
		return pushCount, fmt.Errorf("completed count is nil")
	}

	discount := gjr.calDiscount()

	isVoucherDecoupled, voucherQntWithMaxDiscount, lastDiscount := gjr.decoupleVouchers(discount)

	// Case 1 voucher discount is small, push only 1 job
	if !isVoucherDecoupled {
		err = gjr.pushRewardGenJob(*completedCount, pushCount, discount, nil)
		if err == nil {
			pushCount++
		}
		return pushCount, nil
	}

	// Case 2 voucher discount is big, split it into multiple vouchers.
	// increase number of pushed jobs by number of addional vouchers
	if lastDiscount > 0 {
		*completedCount += voucherQntWithMaxDiscount
	} else {
		*completedCount += voucherQntWithMaxDiscount - 1
	}

	var subCount int
	newCode := gjr.makeVoucherCode(nil)
	for i := 0; i < voucherQntWithMaxDiscount; i++ {
		subCount = i + 1
		subCode := fmt.Sprintf("%s_%d", newCode, subCount)
		err = gjr.pushRewardGenJob(*completedCount, pushCount, *gjr.Reward.MaxVoucherDiscount, &subCode)
		if err == nil {
			pushCount++
		}
	}

	if lastDiscount > 0 {
		subCount = voucherQntWithMaxDiscount + 1
		subCode := fmt.Sprintf("%s_%d", newCode, subCount)
		err = gjr.pushRewardGenJob(*completedCount, pushCount, lastDiscount, &subCode)
		if err == nil {
			pushCount++
		}
	}

	return pushCount, nil
}

func (gjr *GamiJobResult) calDiscount() int {
	percent := gjr.Reward.ValuePercent
	value := gjr.Value
	if percent == nil {
		return 0
	}
	return utils.RoundPrice(int(*percent * float64(value)))
}

// decoupleVouchers checks if voucher discount is bigger than max voucher discount.
// If yes, split voucher into multiple vouchers with max voucher discount and
// limit number of vouchers by max voucher quantity
func (gjr *GamiJobResult) decoupleVouchers(discount int) (bool, int, int) {
	maxDiscount := gjr.Reward.MaxVoucherDiscount
	maxQnt := gjr.Reward.MaxVouchersQnt

	var voucherQntWithMaxDiscount = 0
	var lastDiscount = 0

	if maxDiscount != nil && discount > *maxDiscount {
		splitQnt := discount / *maxDiscount
		surplusDiscount := discount % *maxDiscount
		totalQnt := splitQnt
		if surplusDiscount >= MIN_DISCOUNT {
			totalQnt++
			lastDiscount = surplusDiscount
		}
		if maxQnt != nil && totalQnt > *maxQnt {
			voucherQntWithMaxDiscount = *maxQnt
			lastDiscount = 0
		} else {
			voucherQntWithMaxDiscount = splitQnt
		}
	}

	return voucherQntWithMaxDiscount > 0, voucherQntWithMaxDiscount, lastDiscount
}

// canPushRewardGenJob checks if customer completed the gamification and customer id exists
func (gjr *GamiJobResult) canPushRewardGenJob() error {
	if gjr == nil || gjr.Status != enum.GamificationResultStatus.COMPLETED {
		return fmt.Errorf("gamification result is nil or not completed")
	}

	customerID := gjr.CustomerID

	if customerID == 0 {
		return fmt.Errorf("customer id is not valid")
	}

	return nil
}

func (gjr *GamiJobResult) pushRewardGenJob(
	completedCount int,
	pushCount int,
	discount int,
	voucherCode *string,
) error {
	rvg := gjr.makeRewardVoucherGen(completedCount, pushCount, discount, voucherCode)

	return model.RewardGenJob.Push(
		rvg,
		&job.JobItemMetadata{
			UniqueKey: model.GenCode(REWARD_GEN_JOB_OBJECT),
		},
	)
}

func (gjr *GamiJobResult) makeRewardVoucherGen(
	completedCount int,
	pushCount int,
	discount int,
	voucherCode *string,
) *model.RewardVoucherGen {
	rgj := model.RewardVoucherGen{
		PromotionID: func(i *int64) int64 {
			if i == nil {
				return 0
			}
			return *i
		}(gjr.Reward.PromotionId),
		VoucherCode:           gjr.makeVoucherCode(voucherCode),
		CustomerID:            gjr.CustomerID,
		CustomerScope:         gjr.CustomerScope,
		EndTime:               *gjr.Gami.EndTime,
		GamificationDetailID:  gjr.GamificationDetailID,
		GamiResultID:          gjr.GamificationResultID,
		Account:               gjr.Acc,
		Discount:              discount,
		GamiCode:              gjr.GamificationCode,
		GamiID:                gjr.GamificationID,
		CompletedTotal:        completedCount,
		CurCount:              pushCount + 1,
		CompletedResultsCount: gjr.CompletedResultsCount,
	}

	return &rgj
}

// makeVoucherCode generates voucher code if arg is nil, otherwise return the arg
func (gjr *GamiJobResult) makeVoucherCode(code *string) string {
	if code == nil {
		_, newCode := model.GenVoucherID()
		newCode = fmt.Sprintf("%s_%s", "REWARD", newCode)
		code = &newCode
	}
	return *code
}

func ExecuteGamificationRewardJob(item *job.JobItem) error {
	data, err := bson.Marshal(item.Data)
	if err != nil {
		return err
	}
	var rewardGen model.RewardVoucherGen
	err = bson.Unmarshal(data, &rewardGen)
	if err != nil {
		return err
	} else {
		resp := createVoucherForCustomer(rewardGen)
		if resp != nil && resp.Status != common.APIStatus.Ok {
			return fmt.Errorf(resp.Message)
		}
	}
	defer func() {
		if r := recover(); r != nil {
			err := fmt.Errorf("panic CreateGamificationReward : %s", string(debug.Stack()))
			fmt.Println(err.Error())
		}
	}()
	return nil
}

func createVoucherForCustomer(rvg model.RewardVoucherGen) *common.APIResponse {
	// create simplest voucher with required fields, before applying data from promotion with promotion id
	simplestVoucher, err := newSimpleVoucherFromReward(rvg)

	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: err.Error(),
		}
	}

	// create vouchers based on promotion id
	voucherCreateResp := CreateVoucher(simplestVoucher, &rvg.Account)

	if voucherCreateResp.Status != common.APIStatus.Ok || voucherCreateResp.Data == nil {
		return voucherCreateResp
	}

	voucher := voucherCreateResp.Data.([]*model.Voucher)[0]

	userVoucher := newUserVoucher(rvg, rvg.CustomerID, voucher)

	// Connect voucher to user through updating user promotion db, push new job
	model.UserVoucherCreationJob.Push(model.UserVoucherJob{
		Account:        &rvg.Account,
		UserVoucher:    userVoucher,
		GamiResultID:   rvg.GamiResultID,
		CompletedTotal: rvg.CompletedTotal,
		CurCount:       rvg.CurCount,
		GamiCode:       rvg.GamiCode,

		CompletedResultsCount: rvg.CompletedResultsCount,
	}, &job.JobItemMetadata{
		Topic: "default",
	})

	return nil
}

func newUserVoucher(rvg model.RewardVoucherGen, customerID int64, voucher *model.Voucher) *model.UserPromotion {
	userVoucher := &model.UserPromotion{
		CustomerID:  customerID,
		VoucherCode: voucher.Code,
		Status:      &enum.CodeStatus.ACTIVE,
	}

	return userVoucher
}

func newSimpleVoucherFromReward(r model.RewardVoucherGen) (*model.Voucher, error) {
	resp := model.PromotionDB.QueryOne(&model.Promotion{
		PromotionID: r.PromotionID,
	})

	if resp.Status != common.APIStatus.Ok || resp.Data == nil {
		return nil, fmt.Errorf("could not find promotion with ID: %d", r.PromotionID)
	}

	promotion := resp.Data.([]*model.Promotion)[0]

	maxUsage := int64(1)
	maxUsagePerCustomer := int64(1)

	var rewards []model.Reward
	if r.Discount != 0 {
		rewards = []model.Reward{{
			Type:             &enum.RewardType.ABSOLUTE,
			AbsoluteDiscount: float64(r.Discount),
		}}
	}

	andConds := getAndConditions(promotion, &r)

	voucher := &model.Voucher{
		Code:                r.VoucherCode,
		StartTime:           promotion.StartTime,
		EndTime:             promotion.EndTime,
		PublicTime:          promotion.PublicTime,
		PromotionID:         r.PromotionID,
		VoucherType:         &enum.VoucherType.PUBLIC,
		MaxUsage:            &maxUsage,
		MaxUsagePerCustomer: &maxUsagePerCustomer,
		CustomerApplyType:   enum.CustomerApplyType.MANY,
		Rewards:             rewards,
		VoucherGroupCode:    promotion.VoucherGroupCode,
		// SystemDisplay: func() string {
		// 	if r.CustomerScope == string(enum.CustomerScope.DENTISTRY) {
		// 		return "BUYDENTAL"
		// 	}
		// 	return ""
		// }(),
		AndConditions: andConds,
		ConditionDescription: func() *string {
			if promotion.ConditionDescription != nil && len(*promotion.ConditionDescription) > 0 {
				return promotion.ConditionDescription
			}
			ovCond, ok := andConds[enum.ConditionType.ORDER_VALUE]
			if andConds != nil && ok && ovCond.OrConditions != nil && len(ovCond.OrConditions) > 0 {
				message := MIN_PRICE_MESSAGE + " " + utils.FormatVNDCurrency(strconv.Itoa(r.Discount+ADDITIONAL_PRICE)) + "đ"
				return &message
			}
			return nil
		}(),
	}

	return voucher, nil
}

/*
getAndConditions will return map of condition type and promotion type
  - Condition about min total price of created voucher
*/
func getAndConditions(
	promotion *model.Promotion, r *model.RewardVoucherGen,
) map[enum.ConditionTypeValue]model.PromotionType {
	conditions := make(map[enum.ConditionTypeValue]model.PromotionType)
	if r.Discount == 0 {
		return conditions
	}

	minTotalPrice := r.Discount + ADDITIONAL_PRICE

	// Check if promotion has no min total price ,or has min total price <= discount
	// then add min total price condition with value = discount + additional value
	if promotion == nil || !isPromotionMinPriceGTDiscount(promotion, r.Discount) {
		conditions[enum.ConditionType.ORDER_VALUE] = model.PromotionType{
			OrConditions: []model.PromotionCondition{{
				OrderConditionField: model.OrderConditionField{
					MinTotalPrice: &minTotalPrice,
				},
			}},
		}
	}
	return conditions
}

// isPromotionMinPriceGTDiscount checks if promotion has min total price condition greater than discount + additional value
func isPromotionMinPriceGTDiscount(promotion *model.Promotion, discount int) bool {
	if promotion == nil || promotion.AndConditions == nil {
		return false
	}

	for _, andCond := range promotion.AndConditions {
		if andCond.OrConditions != nil && len(andCond.OrConditions) > 0 {
			for _, orCond := range andCond.OrConditions {
				if orCond.OrderConditionField.MinTotalPrice != nil {
					minTotalPrice := *orCond.OrderConditionField.MinTotalPrice
					if minTotalPrice-discount > ADDITIONAL_PRICE {
						return true
					}
				}
			}
		}
	}

	return false
}

func ExecuteUserVoucherCreateJob(item *job.JobItem) error {
	data, err := bson.Marshal(item.Data)
	if err != nil {
		return err
	}
	var userVoucherJob model.UserVoucherJob
	err = bson.Unmarshal(data, &userVoucherJob)
	if err != nil {
		return err
	} else {
		resp := CreateUserVoucher(userVoucherJob.Account, userVoucherJob.UserVoucher)
		if resp == nil || resp.Status != common.APIStatus.Ok {
			return fmt.Errorf("could not create user voucher for customer: %d", userVoucherJob.UserVoucher.CustomerID)
		}
		model.RewardVoucherUpdationJob.Push(userVoucherJob, &job.JobItemMetadata{
			Topic: "default",
		})
	}

	defer func() {
		if r := recover(); r != nil {
			err := fmt.Errorf("panic CreateUserVoucher : %s", string(debug.Stack()))
			fmt.Println(err.Error())
		}
	}()

	return nil
}

func ExecuteRewardVoucherUpdationJob(item *job.JobItem) error {
	data, err := bson.Marshal(item.Data)
	if err != nil {
		return err
	}
	var userVoucherJob model.UserVoucherJob
	err = bson.Unmarshal(data, &userVoucherJob)
	if err != nil {
		return err
	} else {
		resp := updateGamificationResult(userVoucherJob)
		if resp == nil || resp.Status != common.APIStatus.Ok {
			return fmt.Errorf("could not update gamification result with id: %d", userVoucherJob.GamiResultID)
		}

		if userVoucherJob.CompletedTotal == userVoucherJob.CurCount {
			notifyGamificationResult(userVoucherJob)
		}
	}

	defer func() {
		if r := recover(); r != nil {
			err := fmt.Errorf("panic updateGamificationResult : %s", string(debug.Stack()))
			fmt.Println(err.Error())
		}
	}()

	return nil
}

func notifyGamificationResult(uvj model.UserVoucherJob) {
	_ = client.Services.Notification.CreateNotification(&model.Notification{
		Username:     uvj.Account.Username,
		UserID:       uvj.Account.AccountID,
		ReceiverType: utils.ParseStringToPointer("EMPLOYEE"),
		Topic:        "ANNOUNCEMENT",
		Title: fmt.Sprintf(
			"Rewards have been successfully distributed to %d, please check again.",
			uvj.CompletedResultsCount,
		),
		Link: fmt.Sprint(
			`/marketing/gamification/detail?code=`, uvj.GamiCode, `&q=%7B"rewardStatus"%3A"COMPLETED"%7D`,
		),
	})
}

func updateGamificationResult(uvj model.UserVoucherJob) *common.APIResponse {
	// update gamification result
	query := &model.GamificationResult{
		GamificationResultID: uvj.GamiResultID,
	}
	update := bson.M{
		"$push": bson.M{
			"created_reward": bson.M{
				"voucher_code": uvj.UserVoucher.VoucherCode,
			},
		},
		"$set": bson.M{
			"reward_status": enum.RewardProgress.COMPLETED,
			"updated_by":    uvj.Account.AccountID,
		},
	}

	return model.GamificationResultDB.UpdateOneWithOption(query, update)
}
