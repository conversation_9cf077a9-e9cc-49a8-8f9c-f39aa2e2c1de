package action

import (
	"fmt"
	"strings"
	"time"

	"gitlab.com/buymed.th/marketplace/promotion/model"
	"gitlab.com/buymed.th/marketplace/promotion/model/enum"
)

type result struct {
	cart           *model.Cart                                     // cart info
	cartItemMap    map[string]model.CartItemInternal               // data map cart item
	orderSummation *model.SummationOrderInfo                       // order info
	promoCondition map[enum.ConditionTypeValue]model.PromotionType // condition
	customer       *model.Customer                                 // customer info
	message        string                                          // message lỗi
	errorCode      enum.VoucherErrorCodeType                       // error code
	isValid        bool                                            // voucher is valid ?
	operator       string                                          // AND || OR
	rewardCount    int                                             // số phần thưởng được cộng dồn

	applyDiscount *model.ApplyDiscountOptions
	organization  enum.PromotionOrganizerValue // tổ chức tổ chức chương trình
	sellerCodes   []string                     // danh sách mã seller
}

type condition interface {
	execute(*result)
	validate(*result, []model.PromotionCondition, string)
	setNext(condition)
	getRewardCount(*result, []model.PromotionCondition, string)
}

// order
type orderCondition struct {
	next condition
}

func (o *orderCondition) execute(r *result) {
	defer func() {
		if r.operator == "AND" {
			if o.next != nil && r.isValid {
				r.message = ""
				o.next.execute(r)
			}
			if o.next != nil && r.message == "" {
				o.next.execute(r)
			}
		} else {
			if r.isValid {
				r.message = ""
				return
			} else if o.next != nil {
				o.next.execute(r)
			}
		}
	}()
	if data, ok := r.promoCondition[enum.ConditionType.ORDER_VALUE]; ok {
		if len(data.AndConditions) > 0 {
			o.validate(r, data.AndConditions, enum.Operator.AND)
			if !r.isValid {
				return
			}
			if r.isValid {
				o.getRewardCount(r, data.AndConditions, enum.Operator.AND)
			}
		}
		if len(data.OrConditions) > 0 {
			o.validate(r, data.OrConditions, enum.Operator.OR)
			if r.isValid {
				o.getRewardCount(r, data.OrConditions, enum.Operator.OR)
				return
			}
		}
	}
}

func (o *orderCondition) validate(r *result, conditions []model.PromotionCondition, operator string) {
	for _, c := range conditions {
		r.isValid = true
		if c.OrderConditionField.MinTotalPrice != nil && *c.OrderConditionField.MinTotalPrice > int(r.cart.Price) {
			r.message = "NOT_ENOUGH_ORDER_VALUE"
			// r.message = fmt.Sprintf("Mua thêm %sđ để sử dụng mã", utils.FormatVNDCurrency(fmt.Sprint(*c.OrderConditionField.MinTotalPrice-int(r.cart.Price))))
			r.errorCode = enum.VoucherErrorCode.NOT_ENOUGH_ORDER_VALUE
			r.isValid = false
			if operator == enum.Operator.AND {
				return
			} else {
				continue
			}
		}
		if !isNilOrDefaultValue(c.OrderConditionField.MinSkuQuantity) && *c.OrderConditionField.MinSkuQuantity > r.cart.TotalItem {
			r.message = "NOT_ENOUGH_ORDER_VALUE"
			// r.message = "Mua thêm " + fmt.Sprintf("%d", *c.OrderConditionField.MinSkuQuantity-r.cart.TotalItem) + " loại sản phẩm để sử dụng mã"
			r.errorCode = enum.VoucherErrorCode.NOT_ENOUGH_SKU_QUANTITY
			r.isValid = false
			if operator == enum.Operator.AND {
				return
			} else {
				continue
			}
		}

		if operator == enum.Operator.OR && r.isValid {
			return
		}
	}
}

func (o *orderCondition) getRewardCount(r *result, conditions []model.PromotionCondition, operator string) {
	var newRewardCountAnd int
	var newRewardCountOr int
	for _, c := range conditions {
		if c.OrderConditionField.MinTotalPrice != nil && *c.OrderConditionField.MinTotalPrice != 0 {
			newRewardCount := int(r.cart.Price) / *c.OrderConditionField.MinTotalPrice
			if operator == enum.Operator.AND {
				if newRewardCount < newRewardCountAnd || newRewardCountAnd == 0 {
					newRewardCountAnd = newRewardCount
				}
			} else if c.OrderConditionField.MinTotalPrice != nil && !(*c.OrderConditionField.MinTotalPrice > int(r.cart.Price)) {
				if newRewardCount > newRewardCountOr || newRewardCountOr == 0 {
					newRewardCountOr = newRewardCount
				}
			}
		}
		if !isNilOrDefaultValue(c.OrderConditionField.MinSkuQuantity) && *c.OrderConditionField.MinSkuQuantity != 0 {
			newRewardCount := r.cart.TotalItem / *c.OrderConditionField.MinSkuQuantity
			if operator == enum.Operator.AND {
				if newRewardCount < newRewardCountAnd || newRewardCountAnd == 0 {
					newRewardCountAnd = newRewardCount
				}
			} else if !(*c.OrderConditionField.MinSkuQuantity > r.cart.TotalItem) {
				if newRewardCount > newRewardCountOr || newRewardCountOr == 0 {
					newRewardCountOr = newRewardCount
				}
			}
		}
	}
	var newRewardCount int
	newRewardCount = newRewardCountOr
	if newRewardCountAnd != 0 && newRewardCountAnd < newRewardCountOr || newRewardCount == 0 {
		newRewardCount = newRewardCountAnd
	}
	if r.rewardCount == 0 || newRewardCount < r.rewardCount {
		r.rewardCount = newRewardCount
	}
}

func (o *orderCondition) setNext(c condition) {
	o.next = c
}

// customer
type customerCondition struct {
	next condition
}

func (c *customerCondition) validate(r *result, conditions []model.PromotionCondition, operator string) {

	for _, c := range conditions {
		r.isValid = true
		if c.CustomerConditionField.MaxOrderCount != nil && *c.CustomerConditionField.MaxOrderCount < r.customer.OrderCount {
			r.isValid = false
			r.message = "NUMBER_OF_ORDER_EXCEED_LIMIT"
			// r.message = "Mã giảm giá này hiện không hoạt động"
			if operator == enum.Operator.AND {
				return
			} else {
				continue
			}
		}

		if c.CustomerConditionField.MinOrderCount != nil && *c.CustomerConditionField.MinOrderCount > r.customer.OrderCount {
			r.isValid = false
			r.message = "NOT_ENOUGH_NUMBER_OF_ORDER"
			// r.message = "Mua thêm " + fmt.Sprintf("%d", *c.CustomerConditionField.MinOrderCount-r.customer.OrderCount) + " đơn hàng nữa để sử dụng mã"
			if operator == enum.Operator.AND {
				return
			} else {
				continue
			}
		}

		if c.CustomerConditionField.IndexOrder != nil && *c.CustomerConditionField.IndexOrder != r.customer.OrderCount+1 {
			r.isValid = false
			r.message = "NOT_ENOUGH_NUMBER_OF_ORDER"
			// r.message = "Mua thêm " + fmt.Sprintf("%d", *c.CustomerConditionField.IndexOrder-r.customer.OrderCount-1) + " đơn hàng để sử dụng mã"
			if operator == enum.Operator.AND {
				return
			} else {
				continue
			}
		}

		if c.CustomerConditionField.MinDayNoOrder != nil {
			now := time.Now()
			lastOrderTime := now
			if r.customer.LastOrderTime != nil {
				lastOrderTime = *r.customer.LastOrderTime
			} else if r.customer.ConfirmedTime != nil {
				lastOrderTime = *r.customer.ConfirmedTime
			} else if r.customer.CreatedTime != nil {
				lastOrderTime = *r.customer.CreatedTime
			}
			lastOrderTime = lastOrderTime.Add(time.Duration(*c.CustomerConditionField.MinDayNoOrder) * 24 * time.Hour)
			if now.Before(lastOrderTime) {
				r.isValid = false
				// r.message = "Bạn không đủ điều kiện sử dụng mã"
				r.message = "NOT_ENOUGH_NO_ORDER_TIME"
				if operator == enum.Operator.AND {
					return
				} else {
					continue
				}
			}
		}

		if operator == enum.Operator.OR && r.isValid {
			return
		}
	}
}

func (c *customerCondition) execute(r *result) {
	defer func() {
		if r.operator == "AND" {
			if c.next != nil && r.isValid {
				r.message = ""
				c.next.execute(r)
			}
			if c.next != nil && r.message == "" {
				c.next.execute(r)
			}
		} else {
			if r.isValid {
				r.message = ""
				return
			} else if c.next != nil {
				c.next.execute(r)
			}
		}
	}()
	if data, ok := r.promoCondition[enum.ConditionType.CUSTOMER_HISTORY]; ok {
		// todo check and conditions
		if len(data.AndConditions) > 0 {
			c.validate(r, data.AndConditions, enum.Operator.AND)
			if !r.isValid {
				return
			}
		}
		if len(data.OrConditions) > 0 {
			c.validate(r, data.OrConditions, enum.Operator.OR)
			if r.isValid {
				return
			}
		}
	}
}

func (c *customerCondition) setNext(c2 condition) {
	c.next = c2
}

func (c *customerCondition) getRewardCount(r *result, conditions []model.PromotionCondition, operator string) {
	// no action
}

// product
type productCondition struct {
	next condition
}

func (p *productCondition) validate(r *result, conditions []model.PromotionCondition, operator string) {
	for _, c := range conditions {
		r.isValid = true
		keyValidate := fmt.Sprintf("%s_%s", "SELLER_CODE", c.ProductConditionField.ProductCode)
		if c.ProductConditionField.SellerCode != nil {
			keyValidate = strings.ReplaceAll(keyValidate, "SELLER_CODE", *c.ProductConditionField.SellerCode)
		}

		if _, ok := r.cart.ProductValue[keyValidate]; !ok {
			r.isValid = false
			r.message = "INVALID_PRODUCT"
			// r.message = "Mua thêm sản phẩm theo điều kiện"
			// if c.ProductConditionField.MinQuantity != nil {
			// 	r.message = fmt.Sprintf("Mua thêm %d sản phẩm theo điều kiện", *c.ProductConditionField.MinQuantity)
			// }
			if operator == enum.Operator.AND {
				return
			} else {
				continue
			}
		}
		item := r.cart.ProductValue[keyValidate]
		if c.ProductConditionField.MinTotalPrice != nil && int(item.Total) < *c.ProductConditionField.MinTotalPrice {
			r.isValid = false
			r.message = "NOT_ENOUGH_PRODUCT_AMOUNT"
			// r.message = fmt.Sprintf("Mua thêm %sđ cho sản phẩm theo điều kiện", utils.FormatVNDCurrency(fmt.Sprint(*c.ProductConditionField.MinTotalPrice-int(item.Total))))
			if operator == enum.Operator.AND {
				return
			} else {
				continue
			}
		}
		if c.ProductConditionField.MinQuantity != nil && int(item.Quantity) < *c.ProductConditionField.MinQuantity {
			r.isValid = false
			r.message = "NOT_ENOUGH_PRODUCT_QUANTITY"
			// r.message = fmt.Sprintf("Mua thêm %d sản phẩm theo điều kiện", *c.ProductConditionField.MinQuantity-int(item.Quantity))
			if operator == enum.Operator.AND {
				return
			} else {
				continue
			}
		}

		if operator == enum.Operator.OR && r.isValid {
			return
		}
	}
}

func (p *productCondition) execute(r *result) {
	defer func() {
		if r.operator == "AND" {
			if p.next != nil && r.isValid {
				r.message = ""
				p.next.execute(r)
			}
			if p.next != nil && r.message == "" {
				p.next.execute(r)
			}
		} else {
			if r.isValid {
				r.message = ""
				return
			} else if p.next != nil {
				p.next.execute(r)
			}
		}
	}()
	if data, ok := r.promoCondition[enum.ConditionType.PRODUCT]; ok && r.cart != nil {
		// todo map product
		if r.cart.ProductValue == nil {
			makeMapItem(r.cart, r.cart.CartItems)
		}
		if len(data.AndConditions) > 0 {
			p.validate(r, data.AndConditions, enum.Operator.AND)
			if !r.isValid {
				return
			}
			if r.isValid {
				p.getRewardCount(r, data.AndConditions, enum.Operator.AND)
			}
		}
		if len(data.OrConditions) > 0 {
			p.validate(r, data.OrConditions, enum.Operator.OR)
			if r.isValid {
				p.getRewardCount(r, data.OrConditions, enum.Operator.OR)
				return
			}
		}
	}
}

func (p *productCondition) getRewardCount(r *result, conditions []model.PromotionCondition, operator string) {
	var newRewardCountAnd int
	var newRewardCountOr int
	for _, c := range conditions {
		keyValidate := fmt.Sprintf("%s_%s", "SELLER_CODE", c.ProductConditionField.ProductCode)
		if c.ProductConditionField.SellerCode != nil {
			keyValidate = strings.ReplaceAll(keyValidate, "SELLER_CODE", *c.ProductConditionField.SellerCode)
		}
		item := r.cart.ProductValue[keyValidate]
		if c.ProductConditionField.MinQuantity != nil && *c.ProductConditionField.MinQuantity != 0 {
			newRewardCount := int(item.Quantity) / *c.ProductConditionField.MinQuantity
			if operator == enum.Operator.AND {
				if newRewardCount < newRewardCountAnd || newRewardCountAnd == 0 {
					newRewardCountAnd = newRewardCount
				}
			} else if c.ProductConditionField.MinQuantity != nil && !(int(item.Quantity) < *c.ProductConditionField.MinQuantity) {
				if newRewardCount > newRewardCountOr || newRewardCountOr == 0 {
					newRewardCountOr = newRewardCount
				}
			}
		}

		if c.ProductConditionField.MinTotalPrice != nil && *c.ProductConditionField.MinTotalPrice != 0 {
			newRewardCount := int(item.Total) / *c.ProductConditionField.MinTotalPrice
			if operator == enum.Operator.AND {
				if newRewardCount < newRewardCountAnd || newRewardCountAnd == 0 {
					newRewardCountAnd = newRewardCount
				}
			} else if c.ProductConditionField.MinTotalPrice != nil && !(int(item.Total) < *c.ProductConditionField.MinTotalPrice) {
				if newRewardCount > newRewardCountOr || newRewardCountOr == 0 {
					newRewardCountOr = newRewardCount
				}
			}
		}

	}
	var newRewardCount int
	newRewardCount = newRewardCountOr
	if newRewardCountAnd != 0 && newRewardCountAnd < newRewardCountOr || newRewardCount == 0 {
		newRewardCount = newRewardCountAnd
	}
	if r.rewardCount == 0 || newRewardCount < r.rewardCount {
		r.rewardCount = newRewardCount
	}
}

func (p *productCondition) setNext(c condition) {
	p.next = c
}

type productTagCondition struct {
	next condition
}

func (p *productTagCondition) validate(r *result, conditions []model.PromotionCondition, operator string) {
	for _, c := range conditions {
		r.isValid = true
		keyValidate := fmt.Sprintf("%s_%s", "SELLER_CODE", c.ProductTagConditionField.TagCode)
		if c.ProductTagConditionField.SellerCode != nil {
			keyValidate = strings.ReplaceAll(keyValidate, "SELLER_CODE", *c.ProductTagConditionField.SellerCode)
		}

		if _, ok := r.cart.TagValue[keyValidate]; !ok {
			r.isValid = false
			// r.message = "Mua thêm sản phẩm theo điều kiện"
			r.message = "INVALID_PRODUCT"
			// if c.ProductTagConditionField.MinQuantity != nil {
			// 	r.message = fmt.Sprintf("Mua thêm %d sản phẩm theo điều kiện", *c.ProductTagConditionField.MinQuantity)
			// }
			if operator == enum.Operator.AND {
				return
			} else {
				continue
			}
		}
		item := r.cart.TagValue[keyValidate]
		if c.ProductTagConditionField.MinTotalPrice != nil && int(item.Total) < *c.ProductTagConditionField.MinTotalPrice {
			r.isValid = false
			r.message = "NOT_ENOUGH_PRODUCT_AMOUNT"
			// r.message = fmt.Sprintf("Mua thêm %sđ sản phẩm theo điều kiện", utils.FormatVNDCurrency(fmt.Sprint(*c.ProductTagConditionField.MinTotalPrice-int(item.Total))))
			if operator == enum.Operator.AND {
				return
			} else {
				continue
			}
		}
		if c.ProductTagConditionField.MinQuantity != nil && int(item.Quantity) < *c.ProductTagConditionField.MinQuantity {
			r.isValid = false
			r.message = "NOT_ENOUGH_PRODUCT_QUANTITY"
			// r.message = fmt.Sprintf("Mua thêm %d sản phẩm theo điều kiện", *c.ProductTagConditionField.MinQuantity-int(item.Quantity))
			if operator == enum.Operator.AND {
				return
			} else {
				continue
			}
		}

		// check dk sku
		if c.ProductTagConditionField.MinProductCount != nil && int(item.Products) < *c.ProductTagConditionField.MinProductCount {
			r.isValid = false
			r.message = "NOT_ENOUGH_PRODUCT_COUNT"
			if operator == enum.Operator.AND {
				return
			} else {
				continue
			}
		}

		if operator == enum.Operator.OR && r.isValid {
			return
		}
	}
}

func (p *productTagCondition) execute(r *result) {
	defer func() {
		if r.operator == "AND" {
			if p.next != nil && r.isValid {
				r.message = ""
				p.next.execute(r)
			}
			if p.next != nil && r.message == "" {
				p.next.execute(r)
			}
		} else {
			if r.isValid {
				r.message = ""
				return
			} else if p.next != nil {
				p.next.execute(r)
			}
		}
	}()
	if data, ok := r.promoCondition[enum.ConditionType.PRODUCT_TAG]; ok && r.cart != nil {
		// todo map product
		if r.cart.TagValue == nil {
			makeMapItem(r.cart, r.cart.CartItems)
		}
		if len(data.AndConditions) > 0 {
			p.validate(r, data.AndConditions, enum.Operator.AND)
			if !r.isValid {
				return
			}
			if r.isValid {
				p.getRewardCount(r, data.AndConditions, enum.Operator.AND)
			}
		}
		if len(data.OrConditions) > 0 {
			p.validate(r, data.OrConditions, enum.Operator.OR)
			if r.isValid {
				p.getRewardCount(r, data.OrConditions, enum.Operator.OR)
				return
			}
		}
	}
}

func (p *productTagCondition) getRewardCount(r *result, conditions []model.PromotionCondition, operator string) {
	var newRewardCountAnd int
	var newRewardCountOr int
	for _, c := range conditions {
		keyValidate := fmt.Sprintf("%s_%s", "SELLER_CODE", c.ProductTagConditionField.TagCode)
		if c.ProductTagConditionField.SellerCode != nil {
			keyValidate = strings.ReplaceAll(keyValidate, "SELLER_CODE", *c.ProductTagConditionField.SellerCode)
		}
		item := r.cart.TagValue[keyValidate]
		if c.ProductTagConditionField.MinQuantity != nil && *c.ProductTagConditionField.MinQuantity != 0 {
			newRewardCount := int(item.Quantity) / *c.ProductTagConditionField.MinQuantity
			if operator == enum.Operator.AND {
				if newRewardCount < newRewardCountAnd || newRewardCountAnd == 0 {
					newRewardCountAnd = newRewardCount
				}
			} else if c.ProductTagConditionField.MinQuantity != nil && !(int(item.Quantity) < *c.ProductTagConditionField.MinQuantity) {
				if newRewardCount > newRewardCountOr || newRewardCountOr == 0 {
					newRewardCountOr = newRewardCount
				}
			}
		}

		if c.ProductTagConditionField.MinTotalPrice != nil && *c.ProductTagConditionField.MinTotalPrice != 0 {
			newRewardCount := int(item.Total) / *c.ProductTagConditionField.MinTotalPrice
			if operator == enum.Operator.AND {
				if newRewardCount < newRewardCountAnd || newRewardCountAnd == 0 {
					newRewardCountAnd = newRewardCount
				}
			} else if c.ProductTagConditionField.MinTotalPrice != nil && !(int(item.Total) < *c.ProductTagConditionField.MinTotalPrice) {
				if newRewardCount > newRewardCountOr || newRewardCountOr == 0 {
					newRewardCountOr = newRewardCount
				}
			}
		}

	}
	var newRewardCount int
	newRewardCount = newRewardCountOr
	if newRewardCountAnd != 0 && newRewardCountAnd < newRewardCountOr || newRewardCount == 0 {
		newRewardCount = newRewardCountAnd
	}
	if r.rewardCount == 0 || newRewardCount < r.rewardCount {
		r.rewardCount = newRewardCount
	}
}

func (p *productTagCondition) setNext(c condition) {
	p.next = c
}

type productBlackListCondition struct {
	next condition
}

func (p *productBlackListCondition) execute(r *result) {
	defer func() {
		if r.operator == "AND" {
			if p.next != nil && r.isValid {
				r.message = ""
				p.next.execute(r)
			}
			if p.next != nil && r.message == "" {
				p.next.execute(r)
			}
		} else {
			if r.isValid {
				r.message = ""
				return
			} else if p.next != nil {
				p.next.execute(r)
			}
		}
	}()
	if data, ok := r.promoCondition[enum.ConditionType.PRODUCT_BLACKLIST]; ok && r.cart != nil {
		// todo map product
		if r.cart.ProductValue == nil {
			makeMapItem(r.cart, r.cart.CartItems)
		}
		if len(data.AndConditions) > 0 {
			p.validate(r, data.AndConditions, enum.Operator.AND)
			if !r.isValid {
				return
			}
			if r.isValid {
				p.getRewardCount(r, data.AndConditions, enum.Operator.AND)
			}
		}
	}
}

func (p *productBlackListCondition) validate(r *result, conditions []model.PromotionCondition, operator string) {
	for _, c := range conditions {
		r.isValid = true
		keyValidate := fmt.Sprintf("%s_%s", "SELLER_CODE", c.ProductBlackListConditionField.ProductCode)
		if c.ProductBlackListConditionField.SellerCode != nil {
			keyValidate = strings.ReplaceAll(keyValidate, "SELLER_CODE", *c.ProductBlackListConditionField.SellerCode)
		}

		if _, ok := r.cart.ProductValue[keyValidate]; ok {
			r.isValid = false
			r.message = "Không áp dụng khi mua sản phẩm " + c.ProductBlackListConditionField.ProductName
			if operator == enum.Operator.AND {
				return
			} else {
				continue
			}
		}

		if operator == enum.Operator.OR && r.isValid {
			return
		}
	}
}

func (p *productBlackListCondition) setNext(c condition) {
	p.next = c
}

func (p *productBlackListCondition) getRewardCount(r *result, conditions []model.PromotionCondition, s string) {
	// do not need to implement for this condition
}

func makeMapItem(cart *model.Cart, items []model.CartItemInternal) {
	cart.TagValue = make(map[string]struct {
		Quantity int64
		Total    float64
		Products int64
	})
	cart.ProductValue = make(map[string]struct {
		Quantity int64
		Total    float64
	})
	cart.SkuValue = make(map[string]struct {
		Quantity int64
		Total    float64
	})

	// convert int to float64

	for _, item := range items {
		key1 := fmt.Sprintf("%s_%s", item.SellerCode, item.ProductCode)
		key2 := fmt.Sprintf("%s_%s", "SELLER_CODE", item.ProductCode)
		skuKey := item.ProductSKU
		if data, ok := cart.ProductValue[key1]; ok {
			data.Total = float64(data.Total + item.Total)
			data.Quantity = data.Quantity + item.Quantity
			cart.ProductValue[key1] = data

		} else {
			cart.ProductValue[key1] = struct {
				Quantity int64
				Total    float64
			}{Quantity: item.Quantity, Total: float64(item.Total)}
		}
		if data, ok := cart.SkuValue[skuKey]; ok {
			data.Total = data.Total + item.Total
			data.Quantity = data.Quantity + item.Quantity
			cart.SkuValue[skuKey] = data
		} else {
			cart.SkuValue[skuKey] = struct {
				Quantity int64
				Total    float64
			}{Quantity: item.Quantity, Total: float64(item.Total)}
		}
		if data, ok := cart.ProductValue[key2]; ok {
			data.Total = data.Total + item.Total
			data.Quantity = data.Quantity + item.Quantity
			cart.ProductValue[key2] = data

		} else {
			cart.ProductValue[key2] = struct {
				Quantity int64
				Total    float64
			}{Quantity: item.Quantity, Total: item.Total}
		}
		for _, tag := range item.ProductTags {
			keyTag1 := fmt.Sprintf("%s_%s", item.SellerCode, tag)
			keyTag2 := fmt.Sprintf("%s_%s", "SELLER_CODE", tag)
			if data, ok := cart.TagValue[keyTag1]; ok {
				data.Total = data.Total + item.Total
				data.Quantity = data.Quantity + item.Quantity
				data.Products = data.Products + 1
				cart.TagValue[keyTag1] = data
			} else {
				cart.TagValue[keyTag1] = struct {
					Quantity int64
					Total    float64
					Products int64
				}{Quantity: item.Quantity, Total: item.Total, Products: 1}
			}
			if data, ok := cart.TagValue[keyTag2]; ok {
				data.Total = data.Total + item.Total
				data.Quantity = data.Quantity + item.Quantity
				data.Products = data.Products + 1
				cart.TagValue[keyTag2] = data
			} else {
				cart.TagValue[keyTag2] = struct {
					Quantity int64
					Total    float64
					Products int64
				}{Quantity: item.Quantity, Total: item.Total, Products: 1}
			}
		}
	}
}

func Run(in result) (bool, string) {
	customerCondition := &customerCondition{}
	productCondition := &productCondition{}
	orderCondition := &orderCondition{}
	productTagCondition := &productTagCondition{}

	productTagCondition.setNext(customerCondition)
	productCondition.setNext(productTagCondition)
	orderCondition.setNext(productCondition)
	orderCondition.execute(&in)
	return in.isValid, in.message
}
